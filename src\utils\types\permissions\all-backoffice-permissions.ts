export enum BACKOFFICEPERMISSIONS {
  VIEW = "backoffice_view",
  CREATE = "backoffice_create",
  EDIT = "backoffice_edit",
  DELETE = "backoffice_delete",
}

export enum BACKOFFICEDASHBOARDPERMISSIONS {
  VIEW = "backoffice_dashboard_view",
}

export enum BACKOFFICEACCOUNTPERMISSIONS {
  VIEW = "backoffice_account_view",
  EDIT = "backoffice_account_edit",
}

export enum BACKOFFICEPLANSPERMISSIONS {
  VIEW = "backoffice_plans_view",
  CREATE = "backoffice_plans_create",
  EDIT = "backoffice_plans_edit",
  DELETE = "backoffice_plans_delete",
}

export enum BACKOFFICEUSERPERMISSIONS {
  VIEW = "backoffice_user_view",
  CREATE = "backoffice_user_create",
  EDIT = "backoffice_user_edit",
  DELETE = "backoffice_user_delete",
}