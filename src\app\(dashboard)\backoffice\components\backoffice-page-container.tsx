"use client";
import { Flex, FlexProps } from "@chakra-ui/react";
import React from "react";

interface BackofficePageContainerProps extends FlexProps {
  children: React.ReactNode;
}

export default function BackofficePageContainer({
  children,
  ...rest
}: BackofficePageContainerProps) {
  return (
    <Flex 
      flex={1} 
      w="100%" 
      flexDirection="column" 
      overflow={"hidden"}
      pt={{base: "12rem", md: 5}} 
      px={5} 
      pb={5}
      {...rest}
    >
      <Flex
        flex={1}
        p={{ base: 4, md: 6 }}
        borderRadius={{ base: 0, md: 15 }}
        bgColor="backgroundCard"
        flexDirection="column"
        overflow="hidden"
      >
        {children}
      </Flex>
    </Flex>
  );
}
