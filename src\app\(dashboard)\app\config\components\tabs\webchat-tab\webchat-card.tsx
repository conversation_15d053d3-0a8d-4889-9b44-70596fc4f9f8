import { NavLink } from "@/components/global/navlink/navlink";
import { Switch } from "@/components/ui/switch";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import truncateText from "@/utils/funcs/truncate-text";
import { Box, VStack, Text, Flex, HStack, useBreakpointValue } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { useEffect, useState } from "react";
import { LuMessageSquareCode } from "react-icons/lu";

type WebChatCardProps = {
  name: string;
  description: string | null;
  secureId: string;
  active: boolean;
};

export default function WebChatCard({
  description,
  name,
  secureId,
  active,
}: WebChatCardProps) {
  const [isActive, setIsActive] = useState<boolean | undefined>();

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  useEffect(() => {
    setIsActive(active);
  }, [active]);

  const handleEdit = () => {
    changeStatusAttendant.mutateAsync();
  };

  const changeStatusAttendant = useMutation({
    mutationFn: async () => {
      await api.put(`/chats/${secureId}`, {
        isActive: isActive,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: "success",
        description: `WebChat ${!isActive ? "desativado" : "ativado"} com sucesso`,
        title: `WebChat ${!isActive ? "desativado" : "ativado"}`,
      });
    },
  });

  return (
    <>
      {isMobile ? (
        // Mobile Layout - Linear/Row Format
        <Box w={"100%"} position={"relative"}>
          {/* <Link href={`/app/webchat/edit/${secureId}`}> */}
          <Flex
            bgColor={"chatBackground"}
            w={"100%"}
            p={4}
            rounded={"2xl"}
            borderWidth={1}
            borderColor={"transparent"}
            position={"relative"}
            alignItems={"center"}
            justifyContent={"space-between"}
            gap={3}
            opacity={isActive ? 1 : 0.5}
            _hover={{
              bgColor: "chatCardBackground",
              cursor: "pointer",
              transition: "0.3s",
              borderColor: "chatPrimary",
            }}
          // _active={{
          //   transform: "scale(0.95)",
          //   transition: "0.2s",
          // }}
          >
            <HStack gap={3} flex={1}>
              <LuMessageSquareCode size={"24"} color="#FD2264" />
              <VStack alignItems={"flex-start"} gap={1}>
                <Text color={"chatTextColor"} fontSize={"sm"} fontWeight={"medium"}>
                  {name}
                </Text>
                <Text color={"chatTextColor"} fontSize={"xs"}>
                  {description ? truncateText(description, 40) : "Sem descrição"}
                </Text>
              </VStack>
            </HStack>
          </Flex>
          {/* </Link> */}
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      ) : (
        // Desktop Layout - Card Format
        <Box w={"20%"} position={"relative"}>
          <Link href={`/app/webchat/edit/${secureId}`}>
            <Box
              bgColor={"chatBackground"}
              opacity={isActive ? 1 : 0.5}
              h={"150px"}
              p={4}
              rounded={"2xl"}
              borderWidth={1}
              borderColor={"transparent"}
              _hover={{
                bgColor: "chatCardBackground",
                cursor: "pointer",
                transition: "0.3s",
                borderColor: "chatPrimary",
              }}
              _active={{
                transform: "scale(0.95)",
                transition: "0.2s",
              }}
              alignContent={"center"}
            >
              <VStack gap={2}>
                <LuMessageSquareCode size={"30"} color="#FD2264" />
                <Text color={"chatTextColor"} fontWeight={"medium"}>
                  {name}
                </Text>
                <Text color={"chatTextColor"} fontWeight={"medium"} fontSize={"xs"}>
                  {description ? truncateText(description, 26) : "Sem descrição"}
                </Text>
              </VStack>
            </Box>
          </Link>
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      )}
    </>
  );
}
