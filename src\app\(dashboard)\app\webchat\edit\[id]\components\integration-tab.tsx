"use client";
import {
  <PERSON>lex,
  Grid,
  GridItem,
  VStack,
  Box,
  useClipboard,
  Button,
  ClipboardRoot,
  Center,
  Spinner,
} from "@chakra-ui/react";
import { Prism, SyntaxHighlighterProps } from "react-syntax-highlighter";
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism";
import { ClipboardButton } from "@/components/ui/clipboard";
import getWidgetScript from "@/utils/funcs/get-widget";
import useFindChat from "@/hook/chats/useFindChat";
import HeaderTab from "@/components/tabs/header";
import { useRouter } from "next/navigation";

type IntegrationTabProps = {
  secureId: string;
};

const SyntaxHighlighter = Prism as any as React.FC<SyntaxHighlighterProps>;

export default function IntegrationTab({ secureId }: IntegrationTabProps) {
  const route = useRouter();
  if (!process.env.NEXT_PUBLIC_DOMAIN) return null;
  if (!secureId) return null;

  const { data, isLoading, isFetching } = useFindChat(secureId);

  if (!data || isLoading || isFetching) {
    return (
      <Flex
        flex={1}
        h={"100%"}
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
      >
        <Center>
          <Spinner color="chatPrimary" />
        </Center>
      </Flex>
    );
  }

  const chatWidgetScript = getWidgetScript({
    websiteUrl: process.env.NEXT_PUBLIC_DOMAIN,
    chatId: data.secureId,
    btnColor: data.webchatButtonBgColor || "#FFF",
    iconUrl: data.upload.urlCdn,
  });

  return (
    <Flex
      h={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      <VStack flex={1} alignItems={"center"} gap={5}>
        <HeaderTab
          title="Integração"
          hrefBack="/app/config#webchats"
        />
        <Box position="relative" flex={1} width={"65vw"}>
          <ClipboardRoot
            position={"absolute"}
            right={10}
            top={10}
            value={chatWidgetScript}
          >
            <ClipboardButton bgColor={"chatPrimary"} />
          </ClipboardRoot>
          <SyntaxHighlighter
            language="javascript"
            style={dracula}
            customStyle={{
              fontSize: "14px",
              borderRadius: "10px",
              padding: "20px",
              maxHeight: "calc(100vh - 200px)",
              // width: "100%",
              overflowY: "auto",
              scrollbarWidth: "thin",
              scrollbarColor: "#D6D6D6 transparent",
            }}
          >
            {chatWidgetScript}
          </SyntaxHighlighter>
        </Box>
        {/* <Grid templateColumns="repeat(5, 1fr)" w={"100%"} h={"100%"}>
          <GridItem colSpan={3}></GridItem>
          <GridItem colSpan={2}>
           
          </GridItem>
        </Grid> */}
      </VStack>
    </Flex>
  );
}
