"use client";
import {
  Center,
  Flex,
  <PERSON>rid,
  <PERSON>rid<PERSON><PERSON>,
  <PERSON><PERSON>,
  VStack,
  Text,
} from "@chakra-ui/react";
import { useState } from "react";
import HeaderTab from "./header-tab";

import Pagination from "@/components/global/pagination/pagination";
import ContactCard from "./contact-card";
import useContact from "@/hook/contacts/useContacts";
import { CreateContactModal } from "./create-contact-modal";

export default function BaseContact() {
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);
  const {
    data: contacts,
    isLoading,
    isFetching,
  } = useContact(page, searchTerm);
  const isContactsLoading = isLoading || isFetching;
  return (
    <>
      <Flex
        flex={1}
        h={{ base: "100%", md: "100%" }}
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
        position="relative"
        overflow="hidden"
        direction="column"
      >
        {(!contacts || isContactsLoading) && !searchTerm ? (
          <Center w={"100%"} h={"100%"}>
            <Spinner color={"chatPrimary"}></Spinner>
          </Center>
        ) : (
          <VStack
            flex={1}
            alignItems={"flex-start"}
            width="100%"
            h="100%"
            maxH="100%"
            overflowY={{ base: "auto", md: "auto" }}
            overflowX="hidden"
            pb={{ base: "80px", md: 0 }}
            px={{ base: 0, md: 0 }}
            gap={3}
            css={{
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-track": {
                background: "transparent",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "rgba(0,0,0,0.2)",
                borderRadius: "3px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                background: "rgba(0,0,0,0.3)",
              },
            }}
          >
            <HeaderTab
              title="Contatos"
              onSearchChange={setSearchTerm}
              openCreateModal={() => setOpen(true)}
            />
            <Grid
              templateColumns={{ base: "1fr", md: `repeat(6, 1fr)` }}
              gap={4}
              width="100%"
              px={5}
              py={3}
              color={"chatPrimary"}
              rounded={"xl"}
              fontWeight="bold"
              display={{ base: "none", md: "grid" }}
            >
              <GridItem>
                <Text textAlign={"center"}>Nome</Text>
              </GridItem>
              <GridItem>
                <Text textAlign={"center"}>Email</Text>
              </GridItem>
              <GridItem>
                <Text textAlign={"center"}>Celular</Text>
              </GridItem>
              <GridItem>
                <Text textAlign={"center"}>Documento</Text>
              </GridItem>
              {/* <GridItem>
										<Text>Descrição</Text>
									</GridItem> */}
              <GridItem>
                <Text textAlign={"center"}>Ultima Atualização</Text>
              </GridItem>
              <GridItem>
                <Text textAlign={"center"}>Opções</Text>
              </GridItem>
            </Grid>

            <VStack gap={3} width="100%">
              {contacts &&
                contacts.data.length > 0 &&
                contacts.data.map((contact) => (
                  <ContactCard
                    key={contact.secureId}
                    secureId={contact.secureId}
                    customerName={contact.name}
                    customerEmail={contact.email}
                    customerPhone={contact.phone}
                    customerDocument={contact.document}
                    isActive={contact.isActive}
                    lastUpdate={
                      contact.updatedAt
                        ? new Date(contact.updatedAt)
                        : undefined
                    }
                  />
                ))}
            </VStack>
            {contacts && (
              <Pagination
                totalItems={contacts.meta.totalItems}
                itemsPerPage={contacts.meta.itemsPerPage}
                page={page}
                setPage={setPage}
                position={{ base: "fixed", md: "absolute" }}
                bottom={{ base: 4, md: 3 }}
                right={{ base: 4, md: 5 }}
                left={{ base: "auto", md: "auto" }}
                zIndex={10}
                bg={{ base: "rgba(255,255,255,0.95)", md: "transparent" }}
                backdropFilter={{ base: "blur(10px)", md: "none" }}
                borderRadius={{ base: "lg", md: 0 }}
                p={{ base: 2, md: 0 }}
                boxShadow={{ base: "0 4px 6px rgba(0,0,0,0.1)", md: "none" }}
                justifyContent={{ base: "flex-end", md: "flex-end" }}
              />
            )}
          </VStack>
        )}
      </Flex>
      <CreateContactModal open={open} setOpen={setOpen} />
    </>
  );
}
