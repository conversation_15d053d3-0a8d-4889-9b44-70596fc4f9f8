import { VStack, Text, Box, Flex } from "@chakra-ui/react";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";

type NavButtonsProps = {
  icon: React.ReactNode;
  label: string;
  redirect: string;
  active?: boolean;
  showLabel?: boolean;
};

const MotionText = motion.create(Text);

export default function NavButtons({
  icon,
  label,
  redirect,
  active,
  showLabel = true
}: NavButtonsProps) {
  return (
    <Link href={redirect}>
      <Flex
        flexDir={{ base: "row", md: "column" }}
        align={"center"}
        justifyContent="center"
        gap={2}
        w={"100%"}
        h={{ md: "60px" }} // Fixed height for the container
        _hover={{
          cursor: "pointer",
          "& .icon": {
            color: "#FD2264",
          },
        }}
      >
        <Box className="icon" color={active ? "#FD2264" : "#FD226480"}>
          {icon}
        </Box>
        <Box h={{ md: "20px" }} position="relative"> {/* Fixed height container for text */}
          <AnimatePresence>
            {showLabel && (
              <MotionText
                color="chatTextColor"
                fontSize={{ md: "xs", base: "xl" }}
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2 }}
                whiteSpace="nowrap"
                overflow="hidden"
              >
                {label}
              </MotionText>
            )}
          </AnimatePresence>
        </Box>
      </Flex>
    </Link>
  );
}
