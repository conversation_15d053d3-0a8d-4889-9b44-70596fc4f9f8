"use client";
import { Flex, HStack } from "@chakra-ui/react";
import SideBar from "@/components/global/sidebar";
import DrawerMenu from "@/components/global/drawer-menu/drawer-backoffice";
import NavLinks from "./navlinks";
import { useAuthContext } from "@/providers/AuthProvider";

type BackOfficeLayoutProps = {
  children: React.ReactNode;
};

export default function BackOfficeLayout({ children }: BackOfficeLayoutProps) {
  const { user } = useAuthContext();
  if (!user) return null;

  return (
    <Flex w={"100vw"} h={"100vh"} bgColor="#1e1e1e" overflow="hidden">
      <DrawerMenu children={<NavLinks />} name={user.activeAccount.userName} url={`/backoffice/profile`} />
      <SideBar children={<NavLinks />} name={user.activeAccount.userName} url={`/backoffice/profile`} />

      <Flex
        flex={1}
        ml={{ md: "25%", "2xl": "20%" }}
        h="100vh"
        overflow="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#555",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#666",
          },
        }}
      >
        {children}
      </Flex>
    </Flex>
  );
}
