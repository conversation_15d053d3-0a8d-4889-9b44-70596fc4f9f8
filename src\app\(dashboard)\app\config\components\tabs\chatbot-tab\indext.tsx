import useListChatBots from "@/hook/chatbots/useListChatbots";
import { useRouter } from "next/navigation";
import { Center, Flex, HStack, Spinner, VStack, useBreakpointValue } from "@chakra-ui/react";
import HeaderTab from "../header-tab";
import ChatBotCard from "./chatbot-card";

type ChatBotTabProps = {};

export default function ChatBotTab({ }: ChatBotTabProps) {
  const { data, isLoading, isFetching } = useListChatBots();

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  const navigate = useRouter();

  const handleCreateWebChat = () => {
    navigate.push("/app/chatbot/create");
  };

  return (
    <Flex
      h={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      {!data || isLoading || isFetching ? (
        <Center w={"100%"} h={"100%"}>
          <Spinner color={"chatPrimary"}></Spinner>
        </Center>
      ) : (
        <>
          <VStack flex={1} alignItems={"flex-start"} mt={{ base: 8, md: 0 }}>
            <HeaderTab
              title="ChatBots"
              buttonTitle={!isMobile ? "Adicionar ChatBot" : undefined}
              onClick={!isMobile ? handleCreateWebChat : undefined}
            />
            {isMobile ? (
              <VStack
                flex={1}
                w={"100%"}
                alignItems={"stretch"}
                gap={3}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum ChatBot Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data?.data.map((chat) => (
                      <ChatBotCard
                        hasKnowledgeBase={true}
                        isAi={chat.isAI}
                        name={chat.name}
                        active={chat.isActive}
                        secureId={chat.secureId}
                        key={chat.secureId}
                      />
                    ))}
                  </>
                )}
              </VStack>
            ) : (
              <HStack
                flex={1}
                flexWrap={"wrap"}
                w={"100%"}
                alignContent={"start"}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum ChatBot Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data?.data.map((chat) => (
                      <ChatBotCard
                        hasKnowledgeBase={true}
                        isAi={chat.isAI}
                        name={chat.name}
                        active={chat.isActive}
                        secureId={chat.secureId}
                        key={chat.secureId}
                      />
                    ))}
                  </>
                )}
              </HStack>
            )}
          </VStack>
        </>
      )}
    </Flex>
  );
}
