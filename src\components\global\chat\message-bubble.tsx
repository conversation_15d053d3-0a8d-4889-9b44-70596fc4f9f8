import { Avatar } from "@/components/ui/avatar";
import { Box, HStack, VStack, Text, Link, Image, Button, IconButton, Icon } from "@chakra-ui/react";
import { IoCheckmarkDoneOutline, IoCheckmarkOutline, IoDocumentOutline, IoDownloadOutline } from "react-icons/io5";
import { TbCornerDownRight } from "react-icons/tb";
import ReactMarkdown from "react-markdown";
import AudioPlayer from "./audio-player";
import remarkGfm from 'remark-gfm';

const isUrl = (text: string): boolean => {
  try {
    if (!text || typeof text !== 'string') return false;
    if (text.startsWith('/')) return false;
    const urlPattern = new RegExp('^(https?:\\/\\/|ftp:\\/\\/|www\\.)' +
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' +
      '((\\d{1,3}\\.){3}\\d{1,3}))' +
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' +
      '(\\?[;&a-z\\d%_.~+=-]*)?' +
      '(\\#[-a-z\\d_]*)?$', 'i');
    return !!urlPattern.test(text);
  } catch (_) {
    return false;
  }
};

type MessageBubbleProps = {
  message: string;
  name?: string;
  clientName: string;
  isMine: boolean;
  avatarUrl?: string;
  type?: 'text' | 'audio' | 'file' | 'image';
  urlFile?: string;
  messageTime: string;
  messageCount: number;
  secureId: string;
  onReply?: (secureId: string, message: string) => void;
  replyTo?: string;
  replyMessage?: string;
};

export default function MessageBubble({
  message,
  name,
  isMine,
  type = 'text',
  urlFile,
  avatarUrl,
  messageTime,
  clientName,
  messageCount,
  secureId,
  onReply,
  replyTo,
  replyMessage,
}: MessageBubbleProps) {

  const renderMessageContent = () => {
    switch (type) {
      case 'audio':
        return urlFile ? <AudioPlayer url={urlFile} /> : null;
      
      case 'image':
        return (
          <VStack align="start" gap={2} w="100%">
            {urlFile && (
              <Box maxW="300px" maxH="300px" overflow="hidden" borderRadius="md">
                <Image 
                  src={urlFile} 
                  onClick={() => window.open(urlFile, '_blank')}
                  cursor="pointer"
                  alt="Image content" 
                  objectFit="contain" 
                  maxW="100%" 
                  maxH="300px"
                  loading="lazy"
                />
              </Box>
            )}
            {message && (
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  ol: ({ children }) => (
                    <Box as="ol" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  ul: ({ children }) => (
                    <Box as="ul" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  li: ({ children }) => (
                    <Box
                      as="li"
                      display="list-item"
                      listStylePosition={"initial"}
                      my={1}
                      alignItems="center"
                    >
                      {children}
                    </Box>
                  ),
                  p: ({ children }) => (
                    <Text display="inline-flex" flexWrap="wrap" wordBreak="break-word"
              overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                  a: ({ href, children }) => (
                    <Link
                      href={href}
                      color="#FD2264"
                      textDecoration="underline"
                      target="_blank"
                      rel="noopener noreferrer"
                      maxWidth="100%"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Link>
                  ),
                  strong: ({ children }) => (
                    <Text as="strong" fontWeight="bold" display="inline" wordBreak="break-word"
                    overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                  code: ({ children }) => (
                    <Text as="code" 
                      bg="gray.100" 
                      p="1" 
                      rounded="sm" 
                      fontSize="sm" 
                      fontFamily="monospace"
                      display="inline" 
                      wordBreak="break-all"
                      overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                }}
                className="markdown-content"
              >
                {message}
              </ReactMarkdown>
            )}
          </VStack>
        );
      
      case 'file':
        return (
          <VStack align="start" gap={2} w="100%">
            {urlFile && (
              <HStack 
                p={3} 
                bg="gray.50" 
                borderRadius="md" 
                w="100%" 
                gap={3}
                border="1px solid"
                justifyContent={"space-between"}
                alignItems="center"
                borderColor="gray.200"
              >
                <Box color="gray.500" >
                <Icon size="lg" color="ChatPrimary">
                  <IoDocumentOutline />
                  </Icon>
                </Box>
                <VStack align="start" gap={0} flex={1}>
                  <Text fontWeight="medium" fontSize="sm" color="chatTextColor">
                    {'Documento'}
                  </Text>
                </VStack>
                <Link href={urlFile} target="_blank"   download>
                  <IconButton size="xs" 
                  rounded="full"
                  fontWeight="700"
                  bgColor="chatPrimary"
                  color="white"
                  transitionDuration={"0.2s"}
                  _hover={{
                    color: "chatPrimary",
                    bgColor: "transparent",
                    borderColor: "chatPrimary",
                  }}
                  _active={{
                    transform: "scale(0.95)",
                  }}>
                    <Icon size="md">
                    <IoDownloadOutline/>
                  </Icon>
                    </IconButton>
                </Link>
              </HStack>
            )}
            {message && (
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  ol: ({ children }) => (
                    <Box as="ol" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  ul: ({ children }) => (
                    <Box as="ul" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  li: ({ children }) => (
                    <Box
                      as="li"
                      display="list-item"
                      listStylePosition={"initial"}
                      my={1}
                      alignItems="center"
                    >
                      {children}
                    </Box>
                  ),
                  p: ({ children }) => (
                    <Text display="inline-flex" flexWrap="wrap" wordBreak="break-word"
              overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                  a: ({ href, children }) => (
                    <Link
                      href={href}
                      color="#FD2264"
                      textDecoration="underline"
                      target="_blank"
                      rel="noopener noreferrer"
                      maxWidth="100%"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Link>
                  ),
                  strong: ({ children }) => (
                    <Text as="strong" fontWeight="bold" display="inline" wordBreak="break-word"
                    overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                  code: ({ children }) => (
                    <Text as="code" 
                      bg="gray.100" 
                      p="1" 
                      rounded="sm" 
                      fontSize="sm" 
                      fontFamily="monospace"
                      display="inline" 
                      wordBreak="break-all"
                      overflowWrap="break-word">
                      {children}
                    </Text>
                  ),
                }}
                className="markdown-content"
              >
                {message}
              </ReactMarkdown>
            )}
          </VStack>
        );
      
      default: // text
        if (isUrl(message)) {
          return (
            <Link
              href={message}
              color="#FD2264"
              textDecoration="underline"
              target="_blank"
              rel="noopener noreferrer"
              maxWidth="100%"
              wordBreak="break-word"
              overflowWrap="break-word"
            >
              {message}
            </Link>
          );
        }
        return (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              ol: ({ children }) => (
                <Box as="ol" pl={4} my={2}>
                  {children}
                </Box>
              ),
              ul: ({ children }) => (
                <Box as="ul" pl={4} my={2}>
                  {children}
                </Box>
              ),
              li: ({ children }) => (
                <Box
                  as="li"
                  display="list-item"
                  listStylePosition={"initial"}
                  my={1}
                  alignItems="center"
                >
                  {children}
                </Box>
              ),
              p: ({ children }) => (
                <Text 
                  display="inline-flex" 
                  flexWrap="wrap" 
                  wordBreak="break-word"
                  overflowWrap="break-word"
                  whiteSpace="pre-wrap"
                >
                  {children}
                </Text>
              ),
              a: ({ href, children }) => (
                <Link
                  href={href}
                  color="#FD2264"
                  textDecoration="underline"
                  target="_blank"
                  rel="noopener noreferrer"
                  maxWidth="100%"
                  wordBreak="break-word"
                  overflowWrap="break-word"
                >
                  {children}
                </Link>
              ),
              strong: ({ children }) => (
                <Text 
                  as="strong" 
                  fontWeight="bold" 
                  display="inline" 
                  wordBreak="break-word"
                  overflowWrap="break-word"
                >
                  {children}
                </Text>
              ),
              code: ({ children }) => (
                <Text 
                  as="code" 
                  bg="gray.100" 
                  p="1" 
                  rounded="sm" 
                  fontSize="sm" 
                  fontFamily="monospace"
                  display="inline" 
                  wordBreak="break-all"
                  overflowWrap="break-word"
                >
                  {children}
                </Text>
              ),
            }}
            className="markdown-content"
          >
            {message}
          </ReactMarkdown>
        );
    }
  };

  const handleReply = () => {
    if (onReply) {
      onReply(secureId, message);
    }
  };

  const renderReplyMessage = () => {
    if (!replyTo || !replyMessage) return null;
    
    return (
      <Box 
        p={1.5} 
        bg="gray.100" 
        borderRadius="md" 
        borderLeftWidth={2}
        borderLeftColor="chatPrimary"
        mb={2}
        w={"100%"}
        maxHeight="60px"
        overflow="hidden"
        fontSize="xs"
      >
        <Text fontWeight="medium" color="chatPrimary" fontSize="xs" mb={0.5}>
          Mensagem respondida
        </Text>
        <Text  color="gray.600" fontSize="xs">
          {replyMessage}
        </Text>
      </Box>
    );
  };

  return (
    <>
      {isMine ? (
        <HStack w={"100%"} justifyContent={"flex-end"}>
          <HStack
            flexDir={"row-reverse"}
            alignItems={"start"}
            gap={5}
            maxWidth={{'2xl':"80%", base: "70%"}}
            mr={messageCount !== 1 ? 16 : 0}
          >
            <Avatar
              hidden={messageCount !== 1}
              size={"lg"}
              bgColor={'#5e5e5e'}
              name={name}
              src={!!avatarUrl ? avatarUrl : undefined}
            />
            <VStack position={"relative"}>
              {messageCount === 1 && (
                <Box
                  position="absolute"
                  bottom={0}
                  right="-15px"
                  top="30px"
                  width={0}
                  height={0}
                  zIndex={0}
                  borderLeft="20px solid transparent"
                  borderRight="20px solid transparent"
                  borderTop="20px solid white"
                />
              )}
              <Box 
                bgColor={"white"} 
                p={2.5} 
                rounded={10} 
                zIndex={1} 
                position="relative"
                _hover={{
                  "& .reply-button": {
                    opacity: 1,
                  }
                }}
              >
                <VStack align={"start"} justify={"space-between"} w="100%">
                  {renderReplyMessage()}
                  <Box color={"black"} w="100%">
                    {renderMessageContent()}
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                  </HStack>
                </VStack>
                <IconButton
                  className="reply-button"
                  aria-label="Reply to message"
                  size="sm"
                  position="absolute"
                  left={-10}
                  bottom={0}
                  rounded="full"
                  bgColor="chatPrimary"
                  color="white"
                  opacity={0}
                  transition="opacity 0.2s"
                  onClick={handleReply}
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                  zIndex={2}
                >
                  <TbCornerDownRight />
                </IconButton>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      ) : (
        <HStack w={"100%"} justifyContent={"flex-start"}>
          <HStack
            alignItems={"start"}
            gap={5}
            maxWidth={{'2xl':"80%", base: "70%"}}
            ml={messageCount !== 1 ? 16 : 0}
          >
            <Avatar
              hidden={messageCount !== 1}
              size={"lg"}
              name={clientName}
              bgColor={'#5e5e5e'}
              src={undefined}
              zIndex={1}
            />

            <VStack position={"relative"}>
              {messageCount === 1 && (
                <Box
                  position="absolute"
                  bottom={0}
                  left="-15px"
                  top="30px"
                  width={0}
                  height={0}
                  zIndex={0}
                  borderLeft="20px solid transparent"
                  borderRight="20px solid transparent"
                  borderTop="20px solid white"
                />
              )}
              <Box 
                bgColor={"white"} 
                p={2.5} 
                rounded={10} 
                zIndex={1} 
                position="relative"
                _hover={{
                  "& .reply-button": {
                    opacity: 1,
                  }
                }}
              >
                <VStack align={"start"} justify={"space-between"} w="100%">
                  {renderReplyMessage()}
                  <Box color={"black"} w="100%">
                    {renderMessageContent()}
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                  </HStack>
                </VStack>
                <IconButton
                  className="reply-button"
                  aria-label="Reply to message"
                  size="xs"
                  position="absolute"
                  bottom={0}
                  right={-10}
                  rounded="full"
                  bgColor="chatPrimary"
                  color="white"
                  opacity={0}
                  transition="opacity 0.2s"
                  onClick={handleReply}
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                  zIndex={2}
                >
                  <TbCornerDownRight />
                </IconButton>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      )}
    </>
  );
}
