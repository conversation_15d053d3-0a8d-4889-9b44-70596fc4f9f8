"use client";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { Flex, Text, HStack, Badge, VStack } from "@chakra-ui/react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useEffect, useState } from "react";
import { api } from "@/services/api";
import { Avatar } from "@/components/ui/avatar";

type InfoTextProps = {
  sessionId?: string;
};

export default function InfoText({ sessionId }: InfoTextProps) {
  const { sessionInfo } = useChatSessionContext();
  const [createdAt, setCreatedAt] = useState<string | null>(null);

  useEffect(() => {
    if (sessionId) {
      // Buscar informações adicionais da sessão se necessário
      const fetchSessionDetails = async () => {
        try {
          const { data } = await api.get(`/chat-sessions/${sessionId}`);
          if (data && data.createdAt) {
            setCreatedAt(data.createdAt);
          }
        } catch (error) {
          console.error("Erro ao buscar detalhes da sessão:", error);
        }
      };

      fetchSessionDetails();
    }
  }, [sessionId]);

  // Formatar a data de criação da sessão
  const formattedDate = createdAt
    ? format(new Date(createdAt), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", {
        locale: ptBR,
      })
    : "Data não disponível";

  return (
    <Flex
      w="100%"
      justifyContent="space-between"
      alignItems="center"
      flexWrap="wrap"
      gap={2}
    >
      <HStack gap={4}>
        <HStack>
          <Avatar
            name={sessionInfo?.customerName}
            size={"lg"}
            bgColor={"#5e5e5e"}
          />
          <VStack gap={0} alignItems="flex-start">
            <Text fontSize='md' fontWeight={'bold'} color="gray.500">
              {sessionInfo?.customerName || "Cliente"}
            </Text>
            <Text fontSize="sm" color="gray.500">
              {sessionInfo?.customerPhone || ''}
            </Text>
          </VStack>
        </HStack>
        {sessionInfo?.isAIResponder && (
          <Badge
            colorScheme="purple"
            variant="solid"
            borderRadius="full"
            px={2}
          >
            IA
          </Badge>
        )}
        {sessionInfo?.isArchived && (
          <Badge colorScheme="gray" variant="solid" borderRadius="full" px={2}>
            Arquivado
          </Badge>
        )}
      </HStack>

      {/* <HStack gap={4}>
        <Text fontSize="sm" color="gray.500">
          Criado em: {formattedDate}
        </Text>
      </HStack> */}
    </Flex>
  );
}
