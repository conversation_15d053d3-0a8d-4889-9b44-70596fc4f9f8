"use client"

import {
  FileUpload,
  Float,
  useFileUploadContext,
  VStack,
  Text
} from "@chakra-ui/react"
import { LuFileText, LuX } from "react-icons/lu"
import { useEffect, useState } from "react"

type FileUploadListProps = {
  handleClearFile: () => void
  file: File | null
}

export const FileUploadList = ({handleClearFile, file}: FileUploadListProps) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  
  useEffect(() => {
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl);
    }
    
    if (file && file.type.startsWith("image/") && file.size > 0) {
      const url = URL.createObjectURL(file);
      setImageUrl(url);
      
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file]);
  
  if (!file) return null
  
  return (
    <FileUpload.ItemGroup position={"absolute"} zIndex={10} mt="2" gap="2">
        <FileUpload.Item
          w="auto"
          boxSize="20"
          zIndex={10}
          borderWidth={0}
          bgColor={"white"}
          shadow={"sm"}
          borderColor={"transparent"}
          position={"absolute"}
          top={-28}
          right={0}
          p="3"
          file={file}
          key={file.name}
        >
          {file.type.startsWith("image/") ? (
            file.size > 0 && imageUrl ? (
              // Passamos explicitamente o src para o ItemPreviewImage
              <FileUpload.ItemPreviewImage 
                src={imageUrl} 
                objectFit={"contain"} 
                w={'100%'} 
                h={"100%"} 
              />
            ) : (
              <VStack>
                <LuFileText size={30} color="#FD2264"/>
                <Text color={'chatTextColor'} textAlign={'center'}>{ShortFileName(file.name)}</Text>
              </VStack>
            )
          ) : (
            <VStack>
              <LuFileText size={30} color="#FD2264"/>
              <Text color={'chatTextColor'} textAlign={'center'}>{ShortFileName(file.name)}</Text>
            </VStack>
          )}
          <Float placement="top-start" _hover={{
            cursor: "pointer",
          }} >
            <FileUpload.ItemDeleteTrigger onClick={handleClearFile} boxSize="5" layerStyle="fill.solid"  bgColor={"chatPrimary"} color={"white"} rounded={"full"}>
              <LuX size={20} />
            </FileUpload.ItemDeleteTrigger>
          </Float>
        </FileUpload.Item>
    </FileUpload.ItemGroup>
  )
}

function ShortFileName(name: string) {
  const maxLength = 7
  if (name.length <= maxLength) return name
  return `${name.slice(0, maxLength)}... ${name.slice(name.length-4, name.length)}`
}
