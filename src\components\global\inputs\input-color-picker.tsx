"use client";

import { Color, HStack, parseColor } from "@chakra-ui/react";
import {
  ColorPickerArea,
  ColorPickerContent,
  ColorPickerControl,
  ColorPickerEyeDropper,
  ColorPickerInput,
  ColorPickerLabel,
  ColorPickerRoot,
  ColorPickerSliders,
  ColorPickerTrigger,
} from "@/components/ui/color-picker";
import { Dispatch, SetStateAction } from "react";

type InputPickColorProps = {
  label: string;
  color: Color;
  setColor: Dispatch<SetStateAction<Color>>;
};

export default function InputPickColor({
  label,
  color,
  setColor,
}: InputPickColorProps) {
  return (
    <ColorPickerRoot
      value={color}
      maxW="200px"
      color={"chatTextColor"}
      onValueChange={(e) => setColor(e.value)}
      border={"none"}
    >
      <ColorPickerLabel color={"chatTextColor"}>{label}</ColorPickerLabel>
      <ColorPickerControl>
        <ColorPickerInput borderColor="#D6D6D6" />
        <ColorPickerTrigger borderColor="#D6D6D6" />
      </ColorPickerControl>
      <ColorPickerContent>
        <ColorPickerArea />
        <HStack>
          <ColorPickerEyeDropper />
          <ColorPickerSliders />
        </HStack>
      </ColorPickerContent>
    </ColorPickerRoot>
  );
}
