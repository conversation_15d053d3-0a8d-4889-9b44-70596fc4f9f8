import {
  Flex,
  Icon,
  Text,
} from "@chakra-ui/react";
import { IoBusinessOutline } from "react-icons/io5";

interface CardAccountProps {
  companyName: string;
}

export default function CardAccount({ companyName }: CardAccountProps) {
  return (
    <Flex
      alignItems="center"
      gap={4}
      p={{ base: 4, md: 6 }}
      borderRadius="2xl"
      flexBasis={{ base: "100%", sm: "45%", md: "40%", lg: "30%" }}
      transition="0.3s"
      _hover={{
        transition: "0.3s",
        transform: "scale(1.01)",
        cursor: "pointer",
      }}
      bg={"background"}
    >
      <Icon color="#A1A1AA" fontSize={{ base: "xl", md: "2xl" }}>
        <IoBusinessOutline/>
      </Icon>
      <Text
        fontSize={{ base: "md", md: "lg" }}
        fontWeight="bold"
        color="whiteAlpha.900"
      >
        {companyName}
      </Text>
    </Flex>
  );
}
