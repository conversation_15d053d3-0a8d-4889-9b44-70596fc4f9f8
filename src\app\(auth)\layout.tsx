import { Metadata } from "next";
import AuthLayoutContainer from "./components/auth-layout";
import { Toaster } from "@/components/ui/toaster";

type AuthLayoutProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "PlyrChat",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function AuthLayout({ children }: AuthLayoutProps) {
  return <AuthLayoutContainer>{children}</AuthLayoutContainer>;
}
