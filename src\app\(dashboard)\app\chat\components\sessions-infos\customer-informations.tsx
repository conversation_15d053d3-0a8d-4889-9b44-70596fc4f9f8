"use client";
import { InputEditable } from "@/components/global/inputs/input-editable";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { VStack } from "@chakra-ui/react";
import { useEffect, useState } from "react";

export default function CustomerInformation() {
  const { sessionInfo, selectedSessionId } = useChatSessionContext();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [document, setDocument] = useState("");
  const [phone, setPhone] = useState("");

  useEffect(() => {
    if (sessionInfo) {
      setName(sessionInfo?.customerName || "");
      setEmail(sessionInfo?.customerEmail || "");
      setDocument(sessionInfo?.customerDocument || "");
      setPhone(sessionInfo?.customerPhone || "");
    }
  }, [sessionInfo, setName, setEmail, setDocument, setPhone]);

  if (!selectedSessionId) return null;

  const handleSave = async () => {
    await api.put(`/chat-sessions/${selectedSessionId}`, {
      customerName: name,
      customerEmail: email !== "" ? email : undefined,
      customerDocument: document !== "" ? document : undefined,
      customerPhone: phone !== "" ? phone : undefined,
    });

    await queryClient.invalidateQueries({
      queryKey: ["chat-sessions"],
    });
  };
  return (
    <VStack flex={1} w="100%" pt={2}>
      <VStack
        w="100%"
        justifyContent={"flex-start"}
        gap={3}
      >
        <InputEditable
          placeholder={"Nome"}
          value={name}
          onValueChange={setName}
          handleSave={handleSave}
        />
        <InputEditable
          placeholder={"Email"}
          value={email}
          onValueChange={setEmail}
          handleSave={handleSave}
        />
        <InputEditable
          placeholder={"Documento"}
          value={document}
          onValueChange={setDocument}
          handleSave={handleSave}
        />
        <InputEditable
          placeholder={"Telefone"}
          value={phone}
          onValueChange={setPhone}
          handleSave={handleSave}
        />
      </VStack>
    </VStack>
  );
}
