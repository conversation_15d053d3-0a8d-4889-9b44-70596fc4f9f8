import BasicModal from "@/components/global/modal/basic-modal";
import Pagination from "@/components/global/pagination/pagination";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { api } from "@/services/api";
import { ListAttendantInputDTO } from "@/utils/types/DTO/attendant.dto";
import { VStack, Text, Box, Center, Spinner } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import AttendantCard from "./attendant-card";
import { useRouter } from "next/navigation";
import { toaster } from "@/components/ui/toaster";
import { queryClient } from "@/services/queryClient";

type ModalAlterAttendantProps = {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  chatSessionSecureId: string;
};

export default function ModalAlterAttendant({
  isOpen,
  chatSessionSecureId,
  setIsOpen,
}: ModalAlterAttendantProps) {
  const { sessionInfo } = useChatSessionContext();
  const [page, setPage] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [attendants, setAttendants] = useState<ListAttendantInputDTO>();

  const router = useRouter();

  useEffect(() => {
    setIsLoading(true);
    const fetchAttendants = async () => {
      const { data } = await api.get<ListAttendantInputDTO>(
        `/attendant?onlyInRotation=true`
      , {
				params: {
					page: page,
					limit: 3,
				} 
			});

      setAttendants(data);
      setIsLoading(false);
    };
    fetchAttendants();
  }, [sessionInfo, page]);

  const handleChangeAttendant = async (secureId: string) => {
    await api.put(`/chat-sessions/${chatSessionSecureId}`, {
      newAttendantSecureId: secureId,
    });
		await queryClient.invalidateQueries({
			queryKey: ["get-all-chat-sessions"]
		})
    setIsOpen(false);
    router.push('/app/chat');
    toaster.create({
      type: "success",
      description: `Atendente alterado com sucesso`,
      title: `Atendente alterado`,
    });
  };

  return (
    <BasicModal
      open={isOpen}
      setOpen={setIsOpen}
      title={"Escolha o Atendente"}
      isSubmitting={false}
      closeOnEscape={true}
      closeOnInteractOutside={true}
      placement="center"
      size="lg"
      children={
        <>
          {!attendants || attendants.data.length === 0 ? (
            <Center w={"100%"} h={"100%"}>
              <Text color={"chatTextColor"} fontSize={"lg"} fontWeight={"bold"}>
                Nenhum atendente encontrado
              </Text>
            </Center>
          ) : (
            <VStack gap={3}>
              {isLoading ? (
                <Center w={"100%"} h={"100%"}>
                  <Spinner color={"chatPrimary"}></Spinner>
                </Center>
              ) : (
                <>
                  {attendants.data?.map((attendant) => {
                    const isSelected =
                      sessionInfo?.attendantSecureId === attendant.secureId;
                    return (
                      <AttendantCard
                        key={attendant.secureId}
                        attendant={attendant}
                        isSelected={isSelected}
                        handleChangeAttendant={handleChangeAttendant}
                      />
                    );
                  })}
                </>
              )}
              <Pagination
                totalItems={attendants.meta.totalItems}
                itemsPerPage={attendants.meta.itemsPerPage}
                page={page}
                setPage={setPage}
                position={"absolute"}
                bottom={0}
                right={0}
              />
            </VStack>
          )}
        </>
      }
    />
  );
}
