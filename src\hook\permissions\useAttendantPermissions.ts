import { api } from "@/services/api";
import { permissionsInputDTO } from "@/utils/types/DTO/permissions.dto";
import { useQuery } from "@tanstack/react-query";

type PermissionsResponse = {
  permissions: permissionsInputDTO[];
};

async function getAllPermissions() {
  const { data } = await api.get<PermissionsResponse>(
    "/register/attendant-dependencies"
  );

  return data;
}

export default function useAttendantPermissions() {
  return useQuery({
    queryKey: ["permissions"],
    queryFn: async () => await getAllPermissions(),
  });
}
