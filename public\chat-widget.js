(function () {
	// Verificar se o widget já existe para evitar duplicação
	if (document.getElementById("plyrchat-widget-button")) {
		return;
	}

	const config = window.PlyrChatWidgetConfig || {};
	const websiteUrl = config.websiteUrl || "https://default-api.com";
	const btnColor = config.btnColor || "#007bff";
	const chatId = config.chatId || "default";
	const iconUrl = config.iconUrl || "https://default.com/icon.png";

	const b = document.createElement("button");
	Object.assign(b.style, {
		position: "fixed", bottom: "20px", right: "20px", zIndex: "9999",
		background: btnColor, color: "#fff", border: "none",
		padding: "15px", borderRadius: "50%", cursor: "pointer",
		boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)", display: "flex",
		alignItems: "center", justifyContent: "center",
		width: "90px", height: "90px"
	});

	// Corrigir a atribuição do ID
	b.id = "plyrchat-widget-button";

	const i = document.createElement("img");
	Object.assign(i.style, { width: "90px", height: "90px", objectFit: "contain", borderRadius: "100%" });
	i.src = iconUrl;
	i.alt = "Chat";

	b.appendChild(i);
	document.body.appendChild(b);

	const styleSheet = document.styleSheets[0];
	styleSheet.insertRule(`@keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.2); } 100% { transform: scale(1); }}`, styleSheet.cssRules.length);
	b.style.animation = "pulse 2s infinite";

	// Garantir que o botão tenha display flex consistente
	b.style.display = "flex";

	b.onclick = () => {
		const container = document.createElement("div");
		Object.assign(container.style, {
			position: "fixed", bottom: "20px", right: "20px", zIndex: "10000",
			width: "400px", height: "600px", opacity: "0", transition: "opacity 0.25s ease-in-out"
		});

		const iframe = document.createElement("iframe");
		Object.assign(iframe.style, {
			width: "100%", height: "100%", border: "none", borderRadius: "8px",
			boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)"
		});
		iframe.src = `${websiteUrl}/webchat/${chatId}`;

		const close = document.createElement("button");
		Object.assign(close.style, {
			position: "absolute", top: "1px", right: "15px", padding: '0', zIndex: "10001",
			background: "transparent", color: btnColor, border: "none",
			fontSize: "30px", cursor: "pointer"
		});
		close.innerText = "×";

		const resize = () => {
			const isSmall = window.matchMedia("(max-width: 479px)").matches;
			Object.assign(container.style, isSmall
				? { top: "0", left: "0", width: "100vw", height: "100vh", borderRadius: "0" }
				: { bottom: "20px", right: "20px", width: "400px", height: "600px", borderRadius: "8px" });
			iframe.style.borderRadius = isSmall ? "0" : "8px";
		};

		resize();
		window.addEventListener("resize", resize);

		container.append(iframe, close);
		document.body.appendChild(container);
		b.style.display = "none";

		setTimeout(() => container.style.opacity = "1", 100);

		close.onclick = () => {
			container.style.opacity = "0";
			setTimeout(() => {
				container.remove();
				// Verificar se o botão ainda existe antes de exibi-lo novamente
				const existingButton = document.getElementById("plyrchat-widget-button");
				if (existingButton) {
					existingButton.style.display = "flex";
				} else {
					// Se o botão não existir mais, reexibir o botão original
					b.style.display = "flex";
					// Garantir que o botão esteja no DOM
					if (!document.body.contains(b)) {
						document.body.appendChild(b);
					}
				}
				window.removeEventListener("resize", resize);
			}, 250);
		};
	};
})();
