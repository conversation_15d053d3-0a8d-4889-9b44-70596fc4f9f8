"use client";
import { toaster } from "@/components/ui/toaster";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { api } from "@/services/api";
import { Text, Textarea, VStack } from "@chakra-ui/react";
import { useEffect, useState } from "react";

export default function SessionsObservation() {
  const { sessionInfo, selectedSessionId } = useChatSessionContext();
  const [value, setValue] = useState("");

  useEffect(() => {
    setValue(sessionInfo?.sessionDescription || "");
  }, [sessionInfo, selectedSessionId]);

  const handleSave = async () => {
    await api.put(`/chat-sessions/${selectedSessionId}`, {
      sessionDescription: value,
    });
  };
  if (!selectedSessionId) return null;

  return (
    <VStack flex={1} h={"100%"} w={"100%"}>
      <Textarea
        size="sm"
        resize="none"
        height={"100%"}
        width={"100%"}
        _hover={{
          borderColor: "chatPrimary",
        }}
        _placeholder={{ color: "#B1A0A5" }}
        _focus={{
          borderColor: "chatPrimary",
        }}
        bgColor={"white"}
        borderRadius={10}
        color={"#84767A"}
        borderColor="#D6D6D6"
        borderWidth={2}
        css={{
          "&::-webkit-scrollbar": {
            width: "4px",
          },
          "&::-webkit-scrollbar-track": {
            width: "4px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
            "&:hover": {
              background: "#A6A6A6",
            },
          },
        }}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onBlur={handleSave}
      />
    </VStack>
  );
}
