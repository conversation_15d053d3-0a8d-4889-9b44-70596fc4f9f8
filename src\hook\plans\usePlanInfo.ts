import { api } from "@/services/api";
import { PlanInfoDto } from "@/utils/types/DTO/plan-info.dto";
import { useQuery } from "@tanstack/react-query";

async function getPlan(slug: string) {
  const { data } = await api.get<PlanInfoDto>(`/backoffice/plans/${slug}`);

  return data;
}

export default function usePlanInfo(slug: string) {
  return useQuery({
    queryFn: async () => await getPlan(slug),
    queryKey: ["plan-info", slug],
    refetchInterval: false,
  });
}
