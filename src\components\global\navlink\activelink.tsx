import Link, { LinkProps } from "next/link";
import { usePathname } from "next/navigation";
import { cloneElement, FC, ReactElement } from "react";

interface ActiveLinkProps extends LinkProps {
  children: ReactElement;
  shouldMatchExactHref?: boolean;
}

export const ActiveLink: FC<ActiveLinkProps> = ({
  children,
  shouldMatchExactHref = false,
  ...rest
}) => {
  const pathname = usePathname();

  let isActive = false;

  if (
    shouldMatchExactHref &&
    (pathname === rest.href || pathname === rest.as)
  ) {
    isActive = true;
  }
  if (
    !shouldMatchExactHref &&
    (pathname.startsWith(String(rest.href)) ||
      pathname.startsWith(String(rest.as)))
  ) {
    isActive = true;
  }

  return (
    <Link {...rest} style={{ width: "100%", maxHeight: "44px" }}>
      {cloneElement(children, {
        // color: isActive ? "blue.500" : "blue.200",
      })}
    </Link>
  );
};
