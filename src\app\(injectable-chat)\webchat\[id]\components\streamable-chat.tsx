"use client";
import ChatFooterWebStream from "@/components/global/chat/chat-footer-stream";
import MessageBubbleInjectChat from "@/components/global/chat/message-bubble-no-pic-ai";
import { ChatMessagesHistoryDto } from "@/utils/types/DTO/chat-messages-history.dto";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import { Flex, VStack } from "@chakra-ui/react";
import { useChat, Message } from "@ai-sdk/react"
import { format } from "date-fns";
import { useCallback, useEffect, useRef, useState } from "react";
import { v4 as uuidV4 } from "uuid";

type StreamableChatProps = {
  secureId: string;
  chatData: GetOneChatDtoInput;
  sessionSecureId: string;
  userSecureId: string;
  chatHistory?: ChatMessagesHistoryDto[];
};

export default function StreamableChat({
  chatData,
  secureId,
  sessionSecureId,
  userSecureId,
  chatHistory,
}: StreamableChatProps) {
  const chatEndRef = useRef<HTMLDivElement>(null);
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    setMessages,
  } = useChat({
    api: `${process.env.NEXT_PUBLIC_API_URL}public/chats/${secureId}`,
    streamProtocol: "data",
    onResponse: (response) => {
      console.log("Response received:", response);
    },
    body: {
      sessionSecureId,
      userSecureId,
    },
  });

  useEffect(() => {
    if (chatData.welcomeMessage) {
      setMessages([
        {
          id: uuidV4(),
          content: chatData.welcomeMessage,
          role: "assistant",
        },
      ]);
    }
    if (chatHistory && chatHistory.length > 0) {
      setMessages((currentMessages) => [
        ...currentMessages,
        ...chatHistory
          .filter(
            (message) =>
              !currentMessages.some(
                (existing) => existing.id === message.secureId
              )
          )
          .map<Message>((message) => ({
            id: message.secureId,
            content: message.receiveMessage ?? message.sendMessage ?? "",
            role: message.receiveMessage ? "user" : "assistant",
          })),
      ]);
    }
  }, [chatData, chatHistory]);

  const scrollToBottom = useCallback(() => {
    setTimeout(() => {
      chatEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }, 100);
  }, [chatEndRef]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <>
      <Flex
        w={"100%"}
        flex={1}
        px={2}
        gap={5}
        pb={2}
        flexDir="column"
        alignItems="flex-end"
        h={{ base: "calc(100vh - 80px)", md: "calc(100vh - 180px)" }}
        overflowY="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            width: "6px",
            marginBottom: "25px",
            marginTop: "25px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
            "&:hover": {
              background: "#A6A6A6",
            },
          },
        }}
      >
        <VStack flex={1} w="100%" justifyContent="flex-end">
          <>
            {messages.map((message, index) => {
              const role = message.role;
              const previousMessage = index > 0 ? messages[index - 1] : null;
              const isFirstMessage = !previousMessage || previousMessage.role !== role;

              return (
                <MessageBubbleInjectChat
                  key={message.id}
                  message={message.content}
                  isMine={message.role === "user"}
                  attendantName={chatData.chatbot?.name}
                  isFirstMessage={isFirstMessage}
                  avatarUrl={chatData.upload.urlCdn}
                  messageTime={format(new Date(), "HH:mm")}
                  messageStatus={"read"}
                  avatarBgColor={chatData.avatarBgColor}
                  customerMessageBgColor={chatData.customerMessageBubbleColor}
                  customerMessageColor={chatData.customerMessageTextColor}
                  attendantMessageBgColor={chatData.attendantMessageBubbleColor}
                  attendantMessageColor={chatData.attendantMessageTextColor}
                  attendantAvatarColor={chatData.chatButtonColor}
                />
              );
            })}
            <div ref={chatEndRef} />
          </>
        </VStack>
      </Flex>
      <ChatFooterWebStream
        handleInputChange={handleInputChange}
        input={input}
        handleSubmit={handleSubmit}
        buttonBgColor={chatData.chatButtonColor}
        inputBgColor={chatData.inputChatBgColor}
        inputMessageColor={chatData.inputChatTextColor}
        isLoading={isLoading}
      />
    </>
  );
}
