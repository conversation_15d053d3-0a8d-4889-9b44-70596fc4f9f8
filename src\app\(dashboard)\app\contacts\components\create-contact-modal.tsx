import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { Flex, HStack, VStack } from "@chakra-ui/react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

type CreateContactModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const CreateContactSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  phone: yup.string().required("Celular é obrigatório"),
  document: yup.string().optional(),
});

type CreateContactForm = yup.InferType<typeof CreateContactSchema>;

export function CreateContactModal({ open, setOpen }: CreateContactModalProps) {
	const phoneMask = "+99 (99) 99999-9999[9]";
	const cpfMask = "999.999.999-99";
	const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
		reset,
    formState: { errors },
  } = useForm<CreateContactForm>({
    resolver: yupResolver(CreateContactSchema),
  });

	const createContactMutation = useMutation({
		mutationFn: async (data: CreateContactForm) => {
			const response = await api.post("/contact", {
				name: data.name,
				email: data.email,
				phone: data.phone,
				document: data.document,
			});
			return response.data;
		},
		onSuccess: () => {
			toaster.success({ title: "Contato criado com sucesso!", type: "success" });
			queryClient.invalidateQueries({ queryKey: ['list-contacts'] });
			setOpen(false);
			reset();
		},
		onError: (error) => {
			toaster.error({ title: "Erro ao criar contato!", type: "error" });
			console.error("Erro ao criar contato:", error);
		},
	});

	useEffect(() => {
		if (!open) {
			reset();
		}
	}, [open, reset]);

	const onSubmit = async (data: CreateContactForm) => {
		await createContactMutation.mutateAsync(data);
	}

  return (
    <BasicModal
      open={open}
      setOpen={setOpen}
      asForm={true}
      cancelText="Cancelar"
      children={
        <VStack gap={10} alignItems={"flex-start"} w={"100%"}>
          <HStack gap={5} w={"100%"}>
            <Input
              label="Nome"
              placeholder="Nome do contato"
              {...register("name")}
							rounded={'md'}
              error={errors.name}
            />
            <Input
              label="Email"
              placeholder="Email do contato"
              {...register("email")}
							rounded={'md'}
              error={errors.email}
            />
          </HStack>
          <HStack w={"100%"}>
            <InputMaskIcon
              label="Celular"
							mask={phoneMask}
              placeholder="Celular do contato"
              {...register("phone")}
							rounded={'md'}
              error={errors.phone}
            />
            <InputMaskIcon
              label="Documento"
							mask={cpfMask}
              placeholder="Documento do contato"
              {...register("document")}
							rounded={'md'}
              error={errors.document}
            />
          </HStack>
        </VStack>
      }
      handleSubmit={handleSubmit(onSubmit)}
      isSubmitting={createContactMutation.isPending}
      confirmText="Cadastrar"
      title="Cadastrar Contato"
      placement="center"
      size="lg"
    />
  );
}
