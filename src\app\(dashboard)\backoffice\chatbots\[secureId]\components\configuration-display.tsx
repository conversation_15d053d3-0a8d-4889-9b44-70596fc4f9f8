import {
  Box,
  Grid,
  <PERSON>ridI<PERSON>,
  Text,
  VStack,
  HStack,
  <PERSON>ge,
  Separator,
  Code,
  Textarea,
} from "@chakra-ui/react";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuSettings, LuMessageSquare, LuUsers, LuThermometer } from "react-icons/lu";

type ConfigurationDisplayProps = {
  chatbot: BackofficeChatbotDetailDto;
};

export default function ConfigurationDisplay({ chatbot }: ConfigurationDisplayProps) {
  const getEmotionalToneLabel = (tone: string) => {
    const tones: Record<string, string> = {
      neutral: "Neutro",
      empathetic: "Empático",
      motivational: "Motivacional",
      positive: "Positivo",
      negative: "Negativo",
      calm: "Calmo",
    };
    return tones[tone] || tone;
  };

  const getMoodLabel = (mood: string) => {
    const moods: Record<string, string> = {
      neutral: "Neutro",
      casual: "Casual",
      formal: "Formal",
      funny: "Engraçado",
      sarcastic: "Sarc<PERSON><PERSON><PERSON>",
    };
    return moods[mood] || mood;
  };

  const getResponseStyleLabel = (style: string) => {
    const styles: Record<string, string> = {
      objective: "Objetivo",
      detailed: "Detalhado",
    };
    return styles[style] || style;
  };

  const getResponseSizeLabel = (size: string) => {
    const sizes: Record<string, string> = {
      short: "Curto",
      medium: "Médio",
      long: "Longo",
    };
    return sizes[size] || size;
  };

  return (
    <Box
      bg="background"
      borderRadius="xl"
      p={6}
      maxH="calc(100vh - 250px)"
      position="relative"
      overflow="auto"
      css={{
        boxSizing: "border-box",
        paddingRight: "8px", 
        "&::-webkit-scrollbar": { width: "6px" },
        "&::-webkit-scrollbar-track": { background: "transparent" },
        "&::-webkit-scrollbar-thumb": {
          background: "rgba(85, 85, 85, 0.4)",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "rgba(102, 102, 102, 0.6)",
        },
      }}
      // shadow="sm"
    >
      <VStack align="stretch" gap={6}>
        {/* Header */}
        <HStack>
          <LuSettings size={24} color="#fd2264" />
          <Text fontSize="lg" fontWeight="bold" color="white">
            Configurações do Chatbot
          </Text>
        </HStack>

        <Separator />

        {/* AI Configuration */}
        <VStack align="stretch" gap={4}>
          <Text fontSize="md" fontWeight="semibold" color="white">
            Configurações de IA
          </Text>
          
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Tom Emocional:
                </Text>
                <Badge colorPalette="blue" variant="subtle" fontSize="sm">
                  {getEmotionalToneLabel(chatbot.configuration.emotionalTone || "Não definido")}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Humor:
                </Text>
                <Badge colorPalette="green" variant="subtle" fontSize="sm">
                  {getMoodLabel(chatbot.configuration.mood || "Não definido")}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Estilo de Resposta:
                </Text>
                <Badge colorPalette="purple" variant="subtle" fontSize="sm">
                  {getResponseStyleLabel(chatbot.configuration.responseStyle || "Não definido")}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Tamanho da Resposta:
                </Text>
                <Badge colorPalette="orange" variant="subtle" fontSize="sm">
                  {getResponseSizeLabel(chatbot.configuration.responseSize || "Não definido")}
                </Badge>
              </VStack>
            </GridItem>
          </Grid>

          {/* Temperature */}
          <HStack>
            <LuThermometer size={20} color="#6B7280" />
            <VStack align="start" gap={0}>
              <Text fontSize="sm" color="gray.400" fontWeight="medium">
                Temperatura (Criatividade):
              </Text>
              <Text fontSize="md" color="white" fontWeight="semibold">
                {(chatbot.configuration.temperature / 100).toFixed(1)} / 1.0
              </Text>
            </VStack>
          </HStack>
        </VStack>

        <Separator />

        {/* Greeting Message */}
        <VStack align="stretch" gap={3}>
          <HStack>
            <LuMessageSquare size={20} color="#fd2264" />
            <Text fontSize="md" fontWeight="semibold" color="white">
              Mensagem de Saudação
            </Text>
          </HStack>
          <Box
            bg="gray.50"
            p={4}
            borderRadius="md"
            border="1px solid"
            borderColor="white"
          >
            <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
              {chatbot.configuration.greetingMessage || "Nenhuma mensagem de saudação configurada"}
            </Text>
          </Box>
        </VStack>

        {/* AI Prompt */}
        {chatbot.configuration.AIPrompt && (
          <>
            <Separator />
            <VStack align="stretch" gap={3}>
              <Text fontSize="md" fontWeight="semibold" color="white">
                Prompt da IA
              </Text>
              <Textarea
                value={chatbot.configuration.AIPrompt}
                color={"gray.700"}
                readOnly
                bg="gray.50"
                border="1px solid"
                borderColor="white"
                fontSize="sm"
                minH="120px"
                resize="none"
              />
            </VStack>
          </>
        )}

        {/* Lead Capture Settings */}
        {chatbot.configuration.isLeadCaptureActive && (
          <>
            <Separator />
            <VStack align="stretch" gap={4}>
              <HStack>
                <LuUsers size={20} color="#fd2264" />
                <Text fontSize="md" fontWeight="semibold" color="white">
                  Configurações de Captura de Lead
                </Text>
              </HStack>

              <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
                <GridItem>
                  <VStack align="start" gap={2}>
                    <Text fontSize="sm" color="gray.400" fontWeight="medium">
                      Limite de Mensagens para Trigger:
                    </Text>
                    <Text fontSize="md" color="white" fontWeight="semibold">
                      {chatbot.configuration.leadTriggerMessageLimit || "Não definido"}
                    </Text>
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="start" gap={2}>
                    <Text fontSize="sm" color="gray.400" fontWeight="medium">
                      Campos Coletados:
                    </Text>
                    <HStack wrap="wrap" gap={2}>
                      {chatbot.configuration.leadCaptureJson?.collectName && (
                        <Badge colorScheme="blue" variant="outline" size="sm">
                          Nome
                        </Badge>
                      )}
                      {chatbot.configuration.leadCaptureJson?.collectEmail && (
                        <Badge colorScheme="green" variant="outline" size="sm">
                          Email
                        </Badge>
                      )}
                      {chatbot.configuration.leadCaptureJson?.collectPhone && (
                        <Badge colorScheme="purple" variant="outline" size="sm">
                          Telefone
                        </Badge>
                      )}
                      {chatbot.configuration.leadCaptureJson?.collectCPF && (
                        <Badge colorScheme="orange" variant="outline" size="sm">
                          CPF
                        </Badge>
                      )}
                    </HStack>
                  </VStack>
                </GridItem>
              </Grid>

              <VStack align="stretch" gap={3}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Mensagem de Captura de Lead:
                </Text>
                <Box
                  bg="gray.50"
                  p={3}
                  borderRadius="md"
                  border="1px solid"
                  borderColor="white"
                >
                  <Text fontSize="sm" color="gray.700">
                    {chatbot.configuration.leadCaptureMessage || "Não configurada"}
                  </Text>
                </Box>
              </VStack>

              <VStack align="stretch" gap={3}>
                <Text fontSize="sm" color="gray.400" fontWeight="medium">
                  Mensagem de Agradecimento:
                </Text>
                <Box
                  bg="gray.50"
                  p={3}
                  borderRadius="md"
                  border="1px solid"
                  borderColor="white"
                >
                  <Text fontSize="sm" color="gray.700">
                    {chatbot.configuration.leadCaptureThankYouMessage || "Não configurada"}
                  </Text>
                </Box>
              </VStack>
            </VStack>
          </>
        )}
      </VStack>
    </Box>
  );
}
