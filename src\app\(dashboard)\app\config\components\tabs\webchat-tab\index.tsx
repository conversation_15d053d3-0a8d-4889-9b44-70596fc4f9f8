"use client";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, VS<PERSON><PERSON>, useBreakpointValue } from "@chakra-ui/react";
import WebChatCard from "./webchat-card";
import useGetChats from "@/hook/chats/useGetChats";
import HeaderTab from "../header-tab";
import { useRouter } from "next/navigation";

export default function WebChatTab() {
  const { data, isLoading, isFetching } = useGetChats();

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  const navigate = useRouter();

  const handleCreateWebChat = () => {
    navigate.push("/app/webchat/create");
  };

  return (
    <Flex
      h={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      {!data || isLoading || isFetching ? (
        <Center flex={1}>
          <Spinner color={"chatPrimary"}></Spinner>
        </Center>
      ) : (
        <>
          <VStack flex={1} alignItems={"flex-start"} mt={{ base: 8, md: 0 }}>
            <HeaderTab
              title="WebChat"
              buttonTitle={!isMobile ? "Adicionar WebChat" : undefined}
              onClick={!isMobile ? handleCreateWebChat : undefined}
            />
            {isMobile ? (
              <VStack
                flex={1}
                w={"100%"}
                alignItems={"stretch"}
                gap={3}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum WebChat Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data?.data.map((chat) => (
                      <WebChatCard
                        description={chat.description}
                        name={chat.name}
                        secureId={chat.secureId}
                        active={chat.isActive}
                        key={chat.secureId}
                      />
                    ))}
                  </>
                )}
              </VStack>
            ) : (
              <HStack
                flex={1}
                flexWrap={"wrap"}
                w={"100%"}
                alignContent={"start"}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum WebChat Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data?.data.map((chat) => (
                      <WebChatCard
                        description={chat.description}
                        name={chat.name}
                        secureId={chat.secureId}
                        active={chat.isActive}
                        key={chat.secureId}
                      />
                    ))}
                  </>
                )}
              </HStack>
            )}
          </VStack>
        </>
      )}
    </Flex>
  );
}
