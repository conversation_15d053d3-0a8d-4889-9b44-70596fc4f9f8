import { api } from "@/services/api";
import { GetChatsDto } from "@/utils/types/DTO/chats.dto";
import { MetaDTO } from "@/utils/types/DTO/meta.dto";
import { useQuery } from "@tanstack/react-query";

type GetChatsDtoInput = {
  meta: MetaDTO;
  data: GetChatsDto[];
};

async function findAllChats() {
  const { data } = await api.get<GetChatsDtoInput>("/chats", {
    params: {
      isDeleted: false,
    },
  });
  return data;
}

export default function useGetChats() {
  return useQuery({
    queryFn: async () => await findAllChats(),
    queryKey: ["chats"],
  });
}
