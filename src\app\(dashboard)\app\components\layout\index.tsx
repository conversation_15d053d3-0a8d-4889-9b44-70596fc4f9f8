"use client";
import { Box, Flex, HStack, Spinner, Text, VStack, useBreakpointValue } from "@chakra-ui/react";
import AppNavBar from "../navbar";
import Header from "../header/header";
import AppDrawerMenu from "@/components/global/drawer-menu/drawer-app";
import { useAuthContext } from "@/providers/AuthProvider";
import { SocketProvider } from "@/providers/SocketProvider";
import BasicModal from "@/components/global/modal/basic-modal";
import { useState, useEffect } from "react";

type AppLayoutContainerProps = {
  children: React.ReactNode;
};

export default function AppLayoutContainer({
  children,
}: AppLayoutContainerProps) {
  const { user } = useAuthContext();

  const isMobile = useBreakpointValue({ base: true, md: false });
  const [showMobileWarning, setShowMobileWarning] = useState(false);

  useEffect(() => {
    const hasShownMobileWarning = sessionStorage.getItem('hasShownMobileWarning');
    console.log({
      hasShownMobileWarning,
      isMobile,
    })

    if (isMobile && !hasShownMobileWarning) {
      setShowMobileWarning(true);
      // Mark that we've shown the warning in this session
      sessionStorage.setItem('hasShownMobileWarning', 'true');
    }
  }, [isMobile]);

  if (!user) return null;

  return (
    <Flex
      flex={1}
      p={{ md: 5, base: 0 }}
      flexDir={"column"}
      h={'100vh'}
      maxH={"100vh"}
      overflow={{ base: "auto", md: "hidden" }}
      w={"100vw"}
      bgColor={"chatBackground"}
    >
      {!user ? (
        <Spinner />
      ) : (
        <>
          <AppDrawerMenu name={user?.activeAccount.userName} />
          <Header name={user?.activeAccount.userName} trialEndAt={user?.activeAccount.trialEndAt} />
          <Flex flex={1} gap={5} mt={{ base: 8, md: 0 }}>
            <AppNavBar />
            {children}
          </Flex>
        </>
      )}

      {/* Mobile Warning Modal */}
      <BasicModal
        open={showMobileWarning}
        setOpen={setShowMobileWarning}
        title="Versão Mobile"
        cancelText="Entendi"
        placement="center"
        size="md"
        handleConfirm={() => setShowMobileWarning(false)}
        children={
          <VStack gap={4} p={4}>
            <Text
              fontSize={"lg"}
              fontWeight={"bold"}
              color="chatPrimary"
              textAlign="center"
            >
              Acesso via Dispositivo Móvel
            </Text>
            <Text
              fontSize={"md"}
              color={"chatTextColor"}
              textAlign="center"
              lineHeight={1.6}
            >
              Algumas funcionalidades podem estar limitadas em dispositivos móveis.
              Para a melhor experiência completa, recomendamos o acesso via desktop.
            </Text>
          </VStack>
        }
      />
    </Flex>
  );
}
