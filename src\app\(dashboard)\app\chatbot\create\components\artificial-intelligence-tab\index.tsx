import { Box, Flex, Grid, Grid<PERSON>tem, HStack, VStack } from "@chakra-ui/react";
import * as yup from "yup";
import { InputSelect } from "@/components/global/inputs/input-select";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { EnumChatBotEmotionalTone } from "@/utils/enums/chat-bots.enums";
import {
  emotionalToneList,
  moodList,
  responseSizeList,
  responseStyleList,
} from "./form-select-values";
import { CustomSlider } from "@/components/global/inputs/slider";
import { useState } from "react";
import { Tooltip } from "@/components/ui/tooltip";
import { LuCircleHelp } from "react-icons/lu";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import HeaderTab from "@/components/tabs/header";
import { useRouter } from "next/navigation";
import { InputTextArea } from "@/components/global/inputs/input-text-area";

type ArtificialIntelligenceTabProps = {
  chatBotSecureId: string;
};

const ArtificialIntelligenceTabSchema = yup.object().shape({
  emotionalTone: yup
    .array(yup.string())
    .required("Tom emocional é obrigatório"),
  mood: yup.array(yup.string()).required("Humor é obrigatório"),
  responseStyle: yup
    .array(yup.string())
    .required("Estilo de resposta é obrigatório"),
  responseSize: yup
    .array(yup.string())
    .required("Tamanho da resposta é obrigatório"),
  temperature: yup.number().required("Temperatura é obrigatória"),
  greetingMessage: yup.string().optional()
});

type ArtificialIntelligenceTabFormData = yup.InferType<
  typeof ArtificialIntelligenceTabSchema
>;

export default function ArtificialIntelligenceTab({
  chatBotSecureId,
}: ArtificialIntelligenceTabProps) {
  const route = useRouter();
  const [temperatureStorage, setTemperatureStorage] = useState([0]);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ArtificialIntelligenceTabFormData>({
    resolver: yupResolver(ArtificialIntelligenceTabSchema),
  });

  const handleUpdate = async (data: ArtificialIntelligenceTabFormData) => {
    await sendUpdate.mutateAsync(data);
  };

  const sendUpdate = useMutation({
    mutationFn: async (data: ArtificialIntelligenceTabFormData) => {
      const { emotionalTone, mood, responseSize, responseStyle, temperature } =
        data;

      await api.put(`/chatbots/${chatBotSecureId}`, {
        emotionalTone: emotionalTone[0],
        mood: mood[0],
        responseSize: responseSize[0],
        responseStyle: responseStyle[0],
        temperature: temperature * 10,
        greetingMessage: data.greetingMessage,
      });
    },
    onSuccess: () => {
      toaster.success({
        description: "ChatBot atualizado com sucesso!",
        title: "Atualizado!",
      });
    },
  });

  return (
    <>
      <Flex
        height={"100%"}
        flex={1}
        p={"5"}
        bgColor={"chatCardBackground"}
        rounded={"2xl"}
        border={"none"}
      >
        <VStack flex={1} alignItems={"flex-start"} w={"100%"} gap={"5"} as={'form'} onSubmit={handleSubmit(handleUpdate)}>
          <HeaderTab
            buttonTitle="Salvar Alterações"
            title="Geral"
            hrefBack="/app/config#chatbots"
            isSubmit={isSubmitting}
          />
          <VStack
            flex={1}
            w={"100%"}
            gap={"8"}
            alignItems={"start"}
            justifyContent={"start"}
          >
            <HStack w={"100%"} gap={"8"} justifyContent={"space-between"}>
              <InputSelect
                {...register("emotionalTone")}
                control={control}
                label="Selecione a emoção transmitida"
                placeholder="(Selecione)"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.emotionalTone as any}
                itensList={emotionalToneList}
              />
              <InputSelect
                {...register("mood")}
                control={control}
                label="Selecione o humor da resposta"
                placeholder="(Selecione)"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.mood as any}
                itensList={moodList}
              />
            </HStack>
            <HStack w={"100%"} gap={"8"} justifyContent={"space-between"}>
              <InputSelect
                {...register("responseStyle")}
                control={control}
                label="Selecione o estilo de resposta"
                placeholder="(Selecione)"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.responseStyle as any}
                itensList={responseStyleList}
              />
              <InputSelect
                {...register("responseSize")}
                control={control}
                label="Selecione o tamanho da resposta"
                placeholder="(Selecione)"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.responseSize as any}
                itensList={responseSizeList}
              />
            </HStack>
            <HStack w={"100%"} gap={"8"} justifyContent={"space-between"}>
              <CustomSlider
                control={control}
                label="Temperatura"
                labelSize="sm"
                onValueChange={setTemperatureStorage}
                value={temperatureStorage}
                minValue={0}
                maxValue={10}
                valueMarks={[
                  { value: 0, label: "0.0" },
                  { value: 3, label: "0.3" },
                  { value: 7, label: "0.7" },
                  { value: 10, label: "1.0" },
                ]}
                labelColor="chatTextColor"
                {...register("temperature")}
                optionalText={
                  <Box pl={1} _hover={{ cursor: "help" }}>
                    <Tooltip
                      showArrow
                      content='A "Temperatura" controla a criatividade das respostas. Com temperatura baixa (0.0 - 0.3), as respostas são mais objetivas e diretas. Com temperatura média (0.4 - 0.7), as respostas ficam equilibradas, criativas e claras. Já com temperatura alta (0.8 - 1.0), as respostas são mais criativas e imprevisíveis.'
                    >
                      <LuCircleHelp color="#FD2264" />
                    </Tooltip>
                  </Box>
                }
              />
            </HStack>
            <HStack w={"100%"} gap={"8"} justifyContent={"space-between"}>
              <InputTextArea
                {...register("greetingMessage")}
                height={"100px"}
                label="Mensagem de saudação"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.greetingMessage}
                optionalText={
                  <Box pl={1} _hover={{ cursor: "help" }}>
                    <Tooltip
                      showArrow
                      content="É a mensagem que o chatbot exibe quando o cliente inicia uma conversa. Ela serve para cumprimentar o usuário, apresentar o bot e estabelecer um tom de conversa adequado, criando uma boa impressão inicial e incentivando a interação."
                    >
                      <LuCircleHelp color="#FD2264" />
                    </Tooltip>
                  </Box>
                }
              />
            </HStack>
          </VStack>
        </VStack>
      </Flex>
    </>
  );
}
