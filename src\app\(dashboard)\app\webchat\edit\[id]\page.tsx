import { Flex } from "@chakra-ui/react";
import EditTabs from "./components/tabs";
import { apiSSR } from "@/services/apiSSR";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";

type PageProps = {
  params: Promise<{ id: string }>;
};
export default async function Page({ params }: PageProps) {
  const { id } = await params;

  const api = await apiSSR();

  const { data } = await api.get<GetOneChatDtoInput>(`/chats/${id}`);

  return (
    <Flex flex={1}>
      <EditTabs data={data} />
    </Flex>
  );
}
