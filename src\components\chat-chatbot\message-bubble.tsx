import { Avatar } from "@/components/ui/avatar";
import { Box, HStack, VStack, Text } from "@chakra-ui/react";
import React from "react";
import { IoCheckmarkDoneOutline, IoCheckmarkOutline } from "react-icons/io5";
import ReactMarkdown from "react-markdown";

type MessageBubbleAIProps = {
  message: string;
  attendantName: string;
  isMine: boolean;
  avatarUrl?: string;
  messageTime: string;
  messageStatus: string;
  customerMessageColor: string | null;
  customerMessageBgColor: string | null;
  attendantMessageColor: string | null;
  attendantMessageBgColor: string | null;
  attendantAvatarColor: string | null;
};

export default function MessageBubbleChatBot({
  message,
  isMine,
  avatarUrl,
  messageTime,
  attendantName,
  messageStatus,
  attendantMessageBgColor,
  attendantMessageColor,
  customerMessageBgColor,
  customerMessageColor,
  attendantAvatarColor,
}: MessageBubbleAIProps) {
  return (
    <>
      {isMine ? (
        <HStack w={"100%"} justifyContent={"flex-end"}>
          <HStack
            flexDir={"row-reverse"}
            alignItems={"start"}
            gap={5}
            maxWidth={"70%"}
            mr={2}
          >
            <VStack position={"relative"}>
              <Box
                position="absolute"
                bottom={0}
                right="-15px"
                top="30px"
                width={0}
                height={0}
                zIndex={0}
                borderLeft="20px solid transparent"
                borderRight="20px solid transparent"
                borderTop="20px solid"
                borderTopColor={customerMessageBgColor || "white"}
              />
              <Box
                bgColor={customerMessageBgColor || "white"}
                p={2.5}
                rounded={10}
              >
                <VStack align={"start"} justify={"space-between"}>
                  <Text color={customerMessageColor || "black"}>{message}</Text>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                    {/* {messageStatus === "delivered" && (
                      <IoCheckmarkOutline color="#FD2264" />
                    )}
                    {messageStatus === "read" && (
                      <IoCheckmarkDoneOutline color="#FD2264" />
                    )}
                    {messageStatus === "pending" && (
                      <IoCheckmarkOutline color="#84767A" />
                    )} */}
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      ) : (
        <HStack w={"100%"} justifyContent={"flex-start"}>
          <HStack alignItems={"start"} gap={5} maxWidth={"80%"} ml={2}>
            <VStack position={"relative"}>
              <Box
                position="absolute"
                bottom={0}
                left="-15px"
                top="30px"
                width={0}
                height={0}
                zIndex={0}
                borderLeft="20px solid transparent"
                borderRight="20px solid transparent"
                borderTop="20px solid"
                borderTopColor={attendantMessageBgColor || "white"}
              />
              <Box
                bgColor={attendantMessageBgColor || "white"}
                p={2.5}
                rounded={10}
                zIndex={1}
              >
                <VStack align={"start"} justify={"space-between"}>
                  <Box color={attendantMessageColor || "black"}>
                    <ReactMarkdown>{message}</ReactMarkdown>
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                    {/* {messageStatus === "delivered" && (
                      <IoCheckmarkOutline color="#FD2264" />
                    )}
                    {messageStatus === "read" && (
                      <IoCheckmarkDoneOutline color="#FD2264" />
                    )}
                    {messageStatus === "pending" && (
                      <IoCheckmarkOutline color="#84767A" />
                    )} */}
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      )}
    </>
  );
}
