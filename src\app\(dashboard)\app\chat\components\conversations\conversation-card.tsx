import { IconBusiness } from "@/components/global/Icon/IconBusiness";
import { Avatar } from "@/components/ui/avatar";
import { queryClient } from "@/services/queryClient";
import { formatMessageDate } from "@/utils/funcs/format-date";
import { formatCellphone } from "@/utils/funcs/format-number-phone";
import { HStack, VStack, Text, Box, Icon } from "@chakra-ui/react";
import Link from "next/link";
import { FaWhatsapp } from "react-icons/fa";
import {
  IoBusiness,
  IoDocumentOutline,
  IoCameraOutline,
  IoLockClosedOutline,
  IoMicOutline,
} from "react-icons/io5";

import { useBreakpointValue } from "@chakra-ui/react";
import { useMediaQuery } from "react-responsive";
type ConversationCardProps = {
  imageUrl?: string;
  name: string;
  lastMessage: string;
  lastMessageDate: string;
  messageType?: 'audio' | 'text' | 'file' | 'image';
  isRead: boolean;
  messageSource: "whatsapp" | "webchat" | "whatsappBusiness";
  whatsappNumber?: string;
  isSelected: boolean;
  secureId: string;
  isArchived?: boolean;
};

export default function ConversationCard({
  imageUrl,
  name,
  lastMessage,
  messageSource,
  isRead,
  messageType,
  lastMessageDate,
  isSelected,
  secureId,
  isArchived,
  whatsappNumber,
}: ConversationCardProps) {
  const isBigScreen = useMediaQuery({ minWidth: 1824 });
  const isMobile = useBreakpointValue({ base: true, md: false });

  const truncatedMessage = lastMessage
    ? lastMessage.length > 20
      ? `${lastMessage.slice(0, 20)}...`
      : lastMessage
    : "Sem mensagens";

  const truncatedName = name
    ? !isBigScreen && name.length > 15
      ? `${name.slice(0, 15)}...`
      : name
    : "Sem nome";


  const lastMessageComponent = () => {
    switch (messageType) {
      case "audio":
        return <IoMicOutline />;
      case "file":
        return <IoDocumentOutline />;
      case "image":
        return <IoCameraOutline />;
      default:
        return truncatedMessage;
    }
  }

  const CardContent = () => (
    <VStack
      w={"100%"}
      p={3}
      borderRadius={isSelected ? 10 : 0}
      gap={0}
      bgColor={isSelected ? "#E0DFE0" : "transparent"}
      _hover={{ bgColor: "#E0DFE0", borderRadius: 10 }}
      borderBottom={"1px solid"}
      borderColor={isSelected ? "transparent" : "#EADBDF"}
      cursor={isMobile ? "default" : "pointer"}
    >
      <HStack
        justifyContent={"space-between"}
        alignItems={"center"}
        position="relative"
        w={"100%"}
      >
        {isArchived && (
          <Box
            position="absolute"
            left="1"
            top="1"
            color="chatPrimary"
          >
            <IoLockClosedOutline size="14px" />
          </Box>
        )}
        {!isRead && (
          <Box
            position="absolute"
            right="2"
            top="2"
            w="8px"
            h="8px"
            borderRadius="full"
            bgColor="#FD2264"
          />
        )}
        <HStack w={"100%"}>
          <Avatar
            bgColor={'#5e5e5e'}
            size={"lg"}
            name={name}
            src={imageUrl}
            children={
              <>
                {messageSource === "whatsapp" ||
                  messageSource === "whatsappBusiness" ? (
                  <Box
                    w={"14px"}
                    h={"14px"}
                    position={"absolute"}
                    bgColor={"green.600"}
                    top={"7"}
                    rounded={"2xl"}
                    left={"7"}
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"center"}
                    p={"2"}
                  >
                    <Icon size={"xs"}>
                      {messageSource === "whatsapp" ? (
                        <FaWhatsapp />
                      ) : (
                        <IconBusiness />
                      )}
                    </Icon>
                  </Box>
                ) : null}
              </>
            }
          />
          <VStack alignItems={"start"} gap={0.3}>
            {whatsappNumber && (
              <Text color={"chatTextColor"} fontSize={"xs"}>
                {formatCellphone(whatsappNumber)}
              </Text>
            )}
            <Text
              fontSize={"sm"}
              fontWeight={"medium"}
              color="black"
            >
              {truncatedName}
            </Text>
            <Text fontSize={"xs"} color="chatCardMessage">
              {lastMessageComponent()}
            </Text>
          </VStack>
        </HStack>
        <VStack alignItems={"center"} gap={0} w={"30%"}>
          {lastMessageDate && (
            <Text fontSize={"xs"} fontWeight={"medium"} letterSpacing={'wider'} color="chatCardMessage">
              {formatMessageDate(lastMessageDate)}
            </Text>
          )}
        </VStack>
      </HStack>
    </VStack>
  );

  return (
    <>
      {isMobile ? (
        <Box
          w="100%"
        >
          <CardContent />
        </Box>
      ) : (
        <Link
          style={{
            width: "100%",
          }}
          href={`/app/chat/${secureId}`}
        >
          <CardContent />
        </Link>
      )}
    </>
  );
}
