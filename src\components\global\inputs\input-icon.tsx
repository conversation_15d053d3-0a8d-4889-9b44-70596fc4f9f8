import {
  Input as ChakraInput,
  InputProps as ChakraInputProps,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  height?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  { name, label, error, startElement, height = "60px", ...rest },
  ref
) => {
  return (
    <Field invalid={!!error} h={height} errorText={error?.message} label={label}>
      <InputGroup w="100%" startElement={startElement}>
        <ChakraInput
          id={name}
          name={name}
          ref={ref}
          bg="white"
          color="chatPrimary"
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: error ? "red.400" : "chatPrimary",
          }}
          _placeholder={{ color: "#B1A0A5" }}
          _focus={{
            borderColor: error ? "red.500" : "chatPrimary",
          }}
          size="md"
          {...rest}
        />
      </InputGroup>
    </Field>
  );
};

export const InputIcon = forwardRef(InputBase);
