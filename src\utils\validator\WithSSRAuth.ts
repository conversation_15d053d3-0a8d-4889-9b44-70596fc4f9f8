"use server";
import { ROLES } from "../types/global/Roles";
import { UserAuthProps } from "../types/global/UserAuth";
import { jwtDecode } from "jwt-decode";
import { ValidateUserRoles } from "./ValidateUserPermissions";
import { validateToken } from "../funcs/validate-token";

type WithSSRAuthOptions = {
  roles?: ROLES[];
  permissions?: string[];
};

const analyze = async (options: WithSSRAuthOptions, cookie: string) => {
  try {
    // First validate the token
    const { isValid, decodedToken, errorMessage } = validateToken(cookie);

    if (!isValid || !decodedToken) {
      console.log(`Token validation failed in WithSSRAuth: ${errorMessage}`);
      return false;
    }

    const { roles, permissions } = options;
    const { activeAccount } = decodedToken;

    // Check role permissions
    if (roles) {
      const hasRole = await ValidateUserRoles(roles, activeAccount.roleSlug);
      if (!hasRole) {
        console.log(`User does not have required role: ${roles.join(', ')}`);
        return false;
      }
    }

    if (permissions) {
      // Adicione a lógica de validação de permissões aqui
    }

    return true;
  } catch (error) {
    console.error("Error in WithSSRAuth:", error);
    return false;
  }
};

export async function WithSSRAuth(options: WithSSRAuthOptions, cookie: string) {
  const result = await analyze(options, cookie);
  return result;
}
