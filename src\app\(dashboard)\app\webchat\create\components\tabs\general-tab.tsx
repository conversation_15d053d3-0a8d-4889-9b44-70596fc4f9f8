import { Input } from "@/components/global/inputs/input";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import HeaderTab from "@/components/tabs/header";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toaster } from "@/components/ui/toaster";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { InputUpload } from "@/components/global/inputs/input-file";
import { InputSelect } from "@/components/global/inputs/input-select";
import useListChatBots from "@/hook/chatbots/useListChatbots";

const GeneralTabSchema = yup.object().shape({
  name: yup.string().required("Nome do WebChat é obrigatório"),
  description: yup.string().required("Descrição é obrigatória"),
  welcomeMessage: yup
    .string()
    .required("Mensagem de boas-vindas é obrigatória"),
  imageUpload: yup.mixed().required("Imagem é obrigatória"),
  chatBotSecureId: yup.array(yup.string()).optional(),
});

type GeneralTabFormData = yup.InferType<typeof GeneralTabSchema>;

type GeneralTabProps = {
  secureId?: string;
  setSecureId: (value: string) => void;
  accountSecureId: string;
};

export default function GeneralTab({
  secureId,
  setSecureId,
  accountSecureId,
}: GeneralTabProps) {
  const { data, isLoading } = useListChatBots();

  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<GeneralTabFormData>({
    resolver: yupResolver(GeneralTabSchema),
  });

  const handleUpdate = async (data: GeneralTabFormData) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("description", data.description);
    formData.append("accountSecureId", accountSecureId);
    if (data.welcomeMessage) {
      formData.append("welcomeMessage", data.welcomeMessage);
    }
    formData.append("chatBotSecureId", data.chatBotSecureId?.[0] ?? "");

    formData.append("file", data.imageUpload as any);
    if (!secureId) {
      const { data: chatSecureId } = await api.post<{ chatSecureId: string }>(
        `/chats`,
        formData
      );
      setSecureId(chatSecureId.chatSecureId);
      toaster.success({
        title: "Sucesso",
        description: "WebChat criado com sucesso",
      });
    } else {
      await api.put(`/chats/${secureId}`, formData);
      toaster.success({
        title: "Sucesso",
        description: "WebChat alterado com sucesso",
      });
    }
  };

  return (
    <Flex
      height={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      <VStack
        flex={1}
        alignItems={"flex-start"}
        as={"form"}
        onSubmit={handleSubmit(handleUpdate)}
      >
        <HeaderTab
          buttonTitle="Salvar Alterações"
          title="Geral"
          hrefBack="/app/config#webchats"
          isSubmit={isSubmitting}
        />
        <HStack w={"100%"} justifyContent={"space-between"} mt={4}>
          <VStack flex={1} gap={8} h={"100%"} alignItems={"flex-start"}>
            <Input
              {...register("name")}
              label="Nome do WebChat"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              color={"#84767A"}
              error={errors.name}
            />
            <Input
              {...register("description")}
              label="Descrição"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              color={"#84767A"}
              error={errors.description}
            />
            <InputTextArea
              {...register("welcomeMessage")}
              height={"150px"}
              label="Mensagem de boas-vindas"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              error={errors.welcomeMessage}
            />
            <InputSelect
              {...register("chatBotSecureId")}
              control={control}
              label="Selecione o ChatBot"
              placeholder="Selecione o ChatBot"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              error={errors.chatBotSecureId as any}
              itensList={
                data?.data.map((chatBot) => ({
                  label: chatBot.name,
                  value: chatBot.secureId,
                })) || []
              }
            />
          </VStack>
          <VStack flex={1} gap={5}>
            <Text color={"chatTextColor"} px={6}>
              Faça upload da logo que será exibida no botão. A imagem deve ter
              exatamente 44x44 pixels e com fundo transparente.
            </Text>
            <InputUpload
              watch={watch}
              {...register("imageUpload")}
              control={control}
              error={errors.imageUpload}
            />
          </VStack>
        </HStack>
      </VStack>
    </Flex>
  );
}
