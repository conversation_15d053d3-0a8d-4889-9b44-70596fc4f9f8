export type GetChatsDto = {
  secureId: string;
  name: string;
  welcomeMessage: string | null;
  avatarUrl: string | null;
  description: string | null;
  chatBgColor: string | null;
  inputChatBgColor: string | null;
  inputChatTextColor: string | null;
  chatButtonColor: string | null;
  chatIconColor: string | null;
  avatarBgColor: string | null;
  webchatButtonBgColor: string | null;
  customerMessageBubbleColor: string | null;
  customerMessageTextColor: string | null;
  attendantMessageBubbleColor: string | null;
  attendantMessageTextColor: string | null;
  messageTimeColor: string | null;
  messageStatusColor: string | null;
  isActive: boolean;
  isDeleted: boolean;
  updatedAt: Date;
  createdAt: Date;
};

export type GetOneChatDtoInput = {
  secureId: string;
  name: string;
  welcomeMessage: string | null;
  description: string | null;
  chatBgColor: string | null;
  inputChatBgColor: string | null;
  inputChatTextColor: string | null;
  chatButtonColor: string | null;
  chatIconColor: string | null;
  webchatButtonBgColor: string | null;
  avatarBgColor: string | null;
  customerMessageBubbleColor: string | null;
  customerMessageTextColor: string | null;
  attendantMessageBubbleColor: string | null;
  attendantMessageTextColor: string | null;
  messageTimeColor: string | null;
  messageStatusColor: string | null;
  isActive: boolean;
  isDeleted: boolean;
  updatedAt: Date;
  createdAt: Date;
  account: {
    secureId: string;
  };
  upload: {
    secureId: string;
    urlCdn: string;
  };
  chatbot?: {
    secureId: string;
    isAI: boolean;
    name: string
  };
};
