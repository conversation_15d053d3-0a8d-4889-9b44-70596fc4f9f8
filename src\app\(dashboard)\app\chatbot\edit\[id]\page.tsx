import { apiSSR } from "@/services/apiSSR";
import { GetChatBotsDto } from "@/utils/types/DTO/chat-bots.dto";
import { Flex } from "@chakra-ui/react";
import EditTabs from "./components/tabs";
// import EditTabs from "./components/tab";

type PageProps = {
  params: Promise<{ id: string }>;
};

export default async function Page({ params }: PageProps) {
  const { id } = await params;

  const api = await apiSSR();

  const { data } = await api.get<GetChatBotsDto>(`/chatbots/${id}`);

  return (
    <Flex flex={1}>
      <EditTabs chatBotData={data} />
    </Flex>
  );
}
