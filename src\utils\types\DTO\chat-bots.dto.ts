import {
  EnumChatBotEmotionalTone,
  EnumChatBotMood,
  EnumChatBotResponseStyle,
} from "@/utils/enums/chat-bots.enums";

export type GetChatBotsDto = {
  secureId: string;
  name: string;
  isAI: boolean;
  isLeadCaptureActive: boolean;

  leadTriggerMessageLimit: number;

  leadCaptureMessage: string;
  leadCaptureThankYouMessage: string;
  temperature: number;

  leadCaptureJson?: {
    collectName?: boolean;
    collectEmail?: boolean;
    collectPhone?: boolean;
    collectCPF?: boolean;
  };

  emotionalTone: EnumChatBotEmotionalTone;
  mood: EnumChatBotMood;
  responseStyle: EnumChatBotResponseStyle;
  responseSize: EnumChatBotResponseStyle;
  greetingMessage: string;

  tokenUsage: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};
