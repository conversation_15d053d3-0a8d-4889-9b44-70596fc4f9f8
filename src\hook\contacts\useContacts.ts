import { api } from "@/services/api";
import { ListContactOutputDTO } from "@/utils/types/DTO/contact.dto";

import { useQuery } from "@tanstack/react-query";

async function listContact(page: number, searchTerm?: string) {
  const limit = 6;
  const { data } = await api.get<ListContactOutputDTO>(`/contact?page=${page}&limit=${limit}`, { 
    params: {
      search: searchTerm
    }
  });
  return data;
}

export default function useContact(page: number, searchTerm?: string) {
  return useQuery({
    queryKey: ["list-contacts", page, searchTerm],
    queryFn: async () => await listContact(page, searchTerm),
  });
}
