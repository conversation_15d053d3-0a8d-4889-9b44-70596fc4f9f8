import { Field } from "@/components/ui/field";
import { Controller } from "react-hook-form";
import { Checkbox } from "@/components/ui/checkbox";

type AllPermissionCheckboxProps = {
  name: string;
  label: string;
  control: any;
  invalid: boolean;
};

export default function AllPermissionCheckbox({
  control,
  invalid,
  name,
  label,
}: AllPermissionCheckboxProps) {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <Field disabled={field.disabled} invalid={invalid}>
          <Checkbox
            checked={field.value}
            onCheckedChange={({ checked }) => field.onChange(checked)}
          >
            {label}
          </Checkbox>
        </Field>
      )}
    />
  );
}
