"use client";

import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import { useEffect, useState } from "react";
import StreamableChat from "./streamable-chat";
import Chat from "./chat";
import { useSocket } from "@/providers/SocketProvider";
import { MessagesDto } from "@/utils/types/DTO/chat-sessions.dto";
import { useChatHistory } from "@/hook/_public/chat-history/useChatHistory";
import { Center, Spinner } from "@chakra-ui/react";
import { toaster } from "@/components/ui/toaster";

type ChatOrchestratorProps = {
  isAi: boolean;
  chatSecureId: string;
  chatData: GetOneChatDtoInput;
  messagesHistory?: MessagesDto[];
  sessionSecureId: string;
  userSecureId: string;
};

export default function ChatOrchestrator({
  chatSecureId,
  chatData,
  sessionSecureId,
  userSecureId,
  isAi,
}: ChatOrchestratorProps) {
  const { data, isLoading, refetch } = useChatHistory(sessionSecureId);
  const { wsServiceRef, isConnected } = useSocket();
  const [isAI, setIsAI] = useState(isAi);

  // Register event handler
  useEffect(() => {
    if (!isConnected) return;    
    wsServiceRef.joinRoom(userSecureId, sessionSecureId)
    
    wsServiceRef.attendantAssignedChat(async (isChanged) => {
      setIsAI(!isChanged);
      await refetch();

      toaster.create({
        title: "Atendimento humano iniciado",
        description: "A assistência via IA foi finalizada. Um atendente humano assumiu seu atendimento.",
        type: "info",
      })
    });

  }, [wsServiceRef, refetch, isConnected]);

  useEffect(() => {
    setIsAI(isAi);
  }, [isAi]);

  return (
    <>
      {isLoading ? (
        <Center h="100%" w="100%">
          <Spinner color={"chatPrimary"} />
        </Center>
      ) : (
        <>
          {isAI ? (
            <StreamableChat
              secureId={chatSecureId}
              chatData={chatData}
              sessionSecureId={sessionSecureId}
              userSecureId={userSecureId}
              chatHistory={data}
            />
          ) : (
            <Chat
              secureId={chatSecureId}
              chatData={chatData}
              sessionSecureId={sessionSecureId}
              userSecureId={userSecureId}
              chatHistory={data}
            />
          )}
        </>
      )}
    </>
  );
}
