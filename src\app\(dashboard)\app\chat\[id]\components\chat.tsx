"use client";

import ChatFooterWebSocket from "@/components/global/chat/chat-footer-websocket";
import MessageBubble from "@/components/global/chat/message-bubble";
import { useAuthContext } from "@/providers/AuthProvider";
import {
  Flex,
  Spinner,
  VStack,
  Button,
  Text,
  Box,
  HStack,
  IconButton,
} from "@chakra-ui/react";
import truncateText from "@/utils/funcs/truncate-text";
import BasicModal from "@/components/global/modal/basic-modal";
import { IoCloseOutline } from "react-icons/io5";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { useSocket } from "@/providers/SocketProvider";
import ChatMessageList from "./ChatMessageList";
import { useChatMessages } from "@/hook/chat-session/useChatMessages";
import { useRef, useState, useEffect, useCallback } from "react";
import { toaster } from "@/components/ui/toaster";

interface Message {
  secureId: string;
  message: string;
  isMine: boolean;
  messageTime: string;
  avatarUrl?: string;
  type?: "text" | "audio" | "file" | "image";
  urlFile?: string;
  attendantName?: string;
  messageCount: number;
  replyTo?: string;
  replyMessage?: string;
}

interface ReplyToMessage {
  secureId: string;
  message: string;
}

type ChatProps = {
  secureId: string;
};

export default function Chat({ secureId }: ChatProps) {
  const { sessionInfo } = useChatSessionContext();
  const { user } = useAuthContext();
  const [openModal, setOpenModal] = useState(false);
  const { wsServiceRef, isConnected } = useSocket();
  const chatEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const isFirstLoad = useRef(true);
  const isFetchingMore = useRef(false);

  const {
    messages,
    isConnecting,
    isAI,
    replyTo,
    setReplyTo,
    cancelReply,
    prevScrollHeight,
    setPrevScrollHeight,
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    refetch,
    setIsAI,
  } = useChatMessages({
    secureId,
    wsServiceRef,
    user,
    sessionInfo,
    isConnected,
    scrollToBottom: () =>
      chatEndRef.current?.scrollIntoView({ behavior: "smooth" }),
  });

  // Scroll para o final ao entrar na conversa
  useEffect(() => {
    if (
      !isLoading &&
      !isConnecting &&
      messages.length > 0 &&
      isFirstLoad.current
    ) {
      isFirstLoad.current = false;
      setTimeout(() => {
        chatEndRef.current?.scrollIntoView({ behavior: "auto" });
      }, 0);
    }
  }, [isLoading, isConnecting, messages.length]);

  // Scroll para o final ao enviar mensagem (se usuário estiver no final)
  const scrollToBottom = useCallback(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // Detectar scroll no topo para buscar mais mensagens
  const handleScroll = useCallback(async () => {
    const container = chatContainerRef.current;
    if (!container || isFetchingMore.current || !hasNextPage) return;
    if (container.scrollTop <= 40) {
      isFetchingMore.current = true;
      const prevHeight = container.scrollHeight;
      await fetchNextPage();
      // Aguarda o scrollHeight mudar antes de ajustar o scrollTop
      const waitForHeight = () => {
        if (container.scrollHeight !== prevHeight) {
          container.scrollTop =
            container.scrollHeight - prevHeight + container.scrollTop;
          isFetchingMore.current = false;
        } else {
          requestAnimationFrame(waitForHeight);
        }
      };
      waitForHeight();
    }
  }, [fetchNextPage, hasNextPage]);

  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  // Scroll automático ao receber nova mensagem só se usuário estiver no final
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container || isFirstLoad.current) return;
    const isAtBottom =
      container.scrollHeight - container.scrollTop - container.clientHeight <
      80;
    if (isAtBottom) {
      scrollToBottom();
    }
  }, [messages.length, scrollToBottom]);

  const handleSendMessage = async (
    content: string,
    file?: { file: File; type: "file" | "image" | "audio" }
  ) => {
    if (!wsServiceRef?.isConnected()) {
      toaster.create({
        title: "Erro ao enviar mensagem",
        description: "Você está desconectado. Reconectando...",
        type: "error",
        duration: 3000,
      });
      return;
    }

    try {
      await wsServiceRef.sendMessage({
        chatId: sessionInfo?.chatId,
        content,
        type: "text",
        messageDirection: "sent",
        isAttendant: true,
        sessionId: secureId,
        userId: user!.subject,
        replyTo: replyTo?.secureId,
        file: file?.file ?? undefined,
      });

      cancelReply();
    } catch (error) {
      toaster.create({
        title: "Erro ao enviar mensagem",
        description: "Não foi possível enviar sua mensagem. Tente novamente.",
        type: "error",
        duration: 3000,
      });
    }
  };

  const handleInterruptAI = async () => {
    if (!wsServiceRef?.isConnected()) {
      toaster.create({
        title: "Erro ao interromper IA",
        description: "Você está desconectado. Reconectando...",
        type: "error",
        duration: 3000,
      });
      return;
    }

    try {
      await wsServiceRef.attendantInterrupt(secureId, user!.subject);
      toaster.create({
        title: "Solicitação enviada",
        description: "A IA foi interrompida com sucesso.",
        type: "success",
        duration: 3000,
      });
      setIsAI(false);
    } catch (error) {
      toaster.create({
        title: "Erro ao interromper IA",
        description: "Não foi possível interromper a IA. Tente novamente.",
        type: "error",
        duration: 3000,
      });
    } finally {
      setOpenModal(false);
    }
  };

  return (
    <>
      {isLoading || isConnecting ? (
        <Flex flex={1} w="100%" justify="center" align="center" py={2}>
          <Spinner size="sm" color="chatPrimary" />
        </Flex>
      ) : (
        <>
          <Flex position="relative" w="100%">
            {isFetchingNextPage && (
              <Flex
                position="absolute"
                top={2}
                left={0}
                right={0}
                justify="center"
                zIndex={10}
              >
                <Spinner size="sm" color="chatPrimary" />
              </Flex>
            )}
          </Flex>
          <Flex
            ref={chatContainerRef}
            flex={1}
            px={2}
            gap={5}
            pb={2}
            flexDir="column"
            alignItems="flex-end"
            h={{ base: "calc(100vh - 180px)", md: "calc(100vh - 255px)" }}
            overflowY="auto"
            css={{
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-track": {
                width: "6px",
                marginBottom: "10px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#D6D6D6",
                borderRadius: "24px",
                "&:hover": {
                  background: "#A6A6A6",
                },
              },
            }}
          >
            <ChatMessageList
              messages={messages}
              clientName={sessionInfo?.customerName ?? ""}
              onReply={setReplyTo}
              sessionInfo={sessionInfo}
            />
            <div ref={chatEndRef} />
          </Flex>
          {isAI ? (
            <Flex w={"100%"} justifyContent={"center"} zIndex={100}>
              <Button
                onClick={() => setOpenModal(true)}
                bgColor={"chatPrimary"}
                borderRadius="full"
                color={"white"}
                size="lg"
                boxShadow="0px 4px 10px rgba(0, 0, 0, 0.2)"
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "0px 6px 15px rgba(0, 0, 0, 0.3)",
                }}
                _active={{
                  transform: "translateY(0)",
                  boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                }}
                transition="all 0.2s"
              >
                Interromper I.A
              </Button>
            </Flex>
          ) : (
            <>
              {replyTo && (
                <Box w="100%" position={"relative"}>
                  <Box
                    w="100%"
                    borderTopRadius="md"
                    bg="gray.50"
                    p={2}
                    mr={2}
                    position="absolute"
                    bottom={0}
                    borderTop="1px solid"
                    borderLeft="1px solid"
                    borderRight="1px solid"
                    borderColor="gray.200"
                    zIndex={100}
                  >
                    <HStack gap={2} align="flex-start" top={-10}>
                      <Box
                        w={1}
                        h="100%"
                        bg="chatPrimary"
                        borderRadius="full"
                        mr={2}
                      />
                      <VStack align="start" flex={1} gap={0}>
                        <Text
                          fontWeight="bold"
                          fontSize="sm"
                          color="chatPrimary"
                        >
                          Respondendo mensagem
                        </Text>
                        <Text
                          fontSize="sm"
                          color="gray.600"
                          maxW="calc(100% - 30px)"
                          overflow="hidden"
                          textOverflow="ellipsis"
                          whiteSpace="nowrap"
                        >
                          {truncateText(replyTo.message, 60)}
                        </Text>
                      </VStack>
                      <IconButton
                        aria-label="Cancel reply"
                        size="sm"
                        color="chatPrimary"
                        _hover={{
                          bg: "chatPrimary",
                          color: "white",
                        }}
                        variant={"ghost"}
                        onClick={cancelReply}
                      >
                        <IoCloseOutline />
                      </IconButton>
                    </HStack>
                  </Box>
                </Box>
              )}
              <ChatFooterWebSocket
                onSendMessage={handleSendMessage}
                buttonBgColor={null}
                inputBgColor={null}
                inputMessageColor={null}
              />
            </>
          )}

          <BasicModal
            open={openModal}
            size="sm"
            setOpen={setOpenModal}
            cancelText="Cancelar"
            handleDelete={handleInterruptAI}
            deleteText="Interromper"
            placement="center"
            isSubmitting={false}
            children={
              <VStack gap={5} m={5}>
                <Text fontSize={"xl"} fontWeight={"bold"} color="red.500">
                  Interromper IA
                </Text>
                <Text fontSize={"md"} fontWeight={"bold"}>
                  Você tem certeza que deseja interromper a IA? Essa ação não
                  pode ser desfeita.
                </Text>
              </VStack>
            }
          />
        </>
      )}
    </>
  );
}
