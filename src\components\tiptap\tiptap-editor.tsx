'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from "@tiptap/extension-text-align"
import Highlight from "@tiptap/extension-highlight"
import { HStack, VStack, Box } from '@chakra-ui/react'
import MenuBar from './menu-bar'
import { useEffect, useMemo } from 'react'
import './tiptap-editor.css'

interface TipTapEditorProps {
	content: string;
	onChange: (content: string) => void;
}

export default function TipTapEditor ({content, onChange}: TipTapEditorProps) {
	const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: true, 
          HTMLAttributes: {
            class: 'bullet-list',
          },
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
          HTMLAttributes: {
            class: 'ordered-list',
          },
        },
        listItem: {
          HTMLAttributes: {
            class: 'list-item',
          },
        },
        heading: {
          levels: [1, 2, 3],
        },
        bold: {
          HTMLAttributes: {
            class: 'font-bold',
          },
        },
        italic: {
          HTMLAttributes: {
            class: 'italic',
          },
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Highlight,
    ],
    content: content,
    editorProps: {
      attributes: {
        class: "tiptap-editor",
      },
    },
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  return (
    <VStack flex={1} w="100%" h="100%">
      <MenuBar editor={editor} />
      <Box maxH="100%" w="100%" h="100%" position="relative" >
        <EditorContent 
          editor={editor} 
          style={{
            color: "#000",
						height: "100%",
            maxHeight: "100%",

          }} 
        />
      </Box>
    </VStack>
  )
}

