"use client";
import { useGetAccount } from "@/hook/accounts/useGetAccount";
import {
  Flex,
  Tabs,
  Text,
} from "@chakra-ui/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { IoArrowBackOutline } from "react-icons/io5";
import AccountSubscriptionTab from "./components/account-subscription-tab";
import AccountTransactionTab from "./components/account-transaction-tab";
import AccountUserTab from "./components/account-user-tab";
import AccountTab from "./components/account-tab";

export default function Account() {
  const { id: secureId } = useParams();
  const { data: account } = useGetAccount(secureId as string);

  return (
    <Flex flex={1} h="100%" w="100%" flexDirection="column" marginTop={{base: "12rem", md: 0}}>
      <Flex
        flex={1}
        p={{ base: 2, md: 5 }}
        m={{ base: 0, md: 5 }}
        gap={2}
        borderRadius={{ base: 0, md: 15 }}
        bgColor="backgroundCard"
        flexDirection="column"
        mb={4}
      >
        <Text fontSize="xl" fontWeight={"bold"} color={"white"}>Empresa: {account?.companyName}</Text>
        <Tabs.Root defaultValue="account" variant={"line"} w={"100%"}>
          <Tabs.List>
            <Link href="/backoffice/accounts" passHref>
              <Tabs.Trigger value="back">
                <IoArrowBackOutline size={22}/>
              </Tabs.Trigger>
            </Link>
            <Tabs.Trigger value="account">
              Conta
            </Tabs.Trigger>
            <Tabs.Trigger value="users">
              Usuários
            </Tabs.Trigger>
            <Tabs.Trigger value="subscriptions">
              Assinaturas
            </Tabs.Trigger>
            <Tabs.Trigger value="transactions">
              Transações
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="account">
            <AccountTab accountSecureId={secureId as string}/>
          </Tabs.Content>
          <Tabs.Content value="users">
            <AccountUserTab accountSecureId={secureId as string}/>
          </Tabs.Content>
          <Tabs.Content value="subscriptions">
            <AccountSubscriptionTab accountSecureId={secureId as string}/>
          </Tabs.Content>
          <Tabs.Content value="transactions">
            <AccountTransactionTab accountSecureId={secureId as string}/>
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </Flex>
  );
}
