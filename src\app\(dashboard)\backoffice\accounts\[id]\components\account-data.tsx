import { useGetAccount } from "@/hook/accounts/useGetAccount";
import { Flex, Text } from "@chakra-ui/react";

type AccountDataTabProps = {
  accountSecureId: string
}

export default function AccountData({accountSecureId}: AccountDataTabProps) {
  const { data: accountData } = useGetAccount(accountSecureId as string);

  return (
    <Flex>
      <Text>
        {accountData?.companyName}
      </Text>
    </Flex>
  )
}