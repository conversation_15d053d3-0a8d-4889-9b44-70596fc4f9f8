import { Box, Flex, Text } from "@chakra-ui/react";

export default function SelectChatComponent() {
  return (
    <Flex
      h="100%"
      justifyContent={"center"}
      align={"center"}
      rounded={"xl"}
      p={4}
      overflow="hidden"
      direction="column"
    >
      <Flex
        direction="column"
        alignItems="center"
        justifyContent="center"
        gap={4}
        maxW="300px"
      >
        {/* Chat bubble placeholder */}
        <Flex w="100%" position="relative" mb={8}>
          {/* Left side - attendant */}
          <Flex alignItems="flex-start" position="relative" maxW="60%">
            {/* Circle avatar */}
            <Box w="30px" h="30px" borderRadius="full" bg="gray.300" mr={2} />
            {/* Message bubble */}
            <Box bg="gray.300" p={3} borderRadius="xl" position="relative">
              <Box
                position="absolute"
                left="-10px"
                top="10px"
                width={0}
                height={0}
                borderTop="10px solid transparent"
                borderBottom="10px solid transparent"
                borderRight="10px solid"
                borderRightColor="gray.300"
              />
              <Box w="120px" h="20px" />
            </Box>
          </Flex>

          {/* Right side - user */}
          <Flex
            alignItems="flex-start"
            position="absolute"
            right={0}
            top="50px"
            maxW="60%"
            flexDirection="row-reverse"
          >
            {/* Circle avatar */}
            <Box w="30px" h="30px" borderRadius="full" bg="gray.300" ml={2} />
            {/* Message bubble */}
            <Box bg="gray.300" p={3} borderRadius="xl" position="relative">
              <Box
                position="absolute"
                right="-10px"
                top="10px"
                width={0}
                height={0}
                borderTop="10px solid transparent"
                borderBottom="10px solid transparent"
                borderLeft="10px solid"
                borderLeftColor="gray.300"
              />
              <Box w="120px" h="20px" />
            </Box>
          </Flex>
        </Flex>

        {/* Text */}
        <Text color="pink.500" fontSize="md" fontWeight="medium" pt={2}>
          Selecione uma conversa
        </Text>
      </Flex>
    </Flex>
  );
}
