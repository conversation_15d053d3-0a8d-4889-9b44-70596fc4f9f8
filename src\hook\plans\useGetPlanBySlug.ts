import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";

type GetPlanBySlugDto = {
  attendantsLimit: number;
  chatbotsLimit: number;
  createdAt: string;
  description: string;
  details: string;
  efiIsDeleted: boolean;
  efiPlanId: number;
  iaMessagesLimit: number;
  interval: number;
  isActive: boolean;
  knowledgeBaseLimit: number;
  name: string;
  price: string;
  secureId: string;
  slug: string;
  trialDays: number;
  updatedAt: string;
  whatsappNumberLimit: number;
};

async function getPlanBySlug(slug: string) {
  const { data } = await api.get<GetPlanBySlugDto>(`/plans/bySlug/${slug}`);

  return data;
}

export default function useGetPlanBySlug(slug: string) {
  return useQuery({
    queryFn: async () => await getPlanBySlug(slug),
    queryKey: ["plan", slug],
    refetchInterval: false,
  });
}
