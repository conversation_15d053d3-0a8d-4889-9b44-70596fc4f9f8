"use client";
import CardUser from "@/components/cards/card-user";
import { But<PERSON> } from "@/components/ui/button";
import { useGetUsersBackoffice } from "@/hook/users/useGetUserBackoffice";
import { Flex, HStack, Text, Spinner } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";
import { useState } from "react";
import ModalCreateUserBackoffice from "./components/modal-create-user-backoffice";
import { BACKOFFICEUSERPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import Pagination from "@/components/global/pagination/pagination";
export default function BackofficeUsers() {
  const [page, setPage] = useState(1);
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const { data: users } = useGetUsersBackoffice(page);
  const hasCreatePermission = HasBackofficePermission(BACKOFFICEUSERPERMISSIONS.CREATE)

  return (
    <>
      <BackofficePageContainer>
        <Flex direction="column" gap={4} w="100%">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Usuários
          </Text>
          <HStack justifyContent="space-between" w="100%" alignItems={"center"}>
            <Text fontSize="md" color="gray.400">Total de usuários: {users?.meta.totalItems}</Text>
            {hasCreatePermission ? (
              <Button
                size="sm"
                color={"white"}
                bg={"chatPrimary"}
                borderRadius={"full"}
                colorScheme={"green"}
                _hover={{
                  transition: "0.3s",
                  bg: "gray.100",
                  color: "chatPrimary",
                }}
                onClick={() => setOpenCreateModal(true)}
              >
                Adicionar Usuário
              </Button>
            ) : null}
          </HStack>
          <Flex
            direction="column"
            flex={1}
            gap={4}
            overflow="auto"
            maxH="calc(100vh - 200px)"
            position="relative"
            css={{
              boxSizing: "border-box",
              paddingRight: "8px", 
              "&::-webkit-scrollbar": { width: "6px" },
              "&::-webkit-scrollbar-track": { background: "transparent" },
              "&::-webkit-scrollbar-thumb": {
                background: "rgba(85, 85, 85, 0.4)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                background: "rgba(102, 102, 102, 0.6)",
              },
            }}
          >
            {users !== undefined && users.data.length > 0 ? (
              users.data.map((user) => (
                <CardUser
                  key={user.secureId}
                  userSecureId={user.secureId}
                  userName={user.name}
                  userIsActive={user.isActive}
                />
              ))
            ) : (
              <Spinner color="white" />
            )}
          </Flex>
          {users &&
            <Pagination
              totalItems={users.meta.totalItems}
              itemsPerPage={users.meta.itemsPerPage}
              page={page}
              setPage={setPage}
              position={"absolute"}
              bottom={8}
              right={8}
              color="white"
              hoverColor="backgroundCard"
            />
          }
        </Flex>
      </BackofficePageContainer>
      {hasCreatePermission ? (
        <ModalCreateUserBackoffice
          openModal={openCreateModal}
          setOpenModal={setOpenCreateModal}
        />
      ) : null}
    </>
  );
}