"use client";
import { Provider } from "@/providers/provider";
import { fonts } from "@/styles/font";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning={true} lang="pt-br">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={fonts.inter.className}
        style={{
          overflowX: "hidden",
        }}
      >
        <Provider>{children}</Provider>
      </body>
    </html>
  );
}
