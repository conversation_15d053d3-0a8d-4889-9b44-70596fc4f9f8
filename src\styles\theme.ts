import { createSystem, defaultConfig, defineConfig } from "@chakra-ui/react";

// sm 480px
// md: 768px
// lg: 1024px
// xl: 1280px
// "2xl": 1536px

export const theme = defineConfig({
  theme: {
    tokens: {
      fonts: {
        body: { value: "Inter" },
      },
      colors: {
        primary: { value: "#6B8AFD" },
        background: { value: "#1e1e1e" },
        backgroundCard: { value: "#3A3A3A" },
        text: { value: "#84767A" },
        chatPrimary: { value: "#FD2264" },
        chatLoginCardBackground: { value: "#FFF4F7" },
        chatCardBackground: { value: "#F2F2F2" },
        chatTextColor: { value: "#84767A" },
        chatBackground: { value: "#E2E2E2" },
        chatCardMessage: { value: "#907E83" },
      },
    },
  },
  globalCss: {
    li: {
      listStyle: "disc",
      listStylePosition: "inside",
      lineHeight: "1.6rem",
    },
  },
});

export default createSystem(defaultConfig, theme);
