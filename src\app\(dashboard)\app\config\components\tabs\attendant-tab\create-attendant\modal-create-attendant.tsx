import { PermissionsCheckbox } from "@/components/global/checkbox/permissions-checkbox";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { InputPassword } from "@/components/global/inputs/input-password";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import useAttendantPermissions from "@/hook/permissions/useAttendantPermissions";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Flex, HStack, Tabs } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { useController, useForm } from "react-hook-form";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ser<PERSON>og } from "react-icons/lu";
import * as yup from "yup";
import { Switch } from "@/components/ui/switch";

type ModalCreateAttendantProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
};

const createAttendantSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  cellPhone: yup.string(),
  password: yup.string().required("Senha é obrigatória"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem coincidir")
    .required("Confirmação de senha é obrigatória"),
  cpf: yup.string().required("CPF é obrigatório"),
  hasAllPermissions: yup.string().optional(),
  participatesInRotation: yup.boolean().optional(),
  permissions: yup.array().of(yup.string()).optional(),
});

type CreateAttendantFormData = yup.InferType<typeof createAttendantSchema>;

export default function ModalCreateAttendant({
  openModal,
  setOpenModal,
}: ModalCreateAttendantProps) {
  const { data, isLoading } = useAttendantPermissions();
  const [permissionsItems, setPermissionsItems] = useState<
    { label: string; value: string }[]
  >([]);

  const cpfMask = "999.999.999-99";
  const phoneMask = "+99 (99) 99999-9999[9]";

  useEffect(() => {
    if (data) {
      setPermissionsItems(
        data.permissions.map((permission) => ({
          label: permission.description,
          value: permission.secureId,
        }))
      );
    }
  }, [data]);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateAttendantFormData>({
    resolver: yupResolver(createAttendantSchema),
  });

  const permissions = useController({
    control,
    name: "permissions",
    defaultValue: [],
  });

  const participatesInRotationField = useController({
    control,
    name: "participatesInRotation",
  });

  const createAttendant = useMutation({
    mutationFn: async (data: CreateAttendantFormData) => {
      const { permissions, hasAllPermissions, ...postData } = data;

      await api.post("/register/attendant", {
        permissionsSecureIds: permissions,
        hasAllPermissions: hasAllPermissions === "all" ? true : undefined,
        ...postData,
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Atendente Cadastrado com Sucesso.",
        title: "Atendente Cadastrado!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["listAttendants"],
      });
      setOpenModal(false);
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: CreateAttendantFormData) => {
    try {
      createAttendant.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <BasicModal
      open={openModal}
      size="xl"
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Cadastrar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Cadastrar novo atendente"
      placement="top"
      children={
        <Tabs.Root defaultValue="user" colorPalette={"pink"} size={"lg"}>
          <Tabs.List borderColor={"chatTextColor"}>
            <Tabs.Trigger
              value="user"
              border={"none"}
              _selected={{
                color: "chatPrimary",
              }}
            >
              <LuUser />
              Usuário
            </Tabs.Trigger>
            <Tabs.Trigger
              value="permissions"
              _selected={{
                color: "chatPrimary",
              }}
            >
              <LuUserCog />
              Permissões
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="user">
            <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
              <Input
                label="Nome"
                placeholder="Digite o nome do atendente"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("name")}
                error={errors.name}
              />
              <Input
                label="Email"
                placeholder="Digite o email do atendente"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("email")}
                error={errors.email}
              />
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <Input
                  type="password"
                  label="Senha"
                  placeholder="Digite o senha do atendente"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("password")}
                  error={errors.password}
                />
                <Input
                  type="password"
                  label="Confirmação da Senha"
                  placeholder="Digite a senha novamente"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("confirmPassword")}
                  error={errors.confirmPassword}
                />
              </HStack>
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <InputMaskIcon
                  label="CPF"
                  mask={cpfMask}
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o CPF do atendente"
                  size={"md"}
                  {...register("cpf")}
                  error={errors.cpf}
                />
                <InputMaskIcon
                  label="Celular"
                  mask={phoneMask}
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o número do atendente"
                  size={"md"}
                  {...register("cellPhone")}
                  error={errors.cellPhone}
                />
              </HStack>
              <HStack w={"100%"} justifyContent={"flex-start"}>
                <Switch
                  checked={participatesInRotationField.field.value}
                  onCheckedChange={({ checked }) =>
                    participatesInRotationField.field.onChange(checked)
                  }
                >
                  Participa da Rotação de Atendimento
                </Switch>
              </HStack>
            </Flex>
          </Tabs.Content>
          <Tabs.Content value="permissions">
            <Box position={"relative"} w={"100%"} h={"100%"}>
              <Checkbox
                size={"md"}
                value={"all"}
                key={"all"}
                position={"absolute"}
                top={0}
                right={0}
                {...register("hasAllPermissions")}
              >
                Conceder todas as permissões
              </Checkbox>
              <PermissionsCheckbox
                invalid={!!errors.permissions}
                onValueChange={permissions.field.onChange}
                value={permissions.field.value as string[] | undefined}
                name={permissions.field.name}
                label="Permissões"
                items={permissionsItems}
              />
            </Box>
          </Tabs.Content>
        </Tabs.Root>
      }
    />
  );
}
