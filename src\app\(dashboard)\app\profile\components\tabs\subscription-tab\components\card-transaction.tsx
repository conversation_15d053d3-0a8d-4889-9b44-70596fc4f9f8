import { TransactionStatus } from "@/utils/types/transactions/transaction-status";
import { Badge, Box, Flex, Separator, Text, VStack } from "@chakra-ui/react";

type CardAccountTransactionPros = {
  transactionAmount: string,
  transactionStatus: TransactionStatus,
  transactionPayedAt: string
}

export default function CardTransactionApp({
  transactionAmount,
  transactionStatus,
  transactionPayedAt
}: CardAccountTransactionPros) {
  function mapTransactionStatus(status: string): { label: string, color: string } {
    switch (status) {
      case "new":
        return { label: "Nova", color: "chatTextColor" };
      case "waiting":
        return { label: "Aguardando", color: "yellow.600" };
      case "identified":
        return { label: "Identificada", color: "dodgerblue" };
      case "approved":
        return { label: "Aprovada", color: "green.600" };
      case "paid":
        return { label: "Paga", color: "green.600" };
      case "unpaid":
        return { label: "Não Paga", color: "red.600" };
      case "refunded":
        return { label: "Reembolsada", color: "gray.500" };
      case "contested":
        return { label: "Contestada", color: "gray.500" };
      case "canceled":
        return { label: "Cancelada", color: "red.600" };
      case "settled":
        return { label: "Marcar como Pago", color: "chatTextColor" };
      case "link":
        return { label: "Link", color: "dodgerblue" };
      case "expired":
        return { label: "Expirada", color: "gray.500" };
      default:
        return { label: "Desconhecido", color: "gray.500" };
    }
  }

  return (
    <Box
      fontSize={"md"}
      borderRadius={10}
      border="1px solid #d0d0d0"
      // bg="chatBackground"
    >
      <VStack align={"start"} gap={0} w={"full"}>
        <Flex
          borderBottom={"1px solid #d0d0d0"}
          bgColor={"chatPrimary"}
          w={"full"}
          p={4}
          borderTopRadius={10}
        >
          <Text fontSize="lg" color={"white"}>
            Valor: <strong>{transactionAmount}{" "}</strong>        
          </Text>
        </Flex>
        <VStack align="start" w={"full"} p={4}>
          <Text>
            Status:
            <Badge color={mapTransactionStatus(transactionStatus).color} bg={"transparent"} fontSize="sm" fontWeight={"600"}>
              {mapTransactionStatus(transactionStatus).label}
            </Badge>
          </Text>
          <Text>
            Data do Pagamento:{" "}
            <strong>
              {transactionPayedAt ? new Date(transactionPayedAt).toLocaleString() : "Não pago"}
            </strong>
          </Text>
        </VStack>
      </VStack>
    </Box>
  )
}