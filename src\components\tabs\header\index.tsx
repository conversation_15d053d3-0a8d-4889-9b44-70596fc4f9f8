import { Button } from "@/components/ui/button";
import { Box, HStack, Text } from "@chakra-ui/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

type HeaderTabProps = {
	title: string;
	buttonTitle?: string;
	isSubmit?: boolean;
	newButton?: React.ReactNode;
	hrefBack?: string;
	handleSubmit?: () => void;
};

export default function HeaderTab({
	buttonTitle,
	title,
	isSubmit,
	newButton,
	hrefBack,
	handleSubmit
}: HeaderTabProps) {

	const navigate = useRouter();
	return (
		<HStack w={"100%"} justifyContent={"space-between"}>
			<Text color="chatTextColor" fontSize={"xl"} fontWeight={"medium"}>
				{title}
			</Text>
			<HStack>
				{newButton && (
					<Box mx={10}>{newButton}</Box>
				)}
				<Box>
					{hrefBack && (
						<Link href={hrefBack}>
						<Button
							w={"100%"}
							borderRadius={20}
							size={"md"}
							fontWeight="700"
							bgColor="gray.300"
							color="black"
							loading={isSubmit}
							transitionDuration={"0.2s"}
							_hover={{
								color: "chatPrimary",
								bgColor: "transparent",
								borderColor: "chatPrimary",
							}}
							_active={{
								transform: "scale(0.95)",
							}}
						>
							Voltar
						</Button>
						</Link>
					)}
				</Box>
				<Box>
					{buttonTitle && (
						<Button
							type="submit"
							w={"100%"}
							borderRadius={20}
							size={"md"}
							fontWeight="700"
							bgColor="chatPrimary"
							color="white"
							loading={isSubmit}
							transitionDuration={"0.2s"}
							_hover={{
								color: "chatPrimary",
								bgColor: "transparent",
								borderColor: "chatPrimary",
							}}
							_active={{
								transform: "scale(0.95)",
							}}
							onClick={handleSubmit}
						>
							{buttonTitle}
						</Button>
					)}
				</Box>
			</HStack>
		</HStack>
	);
}
