"use client";
import CardAccount from "@/components/cards/card-account";
import { Button } from "@/components/ui/button";
import { useGetAllAccounts } from "@/hook/accounts/useGetAccount";
import { Flex, HStack, Spinner, Text, Box } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";
import Link from "next/link";
import ModalCreateAccountBackoffice from "./components/modal-create-account-backoffice";
import { useState } from "react";
import Pagination from "@/components/global/pagination/pagination";

export default function BackofficeAccounts() {
  const [page, setPage] = useState(1);
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const { data: accounts, isLoading, isFetching } = useGetAllAccounts(page);
  const isAccountsLoading = isLoading || isFetching;

  return (
    <>
      <BackofficePageContainer>
        <Flex direction="column" gap={4} w="100%">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Contas
          </Text>
          <HStack justifyContent="space-between" w="100%" alignItems={"center"}>
            <Text fontSize="md" color="gray.400">
              Total de contas: {accounts?.meta.totalItems}
            </Text>
            <Button
              size="sm"
              color={"white"}
              bg={"chatPrimary"}
              borderRadius={"full"}
              colorScheme={"green"}
              _hover={{
                transition: "0.3s",
                bg: "gray.100",
                color: "chatPrimary",
              }}
              onClick={() => setOpenCreateModal(true)}
            >
              Criar Conta
            </Button>
          </HStack>
          <Flex
            direction="column"
            flex={1}
            gap={4}
            overflow="auto"
            maxH="calc(100vh - 200px)"
            position="relative"
            css={{
              boxSizing: "border-box",
              paddingRight: "8px", 
              "&::-webkit-scrollbar": { width: "6px" },
              "&::-webkit-scrollbar-track": { background: "transparent" },
              "&::-webkit-scrollbar-thumb": {
                background: "rgba(85, 85, 85, 0.4)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                background: "rgba(102, 102, 102, 0.6)",
              },
            }}
          >
            {accounts && !isAccountsLoading ?
              accounts.data.map((account) => (
                <Link
                  key={account.secureId}
                  href={`/backoffice/accounts/${account.secureId}`}
                  passHref
                >
                  <CardAccount
                    key={account.secureId}
                    companyName={account.companyName}
                  />
                </Link>
              )) : <Spinner />}
          </Flex>
          {accounts &&
            <Pagination
              totalItems={accounts.meta.totalItems}
              itemsPerPage={accounts.meta.itemsPerPage}
              page={page}
              setPage={setPage}
              position={"absolute"}
              bottom={8}
              right={8}
              color="white"
              hoverColor="backgroundCard"
            />
          }
        </Flex>
      </BackofficePageContainer>
      <ModalCreateAccountBackoffice
        openModal={openCreateModal}
        setOpenModal={setOpenCreateModal}
      />
    </>
  );
}