import { But<PERSON> } from "@/components/ui/button";
import { Box, VStack, Text, Flex, HStack, useBreakpointValue } from "@chakra-ui/react";
import { <PERSON><PERSON><PERSON><PERSON>l, LuUser } from "react-icons/lu";
import ModalUpdateAttendant from "./update-attendant/modal-update-attendant";
import { useEffect, useState } from "react";
import { useGetAttendant } from "@/hook/config/useAttendant";
import { SwitchLabel } from "@/components/global/inputs/switch";
import { Switch } from "@/components/ui/switch";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { toaster } from "@/components/ui/toaster";

type AttendantCardProps = {
  name: string;
  email: string;
  secureId: string;
};

export default function AttendantCard({
  email,
  name,
  secureId,
}: AttendantCardProps) {
  const { data, isLoading, isFetching } = useGetAttendant(secureId);
  const [openUpdateModal, setOpenUpdateModal] = useState(false);
  const [isActive, setIsActive] = useState<boolean | undefined>();

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  useEffect(() => {
    if (data) {
      setIsActive(data.currentAccount.isActive);
    }
  }, [data]);

  const handleEdit = () => {
    changeStatusAttendant.mutateAsync();
  };

  const changeStatusAttendant = useMutation({
    mutationFn: async () => {
      await api.put(`/attendant/${secureId}`, {
        isActive: isActive,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["listAttendants"] });
      toaster.create({
        type: "success",
        description: `Atendente ${!isActive ? "desativado" : "ativado"} com sucesso`,
        title: `Atendente ${!isActive ? "desativado" : "ativado"}`,
      });
    },
    onError: () => {
      setIsActive(!isActive);
    },
  });

  return (
    <>
      {isMobile ? (
        // Mobile Layout - Linear/Row Format
        <Box w={"100%"} position={"relative"}>
          <Flex
            bgColor={"chatBackground"}
            w={"100%"}
            p={4}
            rounded={"2xl"}
            borderWidth={1}
            borderColor={"transparent"}
            position={"relative"}
            alignItems={"center"}
            justifyContent={"space-between"}
            gap={3}
            opacity={isActive ? 1 : 0.5}
            _hover={{
              bgColor: "chatCardBackground",
              cursor: "pointer",
              transition: "0.3s",
              borderColor: "chatPrimary",
            }}
          // _active={{
          //   transform: "scale(0.95)",
          //   transition: "0.2s",
          // }}
          >
            <HStack gap={3} flex={1}>
              <LuUser size={"24"} color="#FD2264" />
              <VStack alignItems={"flex-start"} gap={1}>
                <Text color={"chatTextColor"} fontSize={"sm"} fontWeight={"medium"}>
                  {name}
                </Text>
                <Text
                  color={"chatTextColor"}
                  fontSize={"xs"}
                  opacity={0.7}
                >
                  {email}
                </Text>
              </VStack>
            </HStack>
          </Flex>
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      ) : (
        // Desktop Layout - Card Format
        <Box w={"20%"} position={"relative"}>
          <Box
            h={{ base: "200px", xl: "150px" }}
            bgColor={"chatBackground"}
            p={4}
            rounded={"2xl"}
            borderWidth={1}
            borderColor={"transparent"}
            opacity={isActive ? 1 : 0.5}
            _hover={{
              bgColor: "chatCardBackground",
              cursor: "pointer",
              transition: "0.3s",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
              transition: "0.2s",
            }}
            alignContent={"center"}
            onClick={() => {
              setOpenUpdateModal(true);
            }}
          >
            <VStack gap={2}>
              <LuUser size={"30"} color="#FD2264" />
              <Text color={"chatTextColor"} fontWeight={"medium"}>
                {name}
              </Text>
              <Text
                wordBreak={"break-word"}
                textAlign={"center"}
                color={"chatTextColor"}
                fontWeight={"medium"}
                fontSize={"xs"}
              >
                {email}
              </Text>
            </VStack>
          </Box>
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      )}
      {data && (
        <ModalUpdateAttendant
          attendantSecureId={secureId}
          openModal={openUpdateModal}
          setOpenModal={setOpenUpdateModal}
          cellPhone={data.cellPhone}
          cpf={data.cpf}
          email={data.email}
          userPermissions={data.permissions}
          hasAllPermissions={data.hasAllPermissions}
          participatesInRotation={data.currentAccount.participatesInRotation}
          name={data.name}
          key={secureId}
        />
      )}
    </>
  );
}
