import {
  <PERSON>,
  <PERSON>Stack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  Separator,
  Button,
  Flex,
  Center,
  Spinner,
} from "@chakra-ui/react";
import { BackofficeChatbotDetailDto, ChatbotMessageDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuMessageSquare, LuClock, LuUser, LuBot, LuChevronLeft, LuChevronRight } from "react-icons/lu";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useState, useMemo, useEffect } from "react";

type MessageHistoryProps = {
  chatbot: BackofficeChatbotDetailDto;
  isLoading?: boolean;
  currentPage: number; // A página atual é recebida via props
  onPageChange: (page: number) => void; // Função para notificar o pai sobre a mudança de página
  itemsPerPage?: number; // Opcional: itens por página para a visão "Todas"
};

type ViewMode = "all" | "sent" | "received";

export default function MessageHistory({ 
  chatbot, 
  isLoading,
  currentPage,
  onPageChange,
  itemsPerPage = 10, // Valor padrão para a paginação da visão "Todas"
}: MessageHistoryProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("all");

  useEffect(() => {
    onPageChange(1);
  }, [viewMode, onPageChange]);


  const formatDate = (date: string | Date) => {
    return format(new Date(date), "dd/MM/yyyy HH:mm", { locale: ptBR });
  };

  const getMessageTypeIcon = (type: string) => {
    // (código inalterado)
    switch (type) {
      case "text": return <LuMessageSquare size={16} />;
      case "audio": return "🎵";
      case "file": return "📎";
      case "image": return "🖼️";
      default: return <LuMessageSquare size={16} />;
    }
  };

  console.log("AAAAAA: ", chatbot.sentMessages.data);

  const displayData = useMemo(() => {
    const sentMessages = {
      ...chatbot.sentMessages,
      data: chatbot.sentMessages.data.map((msg) => ({ ...msg, type: "sent" as const }))
    };
    const receivedMessages = {
      ...chatbot.receivedMessages,
      data: chatbot.receivedMessages.data.map((msg) => ({ ...msg, type: "received" as const }))
    };

    switch (viewMode) {
      case "sent":
        return { data: sentMessages.data, meta: sentMessages.meta };

      case "received":
        return { data: receivedMessages.data, meta: receivedMessages.meta };

      case "all":
      default: {
        const all = [...sentMessages.data, ...receivedMessages.data].sort(
          (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
        
        const dominantMeta = sentMessages.meta.totalItems > receivedMessages.meta.totalItems 
          ? sentMessages.meta 
          : receivedMessages.meta;

        return {
          data: all,
          meta: dominantMeta, // Usamos um meta de referência
        };
      }
    }
  }, [chatbot, viewMode]);

  const totalPages = displayData.meta.totalPages;
  const totalItems = displayData.meta.totalItems;

  if (isLoading) {
    return (
      <Box bg="white" borderRadius="xl" p={6} shadow="sm" border="1px solid" borderColor="white">
        <Center h="200px"><Spinner size="lg" color="purple.500" /></Center>
      </Box>
    );
  }

  return (
    <Box
      bg="background"
      borderRadius="xl"
      p={6}
      maxH="calc(100vh - 250px)"
      position="relative"
      overflow="auto"
       css={{
        boxSizing: "border-box",
        paddingRight: "8px",
        "&::-webkit-scrollbar": { width: "6px" },
        "&::-webkit-scrollbar-track": { background: "transparent" },
        "&::-webkit-scrollbar-thumb": {
          background: "rgba(85, 85, 85, 0.4)",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "rgba(102, 102, 102, 0.6)",
        },
      }}
    >
      <VStack align="stretch" gap={6}>
        <HStack justify="space-between" align="center">
          <HStack>
            <LuMessageSquare size={24} color="#fd2264" />
            <Text fontSize="lg" fontWeight="bold" color="white">
              Histórico de Mensagens
            </Text>
          </HStack>
          
          <HStack>
             <Button size="sm" variant={viewMode === "all" ? "solid" : "outline"} colorScheme="gray" onClick={() => setViewMode("all")}>
               Todas ({chatbot.sentMessages.meta.totalItems + chatbot.receivedMessages.meta.totalItems})
             </Button>
             <Button size="sm" variant={viewMode === "sent" ? "solid" : "outline"} colorScheme="purple" onClick={() => setViewMode("sent")}>
               Enviadas ({chatbot.sentMessages.meta.totalItems})
             </Button>
             <Button size="sm" variant={viewMode === "received" ? "solid" : "outline"} colorScheme="blue" onClick={() => setViewMode("received")}>
               Recebidas ({chatbot.receivedMessages.meta.totalItems})
             </Button>
          </HStack>
        </HStack>

        <Separator />

        <VStack align="stretch" gap={4} minH="400px">
          {displayData.data.length > 0 ? (
            displayData.data.map((message) => {
              const isSentMessage = message.type === "sent";
              return (
                <Box
                  key={`${message.secureId}-${message.type}`}
                  p={4}
                  bg={isSentMessage ? "purple.50" : "blue.50"}
                  borderRadius="lg"
                >
                  <VStack align="stretch" gap={3}>
                    {/* Message header */}
                    <HStack justify="space-between" align="center">
                      <HStack>
                        {isSentMessage ? (
                          <LuBot size={16} color="#fd2264" />
                        ) : (
                          <LuUser size={16} color="#2563EB" />
                        )}
                        <Badge
                          color={isSentMessage ? "#fd2264" : "#2563EB"}
                          bgColor={isSentMessage ? "#ffdce7" : "#cedeff"}
                          variant="subtle"
                          fontSize="xs"
                        >
                          {isSentMessage ? "Chatbot" : message.customerName || "Cliente"}
                        </Badge>
                        <HStack>
                          {getMessageTypeIcon("text")}
                          <Text fontSize="xs" color="gray.600">
                            texto
                          </Text>
                        </HStack>
                      </HStack>

                      <HStack>
                        <LuClock size={14} color="#6B7280" />
                        <Text fontSize="xs" color="gray.600">
                          {formatDate(message.createdAt)}
                        </Text>
                      </HStack>
                    </HStack>
                    {/* Message content */}
                    <Box>
                      <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                        {isSentMessage ? message.sendMessage : message.receiveMessage || "Mensagem não disponível"}
                      </Text>
                    </Box>
                    {/* Session info */}
                    <HStack justify="space-between" align="center">
                      <Text fontSize="xs" color="gray.500">
                        Sessão: {message.sessionSecureId}
                      </Text>
                      {message.inputToken && message.outputToken && (
                        <Text fontSize="xs" color="gray.500">
                          Tokens: {message.inputToken} in / {message.outputToken} out
                        </Text>
                      )}
                    </HStack>
                  </VStack>
                </Box>
              );
            })
          ) : (
            <Center py={10}>
              <VStack>
                <LuMessageSquare size={48} color="#D1D5DB" />
                <Text color="gray.500" fontSize="lg">
                  Nenhuma mensagem {viewMode === "all" ? "encontrada" : viewMode === "sent" ? "enviada" : "recebida"}
                </Text>
                <Text color="gray.400" fontSize="sm">
                  Este chatbot ainda não possui mensagens {viewMode === "all" ? "enviadas ou recebidas" : viewMode === "sent" ? "enviadas" : "recebidas"}
                </Text>
              </VStack>
            </Center>
          )}
        </VStack>

        {totalPages > 1 && (
          <>
            <Separator />
            <Flex justify="space-between" align="center">
              <Text fontSize="sm" color="gray.600">
                Mostrando {displayData.data.length} de {totalItems} mensagens
              </Text>
              
              <HStack>
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => onPageChange(currentPage - 1)}
                >
                  <LuChevronLeft />
                  Anterior
                </Button>
                
                <Text fontSize="sm" color="gray.600">
                  Página {currentPage} de {totalPages}
                </Text>
                
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === totalPages}
                  onClick={() => onPageChange(currentPage + 1)}
                >
                  Próxima
                  <LuChevronRight />
                </Button>
              </HStack>
            </Flex>
          </>
        )}
      </VStack>
    </Box>
  );
}