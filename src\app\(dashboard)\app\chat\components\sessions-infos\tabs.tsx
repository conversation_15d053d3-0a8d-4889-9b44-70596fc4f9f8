import { Flex, Tabs, Text } from "@chakra-ui/react";
import SessionsObservation from "./sessions-observation";
import CustomerInformation from "./customer-informations";

export default function SessionInfosTabs() {
  return (
    <Tabs.Root
      defaultValue="customerInfos"
      activationMode="manual"
      orientation="horizontal"
      w={"100%"}
      h={"100%"}
      display="flex"
      flexDirection="column"
      size={"md"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={3}
        border={"none"}
        alignItems={"flex-start"}
      >
        <Tabs.Trigger
          value="customerInfos"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderBottomColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"50%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pb={2}
        >
          <Text fontWeight={"normal"} fontSize="sm">Informações do Cliente</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="observations"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderBottomColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"50%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pb={2}
        >
          <Text fontWeight={"normal"} fontSize="sm">Observações</Text>
        </Tabs.Trigger>
      </Tabs.List>
      <Tabs.Content value="customerInfos" width={"100%"} flex={1} overflow="hidden">
        <Flex
          flex={1}
          h="100%"
          overflow="auto"
          flexDirection={"column"}
        >
          <CustomerInformation />
        </Flex>
      </Tabs.Content>
      <Tabs.Content
        value="observations"
        display="flex"
        flexDirection="column"
        width={"100%"}
        flex={1}
        overflow="hidden"
      >
        <Flex
          flex={1}
          h="100%"
          overflow="auto"
          flexDirection={"column"}
        >
          <SessionsObservation />
        </Flex>
      </Tabs.Content>
    </Tabs.Root>
  );
}
