import { NumberInput as ChakraNumberInput } from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { FieldError } from "react-hook-form";
import { NumberInputField, NumberInputRoot } from "@/components/ui/number-input";
import { Field } from "@/components/ui/field";

interface InputNumberProps extends Omit<ChakraNumberInput.RootProps, "max" | "min"> {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  height?: string;
  color?: string;
  min?: number | string;
  max?: number | string;
  placeholder?: string
}

const InputBase: ForwardRefRenderFunction<HTMLDivElement, InputNumberProps> = (
  { 
    name,
    label,
    error,
    placeholder,
    height = "60px",
    labelColor,
    color = "chatPrimary",
    min,
    max,
    ...rest
  },
  ref
) => {
  return (
    <Field
      invalid={!!error}
      h={height}
      errorText={error?.message}
      label={label}
      labelColor={labelColor}
    >
      <NumberInputRoot
        id={name}
        name={name}
        ref={ref}
        color={color}
        w={"100%"}
        bgColor={"white"}
        borderColor="#D6D6D6"
        borderWidth={2}
        _hover={{
          borderColor: error ? "red.400" : color,
        }}
        _focus={{
          borderColor: error ? "red.500" : color,
        }}
        size={"md"}
        min={typeof min === "string" ? parseFloat(min) : min}
        max={typeof max === "string" ? parseFloat(max) : max}
        {...rest}
      >
        <NumberInputField placeholder={placeholder} _placeholder={{ color: "#B1A0A5" }} border={"none"} outline={"none"} />
      </NumberInputRoot>
    </Field>
  );
};

export const InputNumber = forwardRef(InputBase);

