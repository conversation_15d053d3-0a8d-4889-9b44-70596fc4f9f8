"use client";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { WebSocketService } from "@/services/websocket/websocket";
import {
  ChatSessionDto,
  ChatSessionSessionsInfosDto,
  ChatSessionsMessagesOutputDto,
} from "@/utils/types/DTO/chat-sessions.dto";
import { InfiniteData, useQuery } from "@tanstack/react-query";
import React, { createContext, useContext, useEffect, useState } from "react";
import useInfiniteChatMessages from "@/hook/chat-session/useInfiniteChatMessages";

type SessionContextData = {
  selectedSessionId?: string;
  sessionInfo?: ChatSessionSessionsInfosDto;
  refetch?: () => void;
};

const SessionContext = createContext({} as SessionContextData);

type SessionProviderProps = {
  children: React.ReactNode;
  sessionId?: string;
};

export const SessionProvider = ({
  children,
  sessionId,
}: SessionProviderProps) => {
  const [messages, setMessages] = useState<ChatSessionsMessagesOutputDto[]>([]);
  const [sessionInfo, setSessionInfo] = useState<ChatSessionSessionsInfosDto>();
  const [isLoading, setIsLoading] = useState(false);
  const { data, refetch } = useQuery({
    queryKey: ["chat-sessions", sessionId],
    queryFn: async () => {
      if (sessionId) {
        return await api.get<ChatSessionDto>(`/chat-sessions/${sessionId}`);
      }
    },
    enabled: !!sessionId,
  });

  useEffect(() => {
    setMessages([]);
    setSessionInfo(undefined);
  }, [sessionId]);

  useEffect(() => {
    setIsLoading(true);
    const fetchSessionData = async () => {
      if (data?.data && data) {
        const sessionData = data.data;
        try {
          setSessionInfo({
            chatId: sessionData?.chat?.secureId,
            accountId: sessionData.account.secureId,
            customerId: sessionData.customerId,
            customerName: sessionData.customerName,
            customerEmail: sessionData.customerEmail,
            isAIResponder: sessionData.isAIResponder,
            isArchived: sessionData.isArchived,
            isFinalized: sessionData.isFinalized,
            customerDocument: sessionData.customerDocument,
            customerPhone: sessionData.customerPhone,
            sessionDescription: sessionData.sessionDescription,
            attendantName: sessionData.attendant?.user.name,
            attendantSecureId: sessionData.attendant?.user.secureId,
          });
        } catch (error) {
          console.error(error);
        }
      }
    };
    fetchSessionData();
    setIsLoading(false);
  }, [data?.data]);

  return (
    <SessionContext.Provider
      value={{
        selectedSessionId: sessionId,
        sessionInfo,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
};

export function useChatSessionContext() {
  return useContext(SessionContext);
}
