import { api } from "@/services/api";
import { useInfiniteQuery } from "@tanstack/react-query";
import { GetNotificationsDto } from "@/utils/types/DTO/notifications.dto";

async function getNotifications({
  pageParam = 1,
  pageSize = 5,
}: {
  pageParam?: number;
  pageSize?: number;
}) {
  const { data } = await api.get<GetNotificationsDto>("/notifications", {
    params: {
      page: pageParam,
      limit: pageSize,
    },
  });

  return {
    data: data.data,
    meta: {
      totalItems: data.meta.totalItems,
      totalPages: data.meta.totalPages,
      currentPage: data.meta.currentPage,
      itemsPerPage: data.meta.itemsPerPage,
    },
  };
}

export default function useNotification() {
  return useInfiniteQuery({
    queryKey: ["notifications"],
    queryFn: ({ pageParam }) => getNotifications({ pageParam }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.meta;
      if (currentPage < totalPages) {
        return currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    refetchInterval: 1000 * 60 * 1, // 1 minutes
  });
}
