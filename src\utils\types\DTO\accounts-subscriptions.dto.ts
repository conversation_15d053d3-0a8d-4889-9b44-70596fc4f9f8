import { SubscriptionStatus } from "../subscription/subscription-status";
import { GetAccountDto } from "./accounts.dto";
import { MetaDTO } from "./meta.dto";

export type GetAllAccountsSubscriptionsDto = {
  meta: MetaDTO;
  data: GetAccountSubscriptionDto[]
}

export type GetAccountSubscriptionDto = {
  secureId: string;
  cycle: number;
  status: SubscriptionStatus;
  type: "paid" | "trial";
  plan: {
    secureId: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }
  account: GetAccountDto;
  gatewaySubscriptionId: number;
  startsAt: string;
  endsAt: string;
  canceledAt: string | null;
  createdAt: string;
  updatedAt: string;
}