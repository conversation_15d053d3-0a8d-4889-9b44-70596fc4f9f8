import { api } from "@/services/api";
import { useQuery } from "@tanstack/react-query";

type ApiResponseType = {
  chats: {
    quantity: number;
  };
  leads: {
    quantity: number;
  };
  aiMessages: {
    quantity: number;
  };
  aiSessions: {
    quantity: number;
  };
  attendantSessions: {
    quantity: number;
  }
  waitingSessions: {
    quantity: number;
  }
  ongoingConversations: {
    quantity: number;
  }
  finalizedConversations: {
    quantity: number;
  }
  metrics: {
    averageFirstResponseTime: number;
    // averageResolutionTime: number;
  }
};

async function getDashboardItens() {
  const { data } = await api.get<ApiResponseType>("/dashboard");

  return data;
}

export default function useAppDashboard() {
  return useQuery({
    queryKey: ["dashboard"],
    queryFn: async () => await getDashboardItens(),
  });
}
