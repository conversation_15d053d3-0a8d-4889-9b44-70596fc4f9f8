import { MetaDTO } from "./meta.dto";

export type GetBackofficeChatbotsDto = {
  meta: MetaDTO;
  data: BackofficeChatbotDto[];
};

export type BackofficeChatbotDto = {
  secureId: string;
  name: string;
  isAI: boolean;
  isActive: boolean;
  account: {
    secureId: string;
    companyName: string;
  };
  statistics: {
    tokensInput: number;
    tokensOutput: number;
    messagesSent: number;
    sessionsResponded: number;
  };
  createdAt: string;
  updatedAt: string;
};

// Configuration DTO matching backend
export type ChatbotConfigurationDto = {
  emotionalTone: string | null;
  mood: string | null;
  responseSize: string | null;
  responseStyle: string | null;
  temperature: number;
  isLeadCaptureActive: boolean;
  leadTriggerMessageLimit: number | null;
  leadCaptureMessage: string | null;
  leadCaptureThankYouMessage: string | null;
  greetingMessage: string | null;
  AIPrompt: string | null;
  leadCaptureJson: any;
};

// Message DTO matching backend
export type ChatbotMessageDto = {
  secureId: string;
  sendMessage: string | null;
  receiveMessage: string | null;
  messageDirection: string | null;
  sessionSecureId: string;
  customerName: string | null;
  customerId: string;
  inputToken: number | null;
  outputToken: number | null;
  createdAt: Date;
};

// Pagination interface matching backend
export interface IWithPagination<T> {
  data: T[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    itemsPerPage: number;
  };
}

// Detailed chatbot view DTO matching backend exactly
export type BackofficeChatbotDetailDto = {
  // Basic chatbot information
  secureId: string;
  name: string;
  isAI: boolean;
  inputTokenCount: number;
  outputTokenCount: number;
  accountOwnerName: string;
  totalMessagesSent: number;
  totalSessionsResponded: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Detailed configuration
  configuration: ChatbotConfigurationDto;

  // Message history for analysis
  sentMessages: IWithPagination<ChatbotMessageDto>;
  receivedMessages: IWithPagination<ChatbotMessageDto>;
};
