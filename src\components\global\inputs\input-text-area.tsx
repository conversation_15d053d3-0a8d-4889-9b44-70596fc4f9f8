import {
  Textarea as ChakraInput,
  TextareaProps as ChakraInputProps,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  height?: string;
  optionalText?: React.ReactNode;
  color?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLTextAreaElement, InputProps> = (
  {
    name,
    label,
    error,
    optionalText,
    height,
    startElement,
    labelColor,
    color,
    ...rest
  },
  ref
) => {
  color = color ? color : "chatPrimary";
  return (
    <Field
      invalid={!!error}
      h={height ? `calc(${height} + 20px)` : "60px"}
      errorText={error?.message}
      label={label}
      optionalText={optionalText}
      labelColor={labelColor}
    >
      <InputGroup w="100%" startElement={startElement}>
        <ChakraInput
          ref={ref}
          id={name}
          name={name}
          size="sm"
          resize="none"
          height={height}
          _hover={{
            borderColor: color,
          }}
          _placeholder={{ color: "#B1A0A5" }}
          _focus={{
            borderColor: color,
          }}
          bgColor={"white"}
          color={color}
          borderRadius={10}
          borderColor="#D6D6D6"
          borderWidth={2}
          css={{
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              width: "6px",
              marginBottom: "10px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "#D6D6D6",
              borderRadius: "24px",
              "&:hover": {
                background: "#A6A6A6",
              },
            },
          }}
          {...rest}
        />
      </InputGroup>
    </Field>
  );
};

export const InputTextArea = forwardRef(InputBase);
