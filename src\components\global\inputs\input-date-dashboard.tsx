import { 
  HStack, 
  Input as ChakraInput,
  InputProps as ChakraInputProps,
  Text, 
  Flex
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";

interface CardInputDateProps extends ChakraInputProps {
  label: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, CardInputDateProps> = (
  { label, onChange, ...rest },
  ref
) => {
  return (
    <Flex flexDir={{ base: "column", sm: "row" }} alignItems={"center"} gap={2}>
      <Text color={"white"} fontSize={"sm"} fontWeight={"bold"} lineHeight={"short"} whiteSpace={"nowrap"}>{label}</Text>
      <ChakraInput
        onChange={onChange}
        type="date"
        size="xs"
        placeholder="Selecione a data"
        borderRadius={20}
        bgColor={"background"}
        {...rest}
      />
    </Flex>
  );
}

export const InputDateDashboard = forwardRef(InputBase);