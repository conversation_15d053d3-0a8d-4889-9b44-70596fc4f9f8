export interface StreamMessage {
  type: string;
  content: string;
}

/**
 * Parses SSE (Server-Sent Events) data into separate message objects
 * based on event types
 *
 * @param data Raw event stream data
 * @returns Array of parsed messages
 */
export function parseMessageStream(data: string): StreamMessage[] {
  const messages: StreamMessage[] = [];

  // Split the data by event boundaries
  const eventChunks = data.split("event: ");

  // Process each chunk (skipping the first empty chunk if it exists)
  for (let i = 1; i < eventChunks.length; i++) {
    const chunk = eventChunks[i];
    const eventTypeEnd = chunk.indexOf("\n");

    if (eventTypeEnd > 0) {
      const eventType = chunk.substring(0, eventTypeEnd).trim();
      const dataStart = chunk.indexOf("data: ");

      if (dataStart > 0) {
        const dataContent = chunk.substring(dataStart + 6).trim();

        // Only include message events that we want to display
        if (
          eventType === "ai_message" ||
          eventType === "attachment_message" ||
          eventType === "customer_info_request"
        ) {
          messages.push({
            type: eventType,
            content: dataContent,
          });
        }
      }
    }
  }

  return messages;
}
