import { formatCellphone } from "@/utils/funcs/format-number-phone";
import { Box, HStack, Text, VStack } from "@chakra-ui/react";

type AttendantCardProps = {
  attendant: {
    secureId: string;
    name: string;
		cellPhone: string;
    email: string;
  };
  isSelected: boolean;
	handleChangeAttendant: (secureId: string) => void;
};

export default function AttendantCard({
  attendant,
	handleChangeAttendant,
  isSelected,
}: AttendantCardProps) {
  return (
    <Box
      bgColor={isSelected ? "chatCardBackground" : "chatBackground"}
      border={isSelected ? "1px solid #FD2264" : "none"}
      w={"100%"}
      padding={4}
      borderRadius={5}
      display={"flex"}
      flexDirection={"column"}
      alignItems={"center"}
      justifyContent={"center"}
      cursor={isSelected ? "disabled" : "pointer"}
      _hover={{
        bgColor: "chatCardBackground",
        transition: "0.1s",
      }}
      _active={
        isSelected
          ? {}
          : {
              transform: "scale(0.95)",
              transition: "0.1s",
            }
      }
      onClick={() => {
				if (!isSelected) {
					handleChangeAttendant(attendant.secureId);
				}
      }}
    >
			<VStack gap={3} alignItems="center" justifyContent="space-around" w={"100%"}>
				<Text fontSize="lg" fontWeight="bold" color="chatTextColor">
					{attendant.name}
				</Text>
				<HStack>
				<Text fontSize="lg" fontWeight="bold" color="chatTextColor">
					{attendant.email}
				</Text>
				<Text fontSize="lg" fontWeight="bold" color="chatTextColor">
					{formatCellphone(attendant.cellPhone)}
				</Text>
				</HStack>
			</VStack>
      {/* <Text
        key={attendant.secureId}
        fontSize="sm"
        fontWeight="bold"
        color="chatTextColor"
        textAlign="center"
        padding={2}
        borderRadius={5}
        width="100%"
      >
        {`${attendant.name} - ${attendant.email}`}
      </Text> */}
    </Box>
  );
}
