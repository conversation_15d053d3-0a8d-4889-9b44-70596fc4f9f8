"use client";
import { Tabs, useBreakpointValue } from "@chakra-ui/react";
import { LuBrainCircuit, LuMessageCircleCode, LuUsers } from "react-icons/lu";
import { FaWhatsapp } from "react-icons/fa";
import WhatsappTab from "./whatsapp-tab";
import WebChatTab from "./webchat-tab";
import AttendantTab from "./attendant-tab";
import ChatBotTab from "./chatbot-tab/indext";
import { TabTrigger } from "./components/tab-trigger";
import {
  ATTENDANTPERMISSIONS,
  CHATBOTPERMISSIONS,
  WEBCHATPERMISSIONS,
  WHATSAPPERMISSIONS,
} from "@/utils/types/permissions/all-attendant-permissions";
import HasPermission from "@/utils/funcs/has-permission";
import { useEffect, useState } from "react";

export default function EditTabs() {
  const [activeTab, setActiveTab] = useState<string | undefined>(undefined);
  const [currentUrl, setCurrentUrl] = useState<string>("");
  const isMobile = useBreakpointValue({ base: true, md: false });

  const getTabFromHash = () => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash.replace("#", "");
      // Map hash values to match tab values exactly as they are in the drawer
      const hashToTab: Record<string, string> = {
        whatsapp: "whatsapp",
        webchat: "webchats",
        chatbot: "chatbots",
        attendant: "attendants"
      };
      return hashToTab[hash] || "whatsapp"; // Default to whatsapp if no hash
    }
    return "whatsapp";
  };

  // console.log()

  useEffect(() => {
    const updateActiveTab = () => {
      if (typeof window !== "undefined") {
        const newUrl = window.location.href;
        if (newUrl !== currentUrl) {
          setCurrentUrl(newUrl);
          const tabFromHash = getTabFromHash();
          setActiveTab(tabFromHash);
        }
      }
    };

    updateActiveTab();

    const handleHashChange = () => {
      updateActiveTab();
    };

    const handlePopState = () => {
      updateActiveTab();
    };

    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("popstate", handlePopState);

    const checkUrlInterval = setInterval(updateActiveTab, 100);

    return () => {
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("popstate", handlePopState);
      clearInterval(checkUrlInterval);
    };
  }, [window.location]);

  const handleTabChange = (details: { value: string }) => {
    setActiveTab(details.value);
    // Update URL hash
    if (typeof window !== "undefined") {
      // Map tab values back to hash values (exactly as they are in the drawer)
      const tabToHash: Record<string, string> = {
        whatsapp: "whatsapp",
        webchats: "webchat",
        chatbots: "chatbot",
        attendants: "attendant"
      };
      window.location.hash = tabToHash[details.value] || details.value;
    }
  };

  if (isMobile) {
    const renderMobileContent = () => {
      switch (activeTab) {
        case "whatsapp":
          return HasPermission(WHATSAPPERMISSIONS.VIEW) ? <WhatsappTab /> : null;
        case "webchats":
          return HasPermission(WEBCHATPERMISSIONS.VIEW) ? <WebChatTab /> : null;
        case "chatbots":
          return HasPermission(CHATBOTPERMISSIONS.VIEW) ? <ChatBotTab /> : null;
        case "attendants":
          return HasPermission(ATTENDANTPERMISSIONS.VIEW) ? <AttendantTab /> : null;
        default:
          if (HasPermission(WHATSAPPERMISSIONS.VIEW)) return <WhatsappTab />;
          if (HasPermission(WEBCHATPERMISSIONS.VIEW)) return <WebChatTab />;
          if (HasPermission(CHATBOTPERMISSIONS.VIEW)) return <ChatBotTab />;
          if (HasPermission(ATTENDANTPERMISSIONS.VIEW)) return <AttendantTab />;
          return null;
      }
    };

    return renderMobileContent();
  }

  return (
    <Tabs.Root
      value={activeTab}
      onValueChange={handleTabChange}
      activationMode="manual"
      orientation="vertical"
      gap={1}
      flex={1}
      size={"lg"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={"5"}
        width={{ base: "25%", "2xl": "15%" }}
        rounded={"2xl"}
        border={"none"}
        alignItems={"flex-start"}
      >
        {HasPermission(WHATSAPPERMISSIONS.VIEW) && (
          <TabTrigger
            icon={<FaWhatsapp />}
            label={"Whatsapp"}
            value={"whatsapp"}
            key={"whatsapp"}
          />
        )}
        {HasPermission(WEBCHATPERMISSIONS.VIEW) && (
          <TabTrigger
            icon={<LuMessageCircleCode />}
            label={"WebChats"}
            value={"webchat"}
            key={"webchats"}
          />
        )}
        {HasPermission(CHATBOTPERMISSIONS.VIEW) && (
          <TabTrigger
            icon={<LuBrainCircuit />}
            label={"ChatBots"}
            value={"chatbot"}
            key={"chatbots"}
          />
        )}
        {HasPermission(ATTENDANTPERMISSIONS.VIEW) && (
          <TabTrigger
            icon={<LuUsers />}
            label={"Atendentes"}
            value={"attendant"}
            key={"attendants"}
          />
        )}
      </Tabs.List>

      <Tabs.Content value="whatsapp" width={"100%"}>
        <WhatsappTab />
      </Tabs.Content>
      <Tabs.Content value="webchats" width={"100%"}>
        <WebChatTab />
      </Tabs.Content>
      <Tabs.Content value="chatbots" width={"100%"}>
        <ChatBotTab />
      </Tabs.Content>
      <Tabs.Content value="attendants" width={"100%"}>
        <AttendantTab />
      </Tabs.Content>
    </Tabs.Root>
  );
}
