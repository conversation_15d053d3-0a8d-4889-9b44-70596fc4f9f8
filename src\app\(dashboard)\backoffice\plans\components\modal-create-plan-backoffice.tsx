
import { Input } from "@/components/global/inputs/input";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Flex, HStack, Text, useBreakpointValue, VStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import BasicBackofficeModal from "@/components/global/modal/basic-backoffice-modal";
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import { InputNumber } from "@/components/global/inputs/input-number";
import { InputTextArea } from "@/components/global/inputs/input-text-area";

type ModalCreatePlanBackofficeProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
};

const createPlanSchema = yup.object().shape({
  name: yup.string().required("Nome obrigatório"),
  slug: yup.string().required("Slug obrigatório"),
  description: yup.string().required("Descrição obrigatória"),
  details: yup.string().required("Detalhes obrigatórios"),
  price: yup.string().required("Preço obrigatório"),
  attendantsLimit: yup
    .number()
    .typeError("Deve ser um número")
    .required("Limite obrigatório"),
  whatsappNumberLimit: yup
    .number()
    .typeError("Deve ser um número")
    .required("Limite obrigatório"),
  chatbotsLimit: yup
    .number()
    .typeError("Deve ser um número")
    .required("Limite obrigatório"),
  knowledgeBaseLimit: yup
    .number()
    .typeError("Deve ser um número")
    .required("Limite obrigatório"),
  iaMessagesLimit: yup
    .number()
    .typeError("Deve ser um número")
    .required("Limite obrigatório"),
  trialDays: yup
    .number()
    .typeError("Deve ser um número")
    .required("Dias obrigatórios"),
});

type CreatePlanFormData = yup.InferType<typeof createPlanSchema>;

export default function ModalCreatePlanBackoffice({
  openModal,
  setOpenModal,
}: ModalCreatePlanBackofficeProps) {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreatePlanFormData>({
    resolver: yupResolver(createPlanSchema),
  });
  const detailsValue = watch("details");

  const createPlan = useMutation({
    mutationFn: async (data: CreatePlanFormData) => {
      await api.post("/backoffice/plans", {
        ...data
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Plano criado com Sucesso.",
        title: "Plano criado!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["plans-backoffice"],
      });
      setOpenModal(false);
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: CreatePlanFormData) => {
    try {
      createPlan.mutateAsync(data);
    } catch (e) {}
  };

  const modalSize = useBreakpointValue<"xs" | "sm" | "md" | "lg" | "xl" | "cover" | "full">({
    base: "sm",
    sm: "md",
    md: "lg",
    lg: "xl",
  });

  return (
    <BasicBackofficeModal
      open={openModal}
      size={modalSize}
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Cadastrar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Cadastrar Plano"
      placement="center"
      children={
        <Flex flex={1} gap={4} flexDir={"column"} alignItems={"center"}>
          <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
            <Input
              label="Nome"
              color="background"
              placeholder="Nome do plano"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("name")}
              error={errors.name}
            />
            <Input
              label="Slug"
              color="background"
              placeholder="Slug do plano"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("slug")}
              error={errors.slug}
            />
            <Input
              label="Preço"
              color="background"
              placeholder="Preço do plano (Ex: 199,99)"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("price")}
              error={errors.price}
            />
          </HStack>
          <InputTextArea
            label="Descrição"
            color="background"
            placeholder="Breve descrição do plano"
            size={{base: "sm", lg: "md"}}
            height="80px"
            borderRadius={20}
            {...register("description")}
            error={errors.description}
          />
          <HStack width={"100%"} gap={5} justifyContent={"space-between"} mt={5}>
            <VStack width={"100%"}>
              <Text
                fontSize={"sm"}
                fontWeight={"medium"}
                alignSelf={"flex-start"}
              >
                Detalhes
              </Text>
              <ReactQuill
                placeholder={"Ex: • 1 Atendente..."}
                theme="snow"
                value={detailsValue}
                onChange={(value) => setValue("details", value)}
                style={{
                  width: "100%",
                  padding: "6px",
                  borderRadius: "20px",
                  backgroundColor: "white",
                  color: "black",
                }}
              />
              {errors.details && (
                <Text color="red.400" fontSize="xs" fontWeight={"medium"} alignSelf={"flex-start"}>
                  {errors.details.message}
                </Text>
              )}
            </VStack>
          </HStack>
          <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
            <InputNumber
              label="Chatbots"
              color="background"
              placeholder="Limite de chatbots"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("chatbotsLimit")}
              error={errors.chatbotsLimit}
            />
            <InputNumber
              label="Atendentes"
              color="background"
              placeholder="Limite de atendentes"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("attendantsLimit")}
              error={errors.attendantsLimit}
            />
            <InputNumber
              label="Mensagens de IA"
              color="background"
              placeholder="Limite de mensagens de IA"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("iaMessagesLimit")}
              error={errors.iaMessagesLimit}
            />
          </HStack>
          <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
            <InputNumber
              label="Números WhatsApp"
              color="background"
              placeholder="Limite de números"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("whatsappNumberLimit")}
              error={errors.whatsappNumberLimit}
            />
            <InputNumber
              label="Bases Conhecimento"
              color="background"
              placeholder="Limite de bases"
              borderRadius={20}
              size={{base: "sm", lg: "md"}}
              height="80px"
              {...register("knowledgeBaseLimit")}
              error={errors.knowledgeBaseLimit}
            />
            <InputNumber
              label="Dias de Teste"
              color="background"
              placeholder="Quantos dias de teste"
              size={{base: "sm", lg: "md"}}
              height="80px"
              borderRadius={20}
              {...register("trialDays")}
              error={errors.trialDays}
            />
          </HStack>
        </Flex>
      }
    />
  );
}
