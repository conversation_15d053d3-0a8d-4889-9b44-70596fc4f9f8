import { Flex, FlexProps, Text, useBreakpointValue, VStack } from "@chakra-ui/react";
import { FaCheck } from "react-icons/fa6";

interface CardSignUpProps extends FlexProps {
  title: string;
};

export default function CardSignUp({ title, ...rest }: CardSignUpProps) {
  const iconSize = useBreakpointValue({ base: 20, sm: 28, md: 36 });

  return (
    <Flex
      h={{base: "100px", sm: "120px", md: "125px", lg: "150px"}}
      w={{base: "full", sm: "200px", md: "175px", lg: "200px", "2xl": "250px"}}
      flexDir={"column"}
      bgColor={"chatPrimary"}
      justifyContent={"center"}
      alignItems={"center"}
      borderRadius={20}
      p={4}
      boxShadow={"sm"}
      textAlign={"center"}
      transition={"0.3s"}
      _hover={{
        transform: "scale(1.01)",
      }}
      {...rest}
    >
      <FaCheck color={"white"} size={iconSize} />
      <Text color={"white"} fontSize={{base: "10px", sm: "sm", lg: "md"}} fontWeight={"bold"} textAlign={"center"}>{title}</Text>
    </Flex>
  );
}


