import { PermissionsCheckbox } from "@/components/global/checkbox/permissions-checkbox";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Flex, HStack, Tabs } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { useController, useForm } from "react-hook-form";
import { LuUser, LuUserCog } from "react-icons/lu";
import * as yup from "yup";
import BasicBackofficeModal from "@/components/global/modal/basic-backoffice-modal";
import useBackofficePermissions from "@/hook/permissions/useBackofficePermissions";
import useAttendantPermissions from "@/hook/permissions/useAttendantPermissions";
import Account from "../../page";

type ModalCreateAccountUserBackofficeProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
  accountSecureId: string;
};

const createAccountUserSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  cellPhone: yup.string(),
  password: yup.string().required("Senha é obrigatória"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem coincidir")
    .required("Confirmação de senha é obrigatória"),
  cpf: yup.string().required("CPF é obrigatório"),
  hasAllPermissions: yup.string().optional(),
  participatesInRotation: yup.boolean().optional(),
  permissions: yup.array().of(yup.string()).optional(),
});

type CreateAccountUserFormData = yup.InferType<typeof createAccountUserSchema>;

export default function ModalAccountCreateUserBackoffice({
  openModal,
  setOpenModal,
  accountSecureId,
}: ModalCreateAccountUserBackofficeProps) {
  const { data, isLoading } = useAttendantPermissions();
  const [phoneMask, setPhoneMask] = useState("+99 (99) 9999-9999[9]");
  const [permissionsItems, setPermissionsItems] = useState<
    { label: string; value: string }[]
  >([]);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateAccountUserFormData>({
    resolver: yupResolver(createAccountUserSchema),
  });

  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);

  useEffect(() => {
    if (data) {
      setPermissionsItems(
        data.permissions.map((permission) => ({
          label: permission.description,
          value: permission.secureId,
        }))
      );
    }
  }, [data]);

  const permissions = useController({
    control,
    name: "permissions",
    defaultValue: [],
  });

  const createUser = useMutation({
    mutationFn: async (data: CreateAccountUserFormData) => {
      const { permissions, hasAllPermissions, ...postData } = data;

      await api.post("register/backoffice-account/user", {
        user: {
          permissionsSecureIds: permissions,
          hasAllPermissions: hasAllPermissions === "all" ? true : undefined,
          ...postData,
        },
        accountSecureId: accountSecureId,
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Usuário Cadastrado com Sucesso.",
        title: "Usuário Cadastrado!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["accounts-users"],
      });
      setOpenModal(false);
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: CreateAccountUserFormData) => {
    try {
      createUser.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <BasicBackofficeModal
      open={openModal}
      size="xl"
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Cadastrar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Cadastrar novo usuário"
      placement="top"
      children={
        <Tabs.Root defaultValue="user" colorPalette={"white"} size={"lg"}>
          <Tabs.List borderColor={"chatTextColor"}>
            <Tabs.Trigger
              value="user"
              border={"none"}
              _selected={{
                color: "white",
              }}
            >
              <LuUser />
              Usuário
            </Tabs.Trigger>
            <Tabs.Trigger
              value="permissions"
              _selected={{
                color: "white",
              }}
            >
              <LuUserCog />
              Permissões
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="user">
            <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
              <Input
                label="Nome"
                color="background"
                placeholder="Digite o nome do usuário"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("name")}
                error={errors.name}
              />
              <Input
                label="Email"
                color="background"
                placeholder="Digite o email do usuário"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("email")}
                error={errors.email}
              />
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <InputMaskIcon
                  label="CPF"
                  mask={"999.999.999-99"}
                  color="background"
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o CPF do usuário"
                  size={"md"}
                  {...register("cpf")}
                  error={errors.cpf}
                />
                <InputMaskIcon
                  label="Celular"
                  mask={phoneMask}
                  color="background"
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o número do atendente"
                  size={"md"}
                  {...register("cellPhone")}
                  error={errors.cellPhone}
                />
              </HStack>
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <Input
                  type="password"
                  label="Senha"
                  color="background"
                  placeholder="Digite a senha do usuário"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("password")}
                  error={errors.password}
                />
                <Input
                  type="password"
                  label="Confirmação da Senha"
                  color="background"
                  placeholder="Digite a senha novamente"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("confirmPassword")}
                  error={errors.confirmPassword}
                />
              </HStack>
            </Flex>
          </Tabs.Content>
          <Tabs.Content value="permissions">
            <Box position={"relative"} w={"100%"} h={"100%"}>
              <Checkbox
                size={"md"}
                value={"all"}
                key={"all"}
                position={"absolute"}
                top={0}
                right={0}
                {...register("hasAllPermissions")}
              >
                Conceder todas as permissões
              </Checkbox>
              <PermissionsCheckbox
                invalid={!!errors.permissions}
                onValueChange={permissions.field.onChange}
                value={permissions.field.value as string[] | undefined}
                name={permissions.field.name}
                label="Permissões"
                items={permissionsItems}
              />
            </Box>
          </Tabs.Content>
        </Tabs.Root>
      }
    />
  );
}
