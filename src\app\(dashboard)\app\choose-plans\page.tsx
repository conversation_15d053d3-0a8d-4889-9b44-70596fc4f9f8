"use client";

import { <PERSON>, Flex, Spinner, Text, useMediaQuery, VStack, Link } from "@chakra-ui/react";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Button } from "@/components/ui/button";
import "react-datepicker/dist/react-datepicker.css";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import usePlans from "@/hook/plans/usePlans";
import CardPlanApp from "../components/cards/card-plan";
import { useAuthContext } from "@/providers/AuthProvider";

const ChoosePlanSchema = yup.object().shape({
  cardNumber: yup.string().required("Número do cartão é obrigatório"),
  holderName: yup.string().required("Nome é obrigatório"),
  cvv: yup.string().required("CVV é obrigatório"),
  expirationDate: yup.string().required("Data de validade é obrigatória"),
});

type ChoosePlanFormData = yup.InferType<typeof ChoosePlanSchema>;

export default function ChoosePlan() {
  const { user } = useAuthContext();
  const userName = user?.activeAccount.userName || "";
  const currentPlan = user?.activeAccount.planSlug || "";
  const router = useRouter();
  const { data: plans, isLoading, isFetching } = usePlans();
  // const planSecureId = data?.secureId ? data.secureId : "";
  const isLoadingPlan = isLoading || isFetching;
  const [isSmallScreen] = useMediaQuery(['(max-width: 500px)'], {
    ssr: false
  });

  const {
    register,
    handleSubmit,
    setValue,
    formState,
    formState: { errors },
  } = useForm<ChoosePlanFormData>({
    resolver: yupResolver(ChoosePlanSchema),
  });

  useEffect(() => {
    if (userName) {
      setValue("holderName", userName);
    }
  }, [userName]);

  return (
    <Flex flex={1} rounded={"2xl"} alignItems={"center"} justifyContent={"center"} bgColor={"chatCardBackground"} p={8}>
      <VStack
        w={{base:"100%", md: "90%"}}
        h={"full"}
        alignItems={"start"}
        gap={2}
      >
        {isLoadingPlan ? <Spinner color="chatPrimary" /> : null}
        <Text color={"chatPrimary"} fontSize={"2xl"} fontWeight={"bold"}>Escolha seu plano</Text>
        <Flex
          flex={1}
          w={"100%"}
          borderRadius={10}
          alignItems={{ base: "center", md: "start" }}
          justifyContent={{ base: "center", md: "space-between" }}
          flexDirection={{ base: "column", md: "row" }}
          gap={4}
        >
          {plans && !isLoadingPlan ? plans.map((plan: any) => (
            <CardPlanApp
              key={plan.secureId}
              name={plan.name}
              slug={plan.slug}
              description={plan.description}
              price={plan.price}
              details={plan.details}
              isCurrentPlan={plan.slug === currentPlan}
            />
            )) : null
          }
        </Flex>

        {!plans && !isLoadingPlan ? (
          <Box w="100%" textAlign="center" p={4} border="1px solid #d0d0d0" borderRadius={10}>
            <Text fontSize="lg" color="gray.600" mb={2}>
              O plano solicitado não foi encontrado.
            </Text>
            <Link href="/sign-up/starter" color="chatPrimary" fontSize="md">
              Clique aqui para ir ao plano base.
            </Link>
          </Box>
        ) : null}
        <Button
          bgColor={"transparent"}
          alignSelf={"end"}
          border={"1px solid"}
          borderColor={"gray.400"}
          color="gray.400"
          w="auto"
          borderRadius={20}
          size={"md"}
          fontWeight="700"
          _hover={{
            color: "chatPrimary",
            bgColor: "transparent",
            border: "1px solid",
            borderColor: "chatPrimary",
          }}
          onClick={() => router.push('/app')}
        >
          Continuar com plano atual
        </Button>
      </VStack>
    </Flex>
  );
}
