import { MetaDTO } from "./meta.dto";

export type GetAllAccountUsersDto = {
  meta: MetaDTO;
  data: GetAccountUserDto[]
}

export type GetAccountUserDto = {
  secureId: string;
  name: string;
  email: string;
  cpf: string;
  isOwner: boolean;
  cellphone: string | null;
  hasAllPermissions: boolean;
  permissions: string[];
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}