"use client";
import ChatFooterWebSocket from "@/components/global/chat/chat-footer-websocket";
import MessageBubbleInjectChat from "@/components/global/chat/message-bubble-inject-chat";
import { toaster } from "@/components/ui/toaster";
import { useSocket } from "@/providers/SocketProvider";
import truncateText from "@/utils/funcs/truncate-text";
import { ChatMessagesHistoryDto } from "@/utils/types/DTO/chat-messages-history.dto";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import {
  Box,
  Center,
  Flex,
  HStack,
  Spinner,
  VStack,
  Text,
  IconButton,
} from "@chakra-ui/react";
import { format, set } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { IoCloseOutline } from "react-icons/io5";
import { v4 as uuidV4 } from "uuid";

type ChatProps = {
  secureId: string;
  chatData: GetOneChatDtoInput;
  sessionSecureId: string;
  userSecureId: string;
  chatHistory?: ChatMessagesHistoryDto[];
};

interface ReplyToMessage {
  secureId: string;
  message: string;
}

interface Message {
  secureId: string;
  message: string;
  type?: "text" | "file" | "image" | "audio";
  urlFile?: string | null;
  isMine: boolean;
  messageTime: string;
  avatarUrl?: string;
  attendantName: string;
  messageStatus: string;
  isSameSender: boolean;
  replyTo?: string;
  replyMessage?: string;
}

export default function Chat({
  secureId,
  chatData,
  sessionSecureId,
  chatHistory,
  userSecureId,
}: ChatProps) {
  const { wsServiceRef, isConnected } = useSocket();
  const chatEndRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [replyTo, setReplyTo] = useState<ReplyToMessage | null>(null);

  const scrollToBottom = () => {
    setTimeout(() => {
      chatEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
        inline: "nearest",
      });
    }, 100);
  };

  useEffect(() => {
    if (chatData.welcomeMessage) {
      setMessages([
        {
          attendantName: "Atendente",
          avatarUrl: chatData.upload.urlCdn,
          isMine: false,
          message: chatData.welcomeMessage,
          isSameSender: false,
          messageStatus: "read",
          messageTime: format(new Date(), "HH:mm"),
          secureId: uuidV4(),
        },
      ]);
    }
    if (chatHistory && chatHistory.length > 0) {
      setMessages((currentMessages) => {
        // Filtra mensagens que já estão presentes no estado com o mesmo secureId
        const filteredChatHistory = chatHistory.filter(
          (message) =>
            !currentMessages.some(
              (existing) => existing.secureId === message.secureId
            )
        );

        const newMessages = filteredChatHistory.map((message, index) => {
          const isMine = message.messageDirection === "received";

          // Get previous message from the filtered history array instead
          const previousMessage = filteredChatHistory[index - 1];

          const isSameSender = previousMessage
            ? previousMessage.messageDirection === message.messageDirection
            : false;

          let replyMessage: string | undefined = undefined;
          if (message.replyTo) {
            // Encontrar a mensagem original no histórico
            const repliedMessage = filteredChatHistory.find(
              (m) => m.secureId === message.replyTo
            );
            if (repliedMessage) {
              const message = repliedMessage.receiveMessage
                ? repliedMessage.receiveMessage
                : repliedMessage.sendMessage;
              replyMessage = message ?? undefined;
            }
          }

          return {
            secureId: message.secureId,
            message: message.receiveMessage
              ? message.receiveMessage
              : (message.sendMessage ?? ""),
            isMine: isMine,
            messageTime: format(new Date(message.createdAt), "HH:mm"),
            attendantName: message.attendantName ?? "",
            messageStatus: "read",
            type: message.type,
            urlFile: message.urlFile,
            replyTo: message.replyTo,
            replyMessage: replyMessage,
            isSameSender,
          };
        });

        return [...currentMessages, ...newMessages];
      });
    }
  }, [chatHistory, chatData]);

  useEffect(() => {
    const connectWebSocket = async () => {
      try {
        wsServiceRef.onNewMessage((newMessage) => {
          setMessages((currentMessages) => {
            // Verificar se a mensagem já existe
            const messageExists = currentMessages.some(
              (msg) => msg.secureId === newMessage.secureId
            );

            if (messageExists) {
              return currentMessages;
            }

            // Determinar se a mensagem é do usuário atual (minha)
            const isMine = newMessage.messageDirection === "received";

            // Obter a última mensagem
            const previousMessage =
              currentMessages.length > 0
                ? currentMessages[currentMessages.length - 1]
                : null;

            const isSameSender = previousMessage
              ? previousMessage.isMine === isMine
              : false;

            let replyMessage: string | undefined = undefined;
            if (newMessage.replyTo) {
              const repliedMessage = currentMessages.find(
                (msg) => msg.secureId === newMessage.replyTo
              );
              if (repliedMessage) {
                replyMessage = repliedMessage.message;
              }
            }

            const formattedMessage = {
              secureId: newMessage.secureId,
              message: newMessage.receiveMessage
                ? newMessage.receiveMessage
                : (newMessage.sendMessage ?? ""),
              isMine: isMine,
              messageTime: format(new Date(newMessage.createdAt), "HH:mm"),
              avatarUrl: undefined,
              attendantName: newMessage.attendantName ?? "",
              messageStatus: "read",
              type: newMessage.type,
              urlFile: newMessage.urlFile,
              replyTo: newMessage.replyTo,
              replyMessage: replyMessage,
              isSameSender: isSameSender,
            };

            return [...currentMessages, formattedMessage];
          });

          scrollToBottom();
        });

        // if (userSecureId && sessionSecureId) {
        //   await wsServiceRef.joinRoom(userSecureId, sessionSecureId);
        // }
      } catch (error) {
        console.error("Failed to connect to WebSocket or join room:", error);
      }
    };

    if (isConnected) {
      connectWebSocket();
    }
  }, [wsServiceRef, userSecureId, sessionSecureId, isConnected]);

  if (!userSecureId || !sessionSecureId) {
    return (
      <Center>
        <Spinner />
      </Center>
    );
  }

  const handleSendMessage = async (
    content: string,
    file?: { file: File; type: "file" | "image" | "audio" }
  ) => {
    if (!wsServiceRef?.isConnected()) {
      toaster.create({
        title: "Erro ao enviar mensagem",
        description: "Você está desconectado. Reconectando...",
        type: "error",
        duration: 3000,
      });
      return;
    }

    try {
      await wsServiceRef.sendMessage({
        chatId: secureId,
        content,
        isAttendant: false,
        type: file?.type,
        messageDirection: "received",
        sessionId: sessionSecureId,
        customerId: userSecureId,
        replyTo: replyTo?.secureId,
        file: file?.file ?? undefined, 
      });

      setReplyTo(null);
    } catch (error) {
      console.error("Failed to send message:", error);
      toaster.create({
        title: "Erro ao enviar mensagem",
        description: "Não foi possível enviar sua mensagem. Tente novamente.",
        type: "error",
        duration: 3000,
      });
    }
  };

  const handleReplyToMessage = (secureId: string, message: string) => {
    setReplyTo({
      secureId,
      message,
    });
  };

  const cancelReply = () => {
    setReplyTo(null);
  };

  return (
    <>
      <Flex
        w={"100%"}
        flex={1}
        px={2}
        gap={5}
        pb={2}
        flexDir="column"
        alignItems="flex-end"
        h={{ base: "calc(100vh - 80px)", md: "calc(100vh - 180px)" }}
        overflowY="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            width: "6px",
            marginBottom: "10px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
            "&:hover": {
              background: "#A6A6A6",
            },
          },
        }}
      >
        <VStack flex={1} w="100%" justifyContent="flex-end">
          <>
            {messages.map((message) => {
              return (
                <MessageBubbleInjectChat
                  key={message.secureId}
                  message={message.message}
                  isMine={message.isMine}
                  messageType={message.type}
                  urlFile={message.urlFile}
                  attendantName={message.attendantName}
                  messageTime={message.messageTime}
                  avatarUrl={message.avatarUrl}
                  messageStatus={message.messageStatus}
                  isSameSender={message.isSameSender}
                  customerMessageBgColor={chatData.customerMessageBubbleColor}
                  customerMessageColor={chatData.customerMessageTextColor}
                  attendantMessageBgColor={chatData.attendantMessageBubbleColor}
                  attendantMessageColor={chatData.attendantMessageTextColor}
                  attendantAvatarColor={chatData.chatButtonColor}
                  primaryColor={chatData.chatButtonColor}
                  secureId={message.secureId}
                  onReply={handleReplyToMessage}
                  replyTo={message.replyTo}
                  replyMessage={message.replyMessage}
                />
              );
            })}
            <div ref={chatEndRef} />
          </>
        </VStack>
      </Flex>
      <>
        {replyTo && (
          <Box w="100%" position={"relative"}>
            <Box
              w="100%"
              borderTopRadius="md"
              bg="gray.50"
              p={2}
              mr={2}
              position="absolute"
              bottom={-2}
              borderTop="1px solid"
              borderLeft="1px solid"
              borderRight="1px solid"
              borderColor="gray.200"
              zIndex={100}
            >
              <HStack gap={2} align="flex-start" top={-10}>
                <Box
                  w={1}
                  h="100%"
                  bg="chatPrimary"
                  borderRadius="full"
                  mr={2}
                />
                <VStack align="start" flex={1} gap={0}>
                  <Text fontWeight="bold" fontSize="sm" color="chatPrimary">
                    Respondendo mensagem
                  </Text>
                  <Text
                    fontSize="sm"
                    color="gray.600"
                    maxW="calc(100% - 30px)"
                    overflow="hidden"
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                  >
                    {truncateText(replyTo.message, 60)}
                  </Text>
                </VStack>
                <IconButton
                  aria-label="Cancel reply"
                  size="sm"
                  color="chatPrimary"
                  _hover={{
                    bg: "chatPrimary",
                    color: "white",
                  }}
                  variant={"ghost"}
                  onClick={cancelReply}
                >
                  <IoCloseOutline />
                </IconButton>
              </HStack>
            </Box>
          </Box>
        )}
        <ChatFooterWebSocket
          onSendMessage={handleSendMessage}
          buttonBgColor={chatData.chatButtonColor}
          inputBgColor={chatData.inputChatBgColor}
          inputMessageColor={chatData.inputChatTextColor}
        />
      </>
    </>
  );
}
