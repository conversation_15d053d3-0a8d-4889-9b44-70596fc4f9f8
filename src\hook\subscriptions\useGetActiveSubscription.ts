import { api } from "@/services/api";
import { GetAccountSubscriptionDto } from "@/utils/types/DTO/accounts-subscriptions.dto"; import { useQuery } from "@tanstack/react-query";

async function getActiveSubscription() {
  const { data } = await api.get<any>("/subscriptions");

  return data;
}

export function useGetActiveSubscription() {
  return useQuery({
    queryFn: async () => await getActiveSubscription(),
    queryKey: ["active-subscription"],
  });
}






