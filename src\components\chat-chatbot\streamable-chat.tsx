"use client";
import { Box, Flex, VStack, Text } from "@chakra-ui/react";
import { useChat } from "ai/react";
import Cookies from "js-cookie";
import { format } from "date-fns";
import { useEffect, useRef } from "react";
import MessageB<PERSON>bleChatBot from "./message-bubble";
import Chat<PERSON>ooterChatBot from "./chat-footer";

type StreamableChatProps = {
  chatbotSecureId?: string;
};

export default function StreamableChat({
  chatbotSecureId,
}: StreamableChatProps) {
  const cookie = Cookies.get("__PlyrChat_Token");
  const chatEndRef = useRef<HTMLDivElement>(null);
  const { messages, input, handleInputChange, handleSubmit, isLoading } =
    useChat({
      api: `${process.env.NEXT_PUBLIC_API_URL}chatbots/${chatbotSecureId}`,
      streamProtocol: "text",
      body: {
        chatbotSecureId,
      },
      headers: {
        Authorization: `Bearer ${cookie}`,
      },
    });

  const scrollToBottom = () => {
    setTimeout(() => {
      chatEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
        inline: "nearest",
      });
    }, 100);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <Box bgColor={"chatBackground"} rounded={"2xl"} flex={1} p={2}>
      <Flex
        flex={1}
        px={2}
        gap={5}
        pb={2}
        h={{ base: "400px", "2xl": "600px" }}
        flexDir="column"
        alignItems="flex-end"
        overflowY="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            width: "6px",
            marginBottom: "10px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
            "&:hover": {
              background: "#A6A6A6",
            },
          },
        }}
      >
        <VStack flex={1} w="100%" justifyContent="flex-end">
          <>
            {messages.map((message) => {
              //NOTE: this component don´t support 2 following messages from the same user
              return (
                <MessageBubbleChatBot
                  key={message.id}
                  message={message.content}
                  isMine={message.role === "user"}
                  attendantName={"IA"}
                  messageTime={format(new Date(), "HH:mm")}
                  messageStatus={"read"}
                  customerMessageBgColor={null}
                  customerMessageColor={null}
                  attendantMessageBgColor={null}
                  attendantMessageColor={null}
                  attendantAvatarColor={null}
                />
              );
            })}
            <div ref={chatEndRef} />
          </>
        </VStack>
      </Flex>
      <ChatFooterChatBot
        handleInputChange={handleInputChange}
        input={input}
        handleSubmit={handleSubmit}
        buttonBgColor={null}
        inputBgColor={null}
        inputMessageColor={null}
        isLoading={isLoading}
      />
    </Box>
  );
}
