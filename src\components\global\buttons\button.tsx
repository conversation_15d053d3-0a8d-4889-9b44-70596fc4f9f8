import { Button } from "@/components/ui/button";
import { Tooltip } from "@/components/ui/tooltip";
import { ConditionalValue } from "@chakra-ui/react";

type DefaultButtonProps = {
  children: React.ReactNode;
  onClick?: () => void;
  buttonColor?: string;
  disabled?: boolean;
  size?: ConditionalValue<
    "sm" | "md" | "lg" | "xl" | "2xl" | "2xs" | "xs" | undefined
  >;
  loading?: boolean;
  tooltipContent?: string;
  isRounded?: boolean;
};

export default function DefaultButton({
  children,
  onClick,
  buttonColor = "chatPrimary",
  disabled = false,
  loading = false,
  isRounded = false,
  size = "md",
  tooltipContent,
}: DefaultButtonProps) {
  const buttonElement = (
    <Button
      onClick={onClick}
      bgColor={buttonColor}
      loading={loading}
      disabled={disabled}
      w={isRounded ? "9" : ""}
      h={isRounded ? "9" : ""}
      borderRadius={20}
      size={size}
      rounded={"full"}
      transitionDuration={"0.2s"}
      _hover={{
        // bgColor: "chatPrimary",
        transform: "scale(1.05)",
        transitionDuration: "0.2s",
      }}
    >
      {children}
    </Button>
  );

  return (
    <>
      {tooltipContent ? (
        <Tooltip content={tooltipContent}>{buttonElement}</Tooltip>
      ) : (
        buttonElement
      )}
    </>
  );
}
