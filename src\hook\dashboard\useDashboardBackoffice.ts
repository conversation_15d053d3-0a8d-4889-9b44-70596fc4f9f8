import { api } from "@/services/api";
import { GetDashboardDto } from "@/utils/types/DTO/dashboard.dto";
import { useQuery } from "@tanstack/react-query";

async function getDashboardBackoffice(startDate?: string, endDate?: string) {

  const { data } = await api.get<GetDashboardDto>(
    `/backoffice/dashboard`,
    {
      params: {
        startDate,
        endDate
      }
    }
  );

  return data;
}

export default function useDashboardBackoffice(startDate?: string, endDate?: string) {
  return useQuery({
    queryFn: async () => await getDashboardBackoffice(startDate, endDate),
    queryKey: ["dashboard-backoffice", startDate, endDate],
    refetchInterval: false,
  });
}
