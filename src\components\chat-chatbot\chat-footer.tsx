import { HStack, I<PERSON><PERSON><PERSON>on } from "@chakra-ui/react";
import { TbSend } from "react-icons/tb";
import { FormEventHandler, useRef } from "react";
import { InputMessage } from "../global/chat/input-message";

interface ChatFooterStreamProps {
  handleSubmit: FormEventHandler<HTMLDivElement> | undefined;
  handleInputChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>
  ) => void;
  input: string;
  isLoading: boolean;
  inputMessageColor: string | null;
  inputBgColor: string | null;
  buttonBgColor: string | null;
}

export default function ChatFooterChatBot({
  handleSubmit,
  buttonBgColor,
  inputBgColor,
  handleInputChange,
  isLoading,
  input,
  inputMessageColor,
}: ChatFooterStreamProps) {
  return (
    <HStack w="100%" as="form" onSubmit={handleSubmit}>
      <InputMessage
        name="message"
        size={"sm"}
        rounded={20}
        color={inputMessageColor || "chatPrimary"}
        bgColor={inputBgColor || "chatCardColor"}
        placeholder="Digite a mensagem"
        value={input}
        onChange={handleInputChange}
        _focus={{
          borderColor: inputBgColor || "chatTextColor",
        }}
        _hover={{
          borderColor: inputBgColor || "chatTextColor",
        }}
      />
      <IconButton
        type="submit"
        rounded="full"
        bgColor={buttonBgColor || "chatPrimary"}
        color="white"
        aria-label="Enviar mensagem"
        size="md"
        borderWidth={2}
        transition="all 0.3s"
        disabled={isLoading}
        _active={{
          bgColor: "transparent",
          borderColor: "chatPrimary",
          color: "chatPrimary",
          borderWidth: 2,
        }}
      >
        <TbSend />
      </IconButton>
    </HStack>
  );
}
