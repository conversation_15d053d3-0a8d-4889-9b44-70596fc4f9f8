import useAttendant from "@/hook/config/useAttendant";
import { Center, Flex, HStack, Spinner, Text, VStack, useBreakpointValue } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import HeaderTab from "../header-tab";
import AttendantCard from "./attendant-card";
import { useState } from "react";
import ModalCreateAttendant from "./create-attendant/modal-create-attendant";

export default function AttendantTab() {
  const { data, isLoading, isFetching } = useAttendant();
  const [openCreateModal, setOpenCreateModal] = useState(false);

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  const navigate = useRouter();

  return (
    <>
      <Flex
        h={"100%"}
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
      >
        {!data || isLoading || isFetching ? (
          <Center w={"100%"} h={"100%"}>
            <Spinner color={"chatPrimary"}></Spinner>
          </Center>
        ) : (
          <VStack flex={1} alignItems={"flex-start"} mt={{ base: 8, md: 0 }}>
            <HeaderTab
              title="Atendentes"
              buttonTitle={!isMobile ? "Adicionar Atendente" : undefined}
              onClick={!isMobile ? () => setOpenCreateModal(true) : undefined}
            />
            {isMobile ? (
              <VStack
                flex={1}
                w={"100%"}
                alignItems={"stretch"}
                gap={3}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum Atendente Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data.data.map((attendant) => (
                      <AttendantCard
                        key={attendant.secureId}
                        email={attendant.email}
                        name={attendant.name}
                        secureId={attendant.secureId}
                      />
                    ))}
                  </>
                )}
              </VStack>
            ) : (
              <HStack
                flex={1}
                flexWrap={"wrap"}
                w={"100%"}
                alignContent={"start"}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum Atendente Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data.data.map((attendant) => (
                      <AttendantCard
                        key={attendant.secureId}
                        email={attendant.email}
                        name={attendant.name}
                        secureId={attendant.secureId}
                      />
                    ))}
                  </>
                )}
              </HStack>
            )}
          </VStack>
        )}
      </Flex>
      <ModalCreateAttendant
        openModal={openCreateModal}
        setOpenModal={setOpenCreateModal}
      />
    </>
  );
}
