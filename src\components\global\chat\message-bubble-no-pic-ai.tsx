import { Avatar } from "@/components/ui/avatar";
import { Box, HStack, VStack, Text, Link } from "@chakra-ui/react";
import React from "react";
import { IoCheckmarkDoneOutline, IoCheckmarkOutline } from "react-icons/io5";
import { LuExternalLink } from "react-icons/lu";
import ReactMarkdown from "react-markdown";
import remarkGfm from 'remark-gfm';

type MessageBubbleAIProps = {
  message: string;
  attendantName?: string;
  isMine: boolean;
  isFirstMessage: boolean;
  avatarUrl?: string;
  messageTime: string;
  messageStatus: string;
  customerMessageColor: string | null;
  customerMessageBgColor: string | null;
  attendantMessageColor: string | null;
  attendantMessageBgColor: string | null;
  attendantAvatarColor: string | null;
  avatarBgColor: string | null;
};

export default function MessageBubbleInjectChat({
  message,
  isMine,
  avatarUrl,
  messageTime,
  isFirstMessage,
  attendantName,
  messageStatus,
  attendantMessageBgColor,
  attendantMessageColor,
  customerMessageBgColor,
  customerMessageColor,
  attendantAvatarColor,
  avatarBgColor,
}: MessageBubbleAIProps) {
  return (
    <>
      {isMine ? (
        <HStack w={"100%"} justifyContent={"flex-end"} mb={!isFirstMessage ? 0 : 5}>
          <HStack
            flexDir={"row-reverse"}
            alignItems={"start"}
            gap={5}
            maxWidth={"80%"}
            mr={2}
          >
            <VStack position={"relative"}>
              <Box
                position="absolute"
                bottom={0}
                right="-15px"
                top="30px"
                width={0}
                height={0}
                zIndex={0}
                borderLeft="20px solid transparent"
                borderRight="20px solid transparent"
                borderTop="20px solid"
                borderTopColor={customerMessageBgColor || "white"}
              />
              <Box
                bgColor={customerMessageBgColor || "white"}
                p={2.5}
                zIndex={2}
                rounded={10}
              >
                <VStack align={"start"} justify={"space-between"}>
                  <Box color={customerMessageColor || "black"}>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        ol: ({ children }) => (
                          <Box as="ol" pl={4} my={2}>
                            {children}
                          </Box>
                        ),
                        ul: ({ children }) => (
                          <Box as="ul" pl={4} my={2}>
                            {children}
                          </Box>
                        ),
                        li: ({ children }) => (
                          <Box
                            as="li"
                            display="list-item"
                            listStylePosition={"initial"}
                            my={1}
                            alignItems="flex-start"
                            css={{
                              '& p': {
                                display: 'inline',
                                marginTop: '0',
                                marginBottom: '0',
                              },
                              '& strong': {
                                display: 'inline',
                                whiteSpace: 'normal',
                              }
                            }}
                          >
                            {children}
                          </Box>
                        ),
                        p: ({ children }) => (
                          <Text
                            display="block"
                            width="100%"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                            whiteSpace="pre-wrap"
                            css={{
                              '& strong': {
                                display: 'inline',
                                whiteSpace: 'normal',
                              }
                            }}
                          >
                            {children}
                          </Text>
                        ),
                        a: ({ href, children }) => (
                          <Link
                            href={href}
                            color="#FD2264"
                            textDecoration="underline"
                            target="_blank"
                            rel="noopener noreferrer"
                            maxWidth="100%"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                          >
                            {children}
                          </Link>
                        ),
                        strong: ({ children }) => (
                          <Text
                            as="strong"
                            fontWeight="bold"
                            display="inline"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                            whiteSpace="normal"
                          >
                            {children}
                          </Text>
                        ),
                        code: ({ className, children }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          const isCodeBlock = match && className?.includes('language-');

                          return isCodeBlock ? (
                            <Box my={4} rounded="md" overflow="hidden">
                              <Box
                                as="pre"
                                bg="gray.800"
                                color="white"
                                p={4}
                                rounded="md"
                                fontSize="14px"
                                fontFamily="monospace"
                                overflowX="auto"
                                whiteSpace="pre"
                              >
                                <Box as="code" display="block">
                                  {String(children).replace(/\n$/, '')}
                                </Box>
                              </Box>
                            </Box>
                          ) : (
                            <Text
                              as="code"
                              bg="gray.100"
                              p="1"
                              rounded="sm"
                              fontSize="sm"
                              fontFamily="monospace"
                              display="inline"
                              wordBreak="break-all"
                              overflowWrap="break-word"
                            >
                              {children}
                            </Text>
                          );
                        },
                      }}
                      className="markdown-content"
                    >
                      {message}
                    </ReactMarkdown>
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                    {/* {messageStatus === "delivered" && (
                      <IoCheckmarkOutline color="#FD2264" />
                    )}
                    {messageStatus === "read" && (
                      <IoCheckmarkDoneOutline color="#FD2264" />
                    )}
                    {messageStatus === "pending" && (
                      <IoCheckmarkOutline color="#84767A" />
                    )} */}
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      ) : (
        <HStack w={"100%"} justifyContent={"flex-start"}>
          <HStack alignItems={"start"} gap={5} maxWidth={"95%"} ml={0}>
            {isFirstMessage && (
              <Avatar
                size={"lg"}
                name={attendantName}
                src={!!avatarUrl ? avatarUrl : undefined}
                bgColor={avatarBgColor || "gray.200"}
              />
            )}
            <VStack position={"relative"}>
              {isFirstMessage && (
                <>
                  <Text
                    fontSize={"xs"}
                    color={"gray.500"}
                    position={"absolute"}
                    top={-5}
                    left={-1}
                    w={"40"}
                    fontWeight={600}
                  >
                    {attendantName}
                  </Text>

                  <Box
                    position="absolute"
                    bottom={0}
                    left="-15px"
                    top="30px"
                    width={0}
                    height={0}
                    zIndex={0}
                    borderLeft="20px solid transparent"
                    borderRight="20px solid transparent"
                    borderTop="20px solid"
                    borderTopColor={attendantMessageBgColor || "white"}
                  />
                </>
              )}
              <Box
                bgColor={attendantMessageBgColor || "white"}
                p={2.5}
                ml={isFirstMessage ? 0 : 16}
                rounded={10}
                zIndex={1}
              >
                <VStack align={"start"} justify={"space-between"}>
                  <Box color={attendantMessageColor || "black"}>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        ol: ({ children }) => (
                          <Box as="ol" pl={4} my={2}>
                            {children}
                          </Box>
                        ),
                        ul: ({ children }) => (
                          <Box as="ul" pl={4} my={2}>
                            {children}
                          </Box>
                        ),
                        li: ({ children }) => (
                          <Box
                            as="li"
                            display="list-item"
                            listStylePosition={"initial"}
                            my={1}
                            alignItems="flex-start"
                            css={{
                              '& p': {
                                display: 'inline',
                                marginTop: '0',
                                marginBottom: '0',
                              },
                              '& strong': {
                                display: 'inline',
                                whiteSpace: 'normal',
                              }
                            }}
                          >
                            {children}
                          </Box>
                        ),
                        p: ({ children }) => (
                          <Text
                            display="block"
                            width="100%"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                            whiteSpace="pre-wrap"
                            css={{
                              '& strong': {
                                display: 'inline',
                                whiteSpace: 'normal',
                              }
                            }}
                          >
                            {children}
                          </Text>
                        ),
                        a: ({ href, children }) => (
                          <Link
                            href={href}
                            color="#FD2264"
                            textDecoration="underline"
                            target="_blank"
                            rel="noopener noreferrer"
                            maxWidth="100%"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                          >
                            {children}
                          </Link>
                        ),
                        strong: ({ children }) => (
                          <Text
                            as="strong"
                            fontWeight="bold"
                            display="inline"
                            wordBreak="break-word"
                            overflowWrap="break-word"
                            whiteSpace="normal"
                          >
                            {children}
                          </Text>
                        ),
                        code: ({ className, children }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          const isCodeBlock = match && className?.includes('language-');

                          return isCodeBlock ? (
                            <Box my={4} rounded="md" overflow="hidden">
                              <Box
                                as="pre"
                                bg="gray.800"
                                color="white"
                                p={4}
                                rounded="md"
                                fontSize="14px"
                                fontFamily="monospace"
                                overflowX="auto"
                                whiteSpace="pre"
                              >
                                <Box as="code" display="block">
                                  {String(children).replace(/\n$/, '')}
                                </Box>
                              </Box>
                            </Box>
                          ) : (
                            <Text
                              as="code"
                              bg="gray.100"
                              p="1"
                              rounded="sm"
                              fontSize="sm"
                              fontFamily="monospace"
                              display="inline"
                              wordBreak="break-all"
                              overflowWrap="break-word"
                            >
                              {children}
                            </Text>
                          );
                        },
                        blockquote: ({ children }) => (
                          <Box
                            as="blockquote"
                            borderLeftWidth="4px"
                            borderLeftColor="gray.200"
                            pl={4}
                            py={2}
                            my={4}
                            bg="gray.50"
                            rounded="sm"
                          >
                            {children}
                          </Box>
                        ),
                        table: ({ children }) => (
                          <Box
                            as="table"
                            width="100%"
                            my={4}
                            borderWidth="1px"
                            borderColor="gray.200"
                            rounded="sm"
                            overflow="hidden"
                          >
                            {children}
                          </Box>
                        ),
                        thead: ({ children }) => (
                          <Box as="thead" bg="gray.50">
                            {children}
                          </Box>
                        ),
                        tbody: ({ children }) => (
                          <Box as="tbody">
                            {children}
                          </Box>
                        ),
                        tr: ({ children }) => (
                          <Box as="tr" borderBottomWidth="1px" borderBottomColor="gray.200">
                            {children}
                          </Box>
                        ),
                        th: ({ children }) => (
                          <Box as="th" p={2} fontWeight="bold" textAlign="left">
                            {children}
                          </Box>
                        ),
                        td: ({ children }) => (
                          <Box as="td" p={2}>
                            {children}
                          </Box>
                        ),
                      }}
                      className="markdown-content"
                    >
                      {message}
                    </ReactMarkdown>
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                    {/* {messageStatus === "delivered" && (
                      <IoCheckmarkOutline color="#FD2264" />
                    )}
                    {messageStatus === "read" && (
                      <IoCheckmarkDoneOutline color="#FD2264" />
                    )}
                    {messageStatus === "pending" && (
                      <IoCheckmarkOutline color="#84767A" />
                    )} */}
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      )}
    </>
  );
}
