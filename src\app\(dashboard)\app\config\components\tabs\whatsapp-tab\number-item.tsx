import { IconBusinessNoChakra } from "@/components/global/Icon/IconBusiness";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Center, Flex, HStack, Icon, Text, useBreakpointValue } from "@chakra-ui/react";
import { AxiosError } from "axios";
import { useState } from "react";
import { FaWhatsapp } from "react-icons/fa";
import { LuBot, LuGitFork, LuPencil, LuScanQrCode, LuTrash2 } from "react-icons/lu";
import WppEditModal from "./wpp-edit-modal";

type NumberItemProps = {
  number: string;
  secureId: string;
  isBusiness: boolean;
  chatBotSecureId: string | null;
  isActive: boolean;
};

const formatPhoneNumber = (phoneNumber: string) => {
  const countryCode = phoneNumber.slice(0, 2);
  const areaCode = phoneNumber.slice(2, 4);
  const firstPart = phoneNumber.slice(4, 9);
  const secondPart = phoneNumber.slice(9, 13);
  return `+${countryCode} (${areaCode}) ${firstPart}-${secondPart}`;
};

export default function NumberItem({
  number,
  secureId,
  isBusiness,
  chatBotSecureId,
  isActive,
}: NumberItemProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openWebhookModal, setOpenWebhookModal] = useState(false);
  const [openScanModal, setOpenScanModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [webhookToken, setWebhookToken] = useState<
    {
      webhook: string;
      accessToken: string;
    } | undefined
  >(undefined)
  const [QRCode, setQRCode] = useState<
    | {
      pairingCode: string;
      qrCode: string;
    }
    | undefined
  >(undefined);

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  const handleScan = async () => {
    try {
      const { data } = await api.get<{
        pairingCode: string;
        base64: string;
      }>(`/whatsapp/integration/login/${secureId}`);

      setQRCode({
        pairingCode: data.pairingCode,
        qrCode: data.base64,
      });

      setOpenScanModal(true);
    } catch (e) {
    }
  };

  const handleSeeIntegrationMeta = async () => {
    try {
      const { data } = await api.get(`/whatsapp-integration/${secureId}`);
      const webhookPublicMeta = process.env.NEXT_PUBLIC_API_META ?? "https://api.plyrchat.com.br/meta/webhook";
      const webhookUrl = `${webhookPublicMeta}?secureId=${secureId}`;

      setWebhookToken({
        accessToken: data.webhookToken,
        webhook: webhookUrl,
      });

      setOpenWebhookModal(true);
    } catch (e) {
      if (e instanceof AxiosError) {
        toaster.create({
          description: e?.response?.data.message || "Erro ao gerar QRCode",
          title: "Erro ao gerar QRCode",
          type: "error",
        });
      }
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    await api.delete(`/whatsapp/integration/${secureId}`);
    await queryClient.invalidateQueries({
      queryKey: ["whatsapp-integrations"],
    });
    toaster.create({
      description: "Número de Whatsapp deletado com sucesso",
      title: "Número de Whatsapp deletado com sucesso",
      type: "success",
    });
    setIsDeleting(false);
    setOpenDeleteModal(false);
  };

  const handleEdit = () => {
    setIsOpen(true);
  };

  return (
    <>
      {isMobile ? (
        // Mobile Layout - Linear/Row Format
        <Flex
          bgColor={"chatBackground"}
          w={"100%"}
          p={4}
          rounded={"2xl"}
          borderWidth={1}
          borderColor={"transparent"}
          position={"relative"}
          alignItems={"center"}
          justifyContent={"space-between"}
          gap={3}
        >
          <Box
            position="absolute"
            top="2"
            left="2"
            w="10px"
            h="10px"
            bgColor={isActive ? "green.500" : "red.500"}
            rounded="full"
          />
          <HStack gap={3} flex={1} ml={4}>
            <Icon color={"green.500"} size={"lg"}>
              {!isBusiness ? (
                <FaWhatsapp />
              ) : (
                <IconBusinessNoChakra size={24} color={"#22c55e"} />
              )}
            </Icon>
            <Text color={"chatTextColor"} fontSize={"sm"} fontWeight={"medium"}>
              {formatPhoneNumber(number)}
            </Text>
          </HStack>
          <HStack gap={2}>
            <Box
              w={"30px"}
              h={"30px"}
              rounded={"full"}
              fontWeight="700"
              bgColor="chatPrimary"
              color="white"
              border={"1px solid transparent"}
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
                cursor: "pointer",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              display={"flex"}
              alignItems={"center"}
              justifyContent={"center"}
              onClick={handleEdit}
            >
              <Icon size={"xs"}>
                <LuBot />
              </Icon>
            </Box>
            {!isBusiness ? (
              <Box
                w={"30px"}
                h={"30px"}
                rounded={"full"}
                fontWeight="700"
                bgColor="chatPrimary"
                color="white"
                border={"1px solid transparent"}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                  cursor: "pointer",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
                display={"flex"}
                alignItems={"center"}
                justifyContent={"center"}
                onClick={handleScan}
              >
                <Icon size={"xs"}>
                  <LuScanQrCode />
                </Icon>
              </Box>
            ) : (
              <Box
                w={"30px"}
                h={"30px"}
                rounded={"full"}
                fontWeight="700"
                bgColor="chatPrimary"
                color="white"
                border={"1px solid transparent"}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                  cursor: "pointer",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
                display={"flex"}
                alignItems={"center"}
                justifyContent={"center"}
                onClick={handleSeeIntegrationMeta}
              >
                <Icon size={"xs"}>
                  <LuGitFork />
                </Icon>
              </Box>
            )}
            <Box
              w={"30px"}
              h={"30px"}
              rounded={"full"}
              fontWeight="700"
              bgColor="red.500"
              color="white"
              border={"1px solid transparent"}
              transitionDuration={"0.2s"}
              _hover={{
                color: "red.500",
                bgColor: "transparent",
                borderColor: "red.500",
                cursor: "pointer",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              display={"flex"}
              alignItems={"center"}
              justifyContent={"center"}
              onClick={() => setOpenDeleteModal(true)}
            >
              <Icon size={"xs"}>
                <LuTrash2 />
              </Icon>
            </Box>
          </HStack>
        </Flex>
      ) : (
        // Desktop Layout - Card Format
        <Flex
          bgColor={"chatBackground"}
          h={"140px"}
          p={4}
          rounded={"2xl"}
          borderWidth={1}
          borderColor={"transparent"}
          alignContent={"center"}
          position={"relative"}
          flexDir={"column"}
          gap={3}
        >
          <Box
            position="absolute"
            top="2"
            left="2"
            w="10px"
            h="10px"
            bgColor={isActive ? "green.500" : "red.500"}
            rounded="full"
          />
          <Flex w={"100%"} justifyContent={"center"} alignContent={"center"}>
            <Icon color={"green.500"} size={"xl"}>
              {!isBusiness ? (
                <FaWhatsapp />
              ) : (
                <IconBusinessNoChakra size={30} color={"#22c55e"} />
              )}
            </Icon>
          </Flex>
          <Text color={"chatTextColor"}>{formatPhoneNumber(number)}</Text>
          <HStack position={"absolute"} right={4} bottom={4}>
            <Box
              w={"30px"}
              h={"30px"}
              rounded={"full"}
              fontWeight="700"
              bgColor="chatPrimary"
              color="white"
              border={"1px solid transparent"}
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
                cursor: "pointer",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              display={"flex"}
              alignItems={"center"}
              justifyContent={"center"}
              onClick={handleEdit}
            >
              <Icon size={"xs"}>
                <LuBot />
              </Icon>
            </Box>
            {!isBusiness ? (
              <Box
                w={"30px"}
                h={"30px"}
                rounded={"full"}
                fontWeight="700"
                bgColor="chatPrimary"
                color="white"
                border={"1px solid transparent"}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                  cursor: "pointer",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
                display={"flex"}
                alignItems={"center"}
                justifyContent={"center"}
                onClick={handleScan}
              >
                <Icon size={"xs"}>
                  <LuScanQrCode />
                </Icon>
              </Box>
            ) : (
              <Box
                w={"30px"}
                h={"30px"}
                rounded={"full"}
                fontWeight="700"
                bgColor="chatPrimary"
                color="white"
                border={"1px solid transparent"}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                  cursor: "pointer",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
                display={"flex"}
                alignItems={"center"}
                justifyContent={"center"}
                onClick={handleSeeIntegrationMeta}
              >
                <Icon size={"xs"}>
                  <LuGitFork />
                </Icon>
              </Box>

            )}
            <Box
              w={"30px"}
              h={"30px"}
              rounded={"full"}
              fontWeight="700"
              bgColor="chatPrimary"
              color="white"
              border={"1px solid transparent"}
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
                cursor: "pointer",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              display={"flex"}
              alignItems={"center"}
              justifyContent={"center"}
              onClick={() => {
                setOpenDeleteModal(true);
              }}
            >
              <Icon size={"xs"}>
                <LuTrash2 />
              </Icon>
            </Box>
          </HStack>
        </Flex>
      )}
      <BasicModal
        open={openDeleteModal}
        setOpen={setOpenDeleteModal}
        cancelText="Cancelar"
        confirmText="Deletar"
        placement="center"
        title="Deletar Número de Whatsapp"
        handleConfirm={handleDelete}
        children={
          <>
            <Center flex={1}>
              <Text>
                Tem certeza de que deseja apagar este número do WhatsApp? Ao
                realizar esta ação, você não poderá mais enviar ou receber
                mensagens através da nossa integração por esse número.
              </Text>
            </Center>
          </>
        }
      />
      <BasicModal
        open={openScanModal}
        setOpen={setOpenScanModal}
        cancelText="Fechar"
        title="Leia o QRCode"
        placement="center"
        handleConfirm={() => {
          setOpenScanModal(false);
        }}
        children={
          <Flex flex={1} flexDir={"column"} alignItems={"center"}>
            <Box>
              <img src={QRCode?.qrCode} alt="QRCode" />
            </Box>
            <Text fontWeight={"medium"} pt={5} pb={1}>
              Código de Pareamento
            </Text>
            <Text fontSize={"md"} fontWeight={"bold"}>
              {QRCode?.pairingCode}
            </Text>
          </Flex>
        }
      />
      <BasicModal
        open={openWebhookModal}
        setOpen={setOpenWebhookModal}
        cancelText="Fechar"
        title="Dados de Integração"
        placement="center"
        handleConfirm={() => {
          setOpenWebhookModal(false);
        }}
        children={
          <Flex flex={1} flexDir={"column"} alignItems={"center"}>
            <>
              <Text fontWeight={"medium"} pt={5} pb={1}>
                WebHook para uso na plataforma meta
              </Text>
              <Text
                fontSize={"md"}
                fontWeight={"bold"}
                _hover={{
                  cursor: "pointer",
                }}
                onClick={() => {
                  navigator.clipboard.writeText(webhookToken?.webhook ?? "");
                  toaster.create({
                    title: "Webhook copiado",
                    description: "Webhook copiado com sucesso",
                    type: "success",
                  });
                }}
              >
                {webhookToken?.webhook ?? ""}
              </Text>
              <Text fontWeight={"medium"} pt={5} pb={1}>
                Token de Autenticação para uso na plataforma meta
              </Text>
              <Text
                fontSize={"md"}
                fontWeight={"bold"}
                _hover={{
                  cursor: "pointer",
                }}
                wordBreak={"break-all"}
                textAlign={"center"}
                onClick={() => {
                  navigator.clipboard.writeText(webhookToken?.accessToken ?? "");
                  toaster.create({
                    title: "Token copiado",
                    description: "Token copiado com sucesso",
                    type: "success",
                  });
                }}
              >
                {webhookToken?.accessToken ?? ""}
              </Text>
            </>
          </Flex>
        }
      />
      <WppEditModal
        isOpen={isOpen}
        selectedChatBotSecureId={chatBotSecureId}
        setIsOpen={setIsOpen}
        wppIntegrationSecureId={secureId}
      />
    </>
  );
}
