import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Button, Flex, Icon, Separator, Text } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>en } from "react-icons/fa6";
import { Input } from "../global/inputs/input";
import { BACKOFFICEPLANSPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { set } from "date-fns";
import ModalEditPlanBackoffice from "@/app/(dashboard)/backoffice/plans/components/modal-edit-plan-backoffice";

type CardPlanProps = {
  planSecureId: string;
  planName: string;
  planSlug: string;
  planPrice: string;
  planDescription: string;
  planDetails: string;
  planTrialDays: number;
  planAttendantsLimit: number;
  planNumberLimit: number;
  planChatbotsLimit: number;
  planKnowledgeBaseLimit: number;
  planIaMessagesLimit: number;
  isActive: boolean;
};

export default function CardPlan({
  planSecureId,
  planName,
  planSlug,
  planPrice,
  planDescription,
  planDetails,
  planTrialDays,
  planAttendantsLimit,
  planNumberLimit,
  planChatbotsLimit,
  planKnowledgeBaseLimit,
  planIaMessagesLimit,
  isActive,
}: CardPlanProps) {
  const [openEditModal, setOpenEditModal] = useState(false);

  const [newName, setNewName] = useState(planName);

  const updatePlan = useMutation({
    mutationFn: async (data: { secureId: string; [key: string]: any }) => {
      return await api.put(`/backoffice/plans/${data.secureId}`, {
        ...data,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["plans-backoffice"],
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar plano:", error);
    },
  });

  const handleToggleActive = () => {
    updatePlan.mutateAsync({
      secureId: planSecureId,
      isActive: !isActive,
    });
  };

  const handleUpdateName = () => {
    if (newName.length > 0 && newName !== planName) {
      updatePlan.mutateAsync({
        secureId: planSecureId,
        name: newName,
        isActive,
      });
    }
  };

  return (
    <Box
      // w={{base:"full", sm:"49%"}}
      h={"fit-content"}
      bg={"background"}
      borderRadius={"2xl"}
      p={4}
      // _hover={{
      //   transition: "0.3s",
      //   transform: "scale(1.01)",
      //   cursor: "pointer",
      // }}
    >
      <Flex
        flexDirection={"row"}
        justifyContent={"space-between"}
        alignItems={"flex-start"}
        mb={2}
        gap={6}
      >
        <Flex
          justifyContent={"space-between"}
          alignItems={"center"}
          flexWrap={"wrap"}
        >    
          <Text
            mr={4}
            fontSize={{ base: "xl", lg: "2xl" }}
            fontWeight="bold"
            color="chatPrimary"
          >
            {planName}
          </Text>
          <Text
            fontSize={{ base: "xl", lg: "2xl" }}
            fontWeight="bold"
            color="chatPrimary"
          >
            {planPrice}
          </Text>
        </Flex>
        {HasBackofficePermission(BACKOFFICEPLANSPERMISSIONS.EDIT) ? (
          <>
            <Icon
              mt={2}
              color="chatPrimary"
              fontSize="xl"
              _hover={{
                transition: "0.3s",
                color: "white",
                cursor: "pointer",
              }}
              onClick={() => (setOpenEditModal(true))}
            >
              <FaPen />
            </Icon>
          </>
        ) : null}
      </Flex>

      <Text fontSize={{ base: "sm", lg: "md" }} color="gray.400" mb={2}>
        {planDescription}
      </Text>
      <Separator mb={2} />
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Trial Days: {planTrialDays}
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Atendentes: {planAttendantsLimit}
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Números de WhatsApp: {planNumberLimit}
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Chatbots: {planChatbotsLimit}
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Base de Conhecimento: {planKnowledgeBaseLimit}
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} color="#d0d0d0" mb={2}>
        Mensagens IA: {planIaMessagesLimit}
      </Text>
      {HasBackofficePermission(BACKOFFICEPLANSPERMISSIONS.EDIT) ? (
        <Flex justifyContent="flex-end" mt={4}>
          <Button
            size="xs"
            color={"white"}
            bg={isActive ? "backgroundCard" : "chatPrimary"}
            borderRadius={"full"}
            colorScheme={isActive ? "red" : "green"}
            _hover={{
              transition: "0.3s",
              bg: isActive ? "red.500" : "gray.100",
              color: isActive ? "white" : "chatPrimary",
            }}
            onClick={handleToggleActive}
          >
            {isActive ? "Inativar" : "Ativar"}
          </Button>
        </Flex>
      ) : null}
    <ModalEditPlanBackoffice
      openModal={openEditModal}
      setOpenModal={setOpenEditModal}
      planSecureId={planSecureId}
      planName={planName}
      planSlug={planSlug}
      planPrice={planPrice}
      planDescription={planDescription}
      planDetails={planDetails}
      planTrialDays={planTrialDays}
      planAttendantsLimit={planAttendantsLimit}
      planNumberLimit={planNumberLimit}
      planChatbotsLimit={planChatbotsLimit}
      planKnowledgeBaseLimit={planKnowledgeBaseLimit}
      planIaMessagesLimit={planIaMessagesLimit}
      isActive={isActive}
    />
    </Box>
  );
}
