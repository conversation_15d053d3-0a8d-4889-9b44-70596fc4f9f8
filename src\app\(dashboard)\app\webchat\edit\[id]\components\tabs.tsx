"use client";
import { Flex, Tabs, Text } from "@chakra-ui/react";
import {
  LuCodeXml,
  LuFolder,
  <PERSON>ouse,
  <PERSON><PERSON><PERSON>te,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "react-icons/lu";
import GeneralTab from "./general-tab";
import CustomizationTab from "./customization-tab";
import IntegrationTab from "./integration-tab";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import { useEffect, useState } from "react";

type EditTabsProps = {
  data: GetOneChatDtoInput;
};

export default function EditTabs({ data }: EditTabsProps) {
  const [chatData, setChatData] = useState(data);

  useEffect(() => {
    setChatData(data);
  }, [data]);

  return (
    <Tabs.Root
      defaultValue="general"
      activationMode="manual"
      orientation="vertical"
      gap={1}
      flex={1}
      size={"lg"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
        alignItems={"flex-start"}
      >
        <Tabs.Trigger
          value="general"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <LuHouse />
          <Text fontWeight={"normal"}>Geral</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="customization"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <LuPalette />
          <Text fontWeight={"normal"}>Customização</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="integration"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <LuCodeXml />
          <Text fontWeight={"normal"}>Integração</Text>
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="general" width={"100%"}>
        <GeneralTab chatData={chatData} />
      </Tabs.Content>
      <Tabs.Content value="customization" width={"100%"}>
        <CustomizationTab chatData={chatData} />
      </Tabs.Content>
      <Tabs.Content value="integration" width={"100%"}>
        <IntegrationTab secureId={chatData.secureId} />
      </Tabs.Content>
    </Tabs.Root>
  );
}
