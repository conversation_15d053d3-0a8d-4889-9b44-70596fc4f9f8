import {
  Box,
  Flex,
  Icon,
  Text,
} from "@chakra-ui/react";
import { IoPersonOutline } from "react-icons/io5";
import { Switch } from "../ui/switch";
import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { toaster } from "../ui/toaster";
import { useRouter } from "next/navigation";
import { BACKOFFICEUSERPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";

interface CardUserProps {
  userSecureId: string;
  userName: string;
  userIsActive: boolean;
}

export default function CardUser({
  userSecureId,
  userName,
  userIsActive,
}: CardUserProps) {
  const [isActive, setIsActive] = useState<boolean | undefined>();
  const router = useRouter();

  const hasEditPermission = HasBackofficePermission(BACKOFFICEUSERPERMISSIONS.EDIT)

  useEffect(() => {
    if (userIsActive === true) {
      setIsActive(true);
    } else {
      setIsActive(false);
    }
  }, [userIsActive]);

  const handleEdit = () => {
    changeStatusAttendant.mutateAsync();
  };

  const changeStatusAttendant = useMutation({
    mutationFn: async () => {
      await api.put(`/backoffice/users/${userSecureId}`, {
        isActive: isActive,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users-backoffice"] });
      toaster.create({
        type: "success",
        description: `Usuário ${!isActive ? "desativado" : "ativado"} com sucesso`,
        title: `Usuário ${!isActive ? "desativado" : "ativado"}`,
      });
    },
  });

  return (
    <Box position={"relative"}>
      <Flex
        alignItems="center"
        onClick={() => router.push(`/backoffice/users/${userSecureId}`)}
        gap={4}
        p={{ base: 4, md: 6 }}
        borderRadius="2xl"
        flexBasis={{ base: "100%", sm: "45%", md: "40%", lg: "30%" }}
        transition="0.3s"
        _hover={{
          transition: "0.3s",
          transform: "scale(1.01)",
          cursor: "pointer",
        }}
        bg={"background"}
      >
        <Icon color="#A1A1AA" fontSize={{ base: "xl", md: "2xl" }}>
          <IoPersonOutline />
        </Icon>
        <Text
          fontSize={{ base: "md", md: "lg" }}
          fontWeight="bold"
          color="whiteAlpha.900"
        >
          {userName}
        </Text>
        <Box
          position={"absolute"}
          top={6}
          right={6}
          onClick={(event) => event.stopPropagation()}
        >
          {hasEditPermission ? (
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          ): null}
        </Box>
      </Flex>
    </Box>
  );
}
