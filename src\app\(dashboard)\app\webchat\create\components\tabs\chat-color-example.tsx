import { InputMessage } from "@/components/global/chat/input-message";
import { Avatar } from "@/components/ui/avatar";
import { Box, HStack, VStack, Text, IconButton } from "@chakra-ui/react";
import { LuCheckCheck, LuSend } from "react-icons/lu";

type ChatColorExampleProps = {
  backgroundColor: string;
  inputBg: string;
  inputTextColor: string;
  buttonTextColor: string;
  buttonBgColor: string;
  clientMessageBg: string;
  attendantMessageBg: string;
  clientTextColor: string;
  attendantTextColor: string;
};

export default function ChatColorExample({
  attendantMessageBg,
  attendantTextColor,
  backgroundColor,
  buttonBgColor,
  buttonTextColor,
  clientMessageBg,
  clientTextColor,
  inputBg,
  inputTextColor,
}: ChatColorExampleProps) {
  return (
    <VStack
      flex={1}
      h={"100%"}
      w={"100%"}
      bgColor={backgroundColor}
      p={5}
      rounded={"2xl"}
    >
      <Box
        flex={1}
        height={"90%"}
        w={"100%"}
        gap={5}
        flexDir="column"
        alignItems="flex-end"
        overflowY="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            width: "6px",
            marginBottom: "10px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
            "&:hover": {
              background: "#A6A6A6",
            },
          },
        }}
      >
        <VStack flex={1} w="100%" justifyContent="flex-end" pt={2}>
          <HStack w={"100%"} justifyContent={"flex-end"}>
            <HStack
              flexDir={"row-reverse"}
              alignItems={"start"}
              gap={5}
              maxWidth={"70%"}
              mr={2}
              px={3}
            >
              <VStack position={"relative"} alignItems={"flex-end"}>
                <Box bgColor={clientMessageBg} p={2.5} rounded={10}>
                  <Box
                    position="absolute"
                    bottom={0}
                    right="-15px"
                    top="30px"
                    width={0}
                    height={0}
                    zIndex={0}
                    borderLeft="20px solid transparent"
                    borderRight="20px solid transparent"
                    borderTop={`20px solid ${clientMessageBg}`}
                  />
                  <VStack align={"start"} justify={"space-between"}>
                    <Text color={clientTextColor}>
                      Bom dia, estou precisando de uma ajuda
                    </Text>
                    <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                      <Text color={"chatTextColor"} fontSize={"xs"}>
                        12:20
                      </Text>
                      <LuCheckCheck color="#5081ab" />
                    </HStack>
                  </VStack>
                </Box>
              </VStack>
            </HStack>
          </HStack>
          <HStack w={"100%"} justifyContent={"flex-end"}>
            <HStack
              flexDir={"row-reverse"}
              alignItems={"start"}
              gap={5}
              maxWidth={"70%"}
              mr={2}
              px={3}
            >
              <VStack position={"relative"}>
                <Box bgColor={clientMessageBg} p={2.5} rounded={10}>
                  <VStack align={"start"} justify={"space-between"}>
                    <Text color={clientTextColor}>Esta disponível?</Text>
                    <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                      <Text color="chatTextColor" fontSize={"xs"}>
                        12:22
                      </Text>
                      <LuCheckCheck color="#5081ab" />
                    </HStack>
                  </VStack>
                </Box>
              </VStack>
            </HStack>
          </HStack>
          <HStack w={"100%"} justifyContent={"flex-start"}>
            <HStack alignItems={"start"} gap={5} maxWidth={"70%"} px={3}>
              <Avatar
                size={"lg"}
                name={"Jose"}
                bgColor={buttonBgColor}
                src={undefined}
              />
              <VStack position={"relative"}>
                <Box
                  position="absolute"
                  bottom={0}
                  left="-15px"
                  top="30px"
                  width={0}
                  height={0}
                  zIndex={0}
                  borderLeft="20px solid transparent"
                  borderRight="20px solid transparent"
                  borderTop={`20px solid ${attendantMessageBg}`}
                />
                <Box
                  bgColor={attendantMessageBg}
                  p={2.5}
                  rounded={10}
                  zIndex={1}
                >
                  <VStack align={"start"} justify={"space-between"}>
                    <Text color={attendantTextColor}>
                      Olá, Como posso te ajudar hoje?
                    </Text>
                    <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                      <Text color="chatTextColor" fontSize={"xs"}>
                        12:30
                      </Text>
                      <LuCheckCheck color="#5081ab" />
                    </HStack>
                  </VStack>
                </Box>
              </VStack>
            </HStack>
          </HStack>
        </VStack>
      </Box>
      <HStack w="100%">
        <InputMessage
          name="teste"
          bgColor={inputBg}
          color={inputTextColor}
          rounded={20}
          placeholder="Digite a mensagem"
          value={"teste"}
          onChange={(e) => {}}
        />
        <IconButton
          type="submit"
          rounded="full"
          bgColor={buttonBgColor}
          color={buttonTextColor}
          aria-label="Enviar mensagem"
          size="xl"
          borderWidth={2}
          transition="all 0.3s"
          _active={{
            bgColor: "transparent",
            borderColor: buttonBgColor,
            color: buttonBgColor,
            borderWidth: 2,
          }}
        >
          <LuSend />
        </IconButton>
      </HStack>
    </VStack>
  );
}
