import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ck,
	<PERSON>,
	Box,
	<PERSON><PERSON>,
	Spinner,
	AbsoluteCenter,
	Center,
} from "@chakra-ui/react";
import { LuBadgeHelp, Lu<PERSON>he<PERSON> } from "react-icons/lu";
import BasicModal from "@/components/global/modal/basic-modal";
import { useEffect, useState } from "react";
import TipTapEditor from "@/components/tiptap/tiptap-editor";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { KnowledgeBaseDTO } from "@/utils/types/DTO/knowledge-base.dto";
import HeaderTab from "@/components/tabs/header";

type KnowledgeBaseTabProps = {
	chatBotSecureId: string;
};

export default function KnowledgeBaseTab({
	chatBotSecureId,
}: KnowledgeBaseTabProps) {
	const [openHelpModal, setOpenHelpModal] = useState(false);
	const [isSave, setIsSaving] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [knowledgeBaseSecureId, setKnowledgeBaseSecureId] = useState<string | undefined>()
	const [post, setPost] = useState('<p style="text-align: center">Adicione aqui as informações que o chatbot deverá aprender...</p>');

	const defaultContent = '<p style="text-align: center">Adicione aqui as informações que o chatbot deverá aprender...</p>';

  const handleSetPost = async () => {
		setIsLoading(true);
		const { data } = await api.get<KnowledgeBaseDTO>(
			`knowledge-base/${chatBotSecureId}`
		);


		if (data) {
			setPost(data?.content || defaultContent);
			setKnowledgeBaseSecureId(data.secureId);
		} else {
			setPost(defaultContent);
			setKnowledgeBaseSecureId(undefined);
		}
		setIsLoading(false);
	}	

	useEffect(() => {
		handleSetPost();
	}, [chatBotSecureId]);

	
	const onChange = (content: string) => {
		setPost(content);
	};

	const handleSaveKnowledgeBase = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSaving(true);
		try {
			if(!knowledgeBaseSecureId){
				toaster.error({
					title: "Erro ao salvar conteúdo",
					description: "Não foi possível salvar o conteúdo.",
				});
				return;
			}

			await api.post(`knowledge-base/${knowledgeBaseSecureId}`, {
				content: post,
			});
			toaster.success({
				title: "Conteúdo salvo",
				description: "Conteúdo salvo com sucesso.",
			});
		} catch (error) {
			toaster.error({
				title: "Erro ao salvar conteúdo",
				description: "Ocorreu um erro ao salvar o conteúdo.",
			});
		} finally {
			setIsSaving(false);
		}
	}

	return (
		<>
			<Flex
				flex={1}
				h={"100%"}
				bgColor={"chatCardBackground"}
				p={"5"}
				rounded={"2xl"}
				border={"none"}
				direction="column"
				overflow="hidden" // Add this to prevent overflow
				as={'form'}
				onSubmit={handleSaveKnowledgeBase}
				gap={5}
			>
				<HeaderTab
					buttonTitle="Salvar Alterações"
					title="Base de Conhecimento"
					hrefBack="/app/config#chatbots"
					isSubmit={isSave}
					newButton={
							<Button
								w={"100%"}
								borderRadius={"full"}
								size={"md"}
								width={10}
								height={10}
								fontWeight="700"
								bgColor="gray.300"
								color="black"
								onClick={() => setOpenHelpModal(true)}
								transitionDuration={"0.2s"}
								_hover={{
									color: "chatPrimary",
									bgColor: "transparent",
									borderColor: "chatPrimary",
								}}
								_active={{
									transform: "scale(0.95)",
								}}
							>
								<LuBadgeHelp />
							</Button>
					}
				/>
								
				<Box flex={1} overflow="hidden" h={"100%"}>
					{isLoading ? (
						<Flex flex={1}>
							<Spinner color={'chatPrimary'}/>
						</Flex>
					): (
						<TipTapEditor 
							content={post} 
							onChange={onChange}
						/>
					)}
				</Box>
			</Flex>
			
			<BasicModal
				open={openHelpModal}
				size="lg"
				setOpen={setOpenHelpModal}
				cancelText="Confirmar"
				placement="center"
				children={
					<VStack gap={5} m={3}>
						<Text fontSize={"lg"} >
						Para obter melhores respostas do chatbot, organize as informações em tópicos claros, listas ou no formato de perguntas e respostas. Quanto mais estruturado o conteúdo, mais preciso será o entendimento da IA.
						</Text>
					</VStack>
				}
			/>
		</>
	);
}
