import { api } from "@/services/api";
import {
  AttendantInputDTO,
  ListAttendantInputDTO,
} from "@/utils/types/DTO/attendant.dto";
import { useQuery } from "@tanstack/react-query";

async function listAttendant() {
  const { data } = await api.get<ListAttendantInputDTO>("/attendant");
  return data;
}

async function getAttendant(secureId: string) {
  const { data } = await api.get<AttendantInputDTO>(`/attendant/${secureId}`);
  return data;
}

export default function useAttendant() {
  return useQuery({
    queryKey: ["listAttendants"],
    queryFn: async () => await listAttendant(),
  });
}

export function useGetAttendant(secureId: string) {
  return useQuery({
    queryKey: ["getAttendant", secureId],
    queryFn: async () => await getAttendant(secureId),
  });
}
