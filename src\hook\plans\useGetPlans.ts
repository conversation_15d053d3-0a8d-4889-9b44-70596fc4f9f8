import { api } from "@/services/api";
import { GetAllPlansDto } from "@/utils/types/DTO/all-plans.dto";
import { useQuery } from "@tanstack/react-query";

async function getPlans(showOnlyActives: boolean) {
  const { data } = await api.get<GetAllPlansDto>(`/backoffice/plans`, {
    params: {
      isActive: showOnlyActives ? true : undefined,
    },
  });

  return data;
}

export default function useGetPlans(showOnlyActives: boolean) {
  return useQuery({
    queryFn: async () => await getPlans(showOnlyActives),
    queryKey: ["plans-backoffice", showOnlyActives],
    refetchInterval: false,
  });
}
