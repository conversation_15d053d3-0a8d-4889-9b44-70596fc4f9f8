"use client";
import { Avatar } from "@/components/ui/avatar";
import {
  DrawerBackdrop,
  DrawerBody,
  DrawerCloseTrigger,
  DrawerContent,
  DrawerRoot,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Box, HStack, IconButton } from "@chakra-ui/react";
import { RxHamburgerMenu } from "react-icons/rx";
import Cookies from "js-cookie";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { MenuContent, MenuItem, MenuRoot, MenuTrigger } from "@/components/ui/menu";
import HasPermission from "@/utils/funcs/has-permission";
import { APPPERMISSIONS } from "@/utils/types/permissions/all-attendant-permissions";

type DrawerMenuProps = {
  children: React.ReactNode;
  name: string;
  url: string;
};

export default function DrawerMenu({ children, name, url }: DrawerMenuProps) {
  const router = useRouter();

  const handleLogout = () => {
    Cookies.remove("__PlyrChat_Token");
    router.push("/");
  };
  return (
    <Box
      hideFrom={"md"}
      w={"100%"}
      position={"absolute"}
      top={0}
      mt={5}
      // bgColor={"red.200"}
    >
      <HStack w={"100%"} justifyContent={"space-evenly"}>
        <DrawerRoot placement={"start"}>
          <DrawerBackdrop />
          <DrawerTrigger asChild>
            <IconButton
              aria-label="Go Back"
              rounded="full"
              bgColor={"chatPrimary"}
              color={"white"}
              size={"lg"}
              borderWidth={2}
              _active={{
                bgColor: "transparent",
                borderColor: "chatPrimary",
                color: "chatPrimary",
                borderWidth: 2,
              }}
            >
              <RxHamburgerMenu />
            </IconButton>
          </DrawerTrigger>
          <DrawerContent>
            <DrawerBody>{children}</DrawerBody>
            <DrawerCloseTrigger />
          </DrawerContent>
        </DrawerRoot>

        <Box
          w="50%"
          my={2}
          h={"9"}
          bgImage="url(/logo.png)"
          bgPos="center"
          bgRepeat="no-repeat"
          bgSize="contain"
        />
        <MenuRoot>
          <MenuTrigger
            _hover={{ cursor: "pointer" }}
            outline={"none"}
          >
            <Avatar
              bgColor={'#5e5e5e'}
              name={name}
              size={"lg"}
              borderWidth={2}
              borderColor={"transparent"}
              _active={{
                bgColor: "transparent",
                borderColor: "chatPrimary",
                color: "chatPrimary",
                borderWidth: 2,
              }}
            />
          </MenuTrigger>
          <MenuContent
            outline={"none"}
            shadow={"md"}
          >
            <MenuItem
              value="my-account"
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={() => router.push("/backoffice/profile")}
            >
              Minha Conta
            </MenuItem>
            {HasPermission(APPPERMISSIONS.VIEW) && (
              <MenuItem
                value="backoffice"
                _hover={{
                  cursor: "pointer",
                  color: "chatPrimary",
                }}
                onClick={() => router.push("/app/dashboard")}
              >
                App
              </MenuItem>
            )}
            <MenuItem
              value="logout"
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={handleLogout}
            >
              Deslogar
            </MenuItem>
          </MenuContent>
        </MenuRoot>
      </HStack>
    </Box>
  );
}
