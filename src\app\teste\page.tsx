"use client";
import { useEffect } from "react";
import Head from "next/head";
import { Flex } from "@chakra-ui/react";

export default function ChatWidget() {
  useEffect(() => {
    // Definir o objeto de configuração antes de carregar o script do widget
    (window as any).PlyrChatWidgetConfig = {
      websiteUrl: "http://***********:3000",
			btnColor: "#FD2264",
			chatId: "a27389eb-5aa9-47d2-95dc-94173f8879cf",
			iconUrl: "https://devs-concpts.nyc3.digitaloceanspaces.com/plyrchat/1743166665092-chat.png",
    };

    // Criar e carregar o script do widget
    const script = document.createElement("script");
    script.src = "http://***********:3000/chat-widget.js";
    script.async = true;
    script.onload = () => console.log("Widget carregado!");
    document.body.appendChild(script);

    return () => {
      // Remover o script ao desmontar o componente (opcional)
      document.body.removeChild(script);
    };
  }, []);

  return (
    <>
      <Head>
        <title>Chat Widget</title>
      </Head>
      <Flex
        flex="1"
        background="linear-gradient(243deg, #FF004C 0%, #FD2264 47.5%, #97143C 100%)"
        h="100vh"
        display="block"
      >
        <h1>Bem-vindo ao site PlyrChat</h1>
        <p>Este site está integrado com chatbot.</p>
        {/* Contêiner onde o chatbot será renderizado */}
        <div id="chatbot-container"></div>
      </Flex>
    </>
  );
}
