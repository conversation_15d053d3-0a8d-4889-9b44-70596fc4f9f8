import { FileUpload, HStack, IconButton } from "@chakra-ui/react";
import { InputMessage } from "./input-message";
import { Tb<PERSON>aperclip, TbSend } from "react-icons/tb";
import { FormEvent, useState } from "react";
import { toaster } from "@/components/ui/toaster";
import { WebSocketService } from "@/services/websocket/websocket";
import { FileUploadList } from "./file-button";

interface ChatFooterWebSocketProps {
  onSendMessage: (content: string, file?: {
    file: File;
    type: 'file' | 'image' | 'audio';
  }) => Promise<void>;
  inputMessageColor: string | null;
  inputBgColor: string | null;
  buttonBgColor: string | null;
}

export default function ChatFooterWebSocket({
  onSendMessage,
  buttonBgColor,
  inputBgColor,
  inputMessageColor,
}: ChatFooterWebSocketProps) {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [file, setFile] = useState<File | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!message.trim() && !file) {
      toaster.create({
        title: "Mensagem vazia",
        description: "Digite uma mensagem para enviar",
        type: "info",
      });
      return;
    }

    setIsSending(true);
    try {
      if (file) {
        let fileType: 'file' | 'image' | 'audio' = "file";
        if (file.type.startsWith("image/")) {
          fileType = "image";
        } else if (file.type.startsWith("audio/")) {
          fileType = "audio";
        }
        await onSendMessage(message, {
          file,
          type: fileType,
        });
        setFile(null);
        setMessage("");
        return;
      }
      await onSendMessage(message);
      setMessage("");
    } catch (error) {
      toaster.error({
        title: "Erro ao enviar mensagem",
        description: "Tente novamente mais tarde",
      });
    } finally {
      setFile(null);
      setMessage("");
      setIsSending(false);
    }
  };

  return (
    <HStack w="100%" as="form" onSubmit={handleSubmit} position={"relative"} zIndex={1000}>
      <FileUpload.Root
        maxFileSize={1024 * 1024 * 30} // 30mb
        maxFiles={1}
        accept={["image/*", "application/pdf", "audio/mpeg"]}
        w={"auto"}
        onFileAccept={(e) => setFile(e.files[0])}
        onFileReject={(e) => {
          if (e.files.length === 0) return;
          toaster.create({
            title: "Arquivo inválido",
            description: "Selecione um arquivo de imagem, PDF ou audio com até 3MB",
            type: "info",
          });
        }}
      >
        <FileUpload.HiddenInput />
        <FileUpload.Trigger asChild>
          <IconButton
            rounded="full"
            bgColor={buttonBgColor || "chatPrimary"}
            color="white"
            aria-label="Anexar arquivo"
            size="md"
            borderWidth={2}
            transition="all 0.3s"
            // disabled={isSending}
            _active={{
              bgColor: "transparent",
              borderColor: "chatPrimary",
              color: "chatPrimary",
              borderWidth: 2,
            }}
          >
            <TbPaperclip />
          </IconButton>
        </FileUpload.Trigger>
        <FileUploadList handleClearFile={() => setFile(null)} file={file} />
      </FileUpload.Root>
      <InputMessage
        name="message"
        rounded={20}
        color={inputMessageColor || "chatPrimary"}
        bgColor={inputBgColor || "chatBackground"}
        placeholder="Digite a mensagem"
        value={message}
        onChange={(e) => {
          setMessage(e.target.value);
        }}
        disabled={isSending || file?.type === "audio/mpeg"}
        _focus={{
          borderColor: inputBgColor || "chatTextColor",
        }}
        _hover={{
          borderColor: inputBgColor || "chatTextColor",
        }}
        onEnterPress={() => {
          if (message.trim() || file) {
            handleSubmit({ preventDefault: () => {} } as any);
          }
        }}
      />
      <IconButton
        type="submit"
        rounded="full"
        bgColor={buttonBgColor || "chatPrimary"}
        color="white"
        aria-label="Enviar mensagem"
        size="xl"
        borderWidth={2}
        transition="all 0.3s"
        disabled={isSending}
        _active={{
          bgColor: "transparent",
          borderColor: "chatPrimary",
          color: "chatPrimary",
          borderWidth: 2,
        }}
      >
        <TbSend />
      </IconButton>
    </HStack>
  );
}
