import { TransactionStatus } from "../transactions/transaction-status";
import { GetAccountSubscriptionDto } from "./accounts-subscriptions.dto";
import { GetAccountDto } from "./accounts.dto";
import { MetaDTO } from "./meta.dto";

type SimplifiedAccountSubscriptionDto = Omit<GetAccountSubscriptionDto, 'plan' | 'account'>;

export type GetAllAccountsTransactionsDto = {
  meta: MetaDTO;
  data: GetAccountTransactionDto[];
}

export type GetAccountTransactionDto = {
  secureId: string;
  amount: number;
  status: TransactionStatus;
  account: GetAccountDto;
  subscription: SimplifiedAccountSubscriptionDto;
  payedAt: string;
  createdAt: string;
  updatedAt: string;
}