import { PermissionsCheckbox } from "@/components/global/checkbox/permissions-checkbox";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Flex, HStack, Tabs } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { useController, useForm } from "react-hook-form";
import { LuUser, LuUserCog } from "react-icons/lu";
import * as yup from "yup";
import BasicBackofficeModal from "@/components/global/modal/basic-backoffice-modal";
import useBackofficePermissions from "@/hook/permissions/useBackofficePermissions";
import useAttendantPermissions from "@/hook/permissions/useAttendantPermissions";
import Account from "../../page";
import useGetPlans from "@/hook/plans/useGetPlans";
import { InputSelect } from "@/components/global/inputs/input-select";

type ModalCreateAccountUserBackofficeProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
  accountSecureId: string;
};

const createAccountUserSchema = yup.object().shape({
  planSecureId: yup.array(yup.string()).optional(),
});

type CreateAccountUserFormData = yup.InferType<typeof createAccountUserSchema>;

export default function ModalAccountCreateSubscriptionBackoffice({
  openModal,
  setOpenModal,
  accountSecureId,
}: ModalCreateAccountUserBackofficeProps) {
  const { data: plans } = useGetPlans(true);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateAccountUserFormData>({
    resolver: yupResolver(createAccountUserSchema),
  });

  const createUser = useMutation({
    mutationFn: async (data: CreateAccountUserFormData) => {
      await api.post("accounts-subscriptions", {
        planSecureId: data.planSecureId,
        accountSecureId: accountSecureId,
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Usuário Cadastrado com Sucesso.",
        title: "Usuário Cadastrado!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["accounts-subscriptions"],
      });
      setOpenModal(false);
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: CreateAccountUserFormData) => {
    try {
      createUser.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <BasicBackofficeModal
      open={openModal}
      size="xl"
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Criar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Criar assinatura"
      placement="top"
      children={
        <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
          <InputSelect
            {...register("planSecureId")}
            control={control}
            label="Selecione o Plano"
            placeholder="Selecione o Plano"
            rounded={"2xl"}
            error={errors.planSecureId as any}
            itensList={
              plans?.data.map((plan) => ({
              label: plan.name,
              value: plan.secureId,
            })) || []
          }
          />
        </Flex>
      }
    />
  );
}
