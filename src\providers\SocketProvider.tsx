"use client";
import { WebSocketService } from "@/services/websocket/websocket";
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

type SocketContextData = {
  wsServiceRef: WebSocketService;
  isConnected: boolean;
};

const SocketContext = createContext({} as SocketContextData);

type SocketProviderProps = {
  children: React.ReactNode;
  // userId: string;
};

export const SocketProvider = ({ children }: SocketProviderProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const wsServiceRef = useRef(new WebSocketService());

  // Efeito para conectar o websocket
  useEffect(() => {
    const connectWebSocket = async () => {
      await wsServiceRef.current.connect();
      setIsConnected(wsServiceRef.current.isConnected());
    };

    connectWebSocket();
  }, [wsServiceRef]);

  // Efeito para desconectar o websocket apenas no unmount
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (wsServiceRef.current && wsServiceRef.current.isConnected()) {
        wsServiceRef.current.removeAllListeners();
        wsServiceRef.current.disconnect();
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      if (wsServiceRef.current && wsServiceRef.current.isConnected()) {
        wsServiceRef.current.removeAllListeners();
        wsServiceRef.current.disconnect();
      }
    };
  }, [wsServiceRef]);

  return (
    <SocketContext.Provider
      value={{ wsServiceRef: wsServiceRef.current, isConnected }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export function useSocket() {
  return useContext(SocketContext);
}
