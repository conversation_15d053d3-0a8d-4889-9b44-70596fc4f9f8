export async function ValidateUserPermissions(
  permissions: string[],
  userPermissions: string[]
) {
  const hasPermission = permissions.some((permission) =>
    userPermissions.includes(permission)
  );

  return hasPermission;
}

export async function ValidateUserRoles(roles: string[], userRoles: string) {
  if (userRoles.includes("MASTER")) {
    return true;
  }
  const hasRole = roles.some((role) => userRoles.includes(role));

  return hasRole;
}
