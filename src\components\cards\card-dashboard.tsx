import {
  Box,
  HStack,
  Icon,
  Text,
  VStack,
} from "@chakra-ui/react";
import { ReactNode } from "react";

type CardDashboardProps = {
  title: string;
  icon: ReactNode;
  value: number | string;
  percentage: number;
  percentagePositive: boolean;
};

export default function CardDashboard({
  icon,
  percentage,
  percentagePositive,
  title,
  value,
}: CardDashboardProps) {
  return (
    <Box
      bgColor="background"
      p={5}
      h="100%"
      minH="150px"
      w="100%"
      borderRadius="2xl"
      transition="transform 0.3s, box-shadow 0.3s"
      shadowColor= "white"
      _hover={{
        transform: "translateY(-5px)",
        boxShadow: "0 4px 10px rgba(255, 255, 255, 0.2)"
      }}
    >
      <VStack h="100%" justifyContent="space-between" gap={3} w="100%">
        <HStack w="100%" align="center" justify="flex-start">
          <Icon color="#A1A1AA" fontSize="30px">
            {icon}
          </Icon>
          <Text fontSize={{base: "md", "2xl": "lg"}} color="white" fontWeight="medium">
            {title}
          </Text>
        </HStack>

        <VStack w="100%" align="flex-end">
          {typeof value === "string" ? (
            <Text
              fontSize={{base: "lg", "2xl": "xl"}}
              fontWeight="medium"
              color="white"
            >
              {value}
            </Text>
          ) : (
            <Text
              fontSize={{base: "lg", "2xl": "xl"}}
              fontWeight="medium"
              color="white"
            >
              {value}
            </Text>
          )}
        </VStack>
      </VStack>
    </Box>
  );
}
