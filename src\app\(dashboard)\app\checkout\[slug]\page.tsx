"use client";
import * as yup from "yup";
import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import CardPlanApp from "../../components/cards/card-plan";
import { Input } from "@/components/global/inputs/input";
import EfiPay from "payment-token-efi";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toaster } from "@/components/ui/toaster";
import { IoArrowBackOutline } from "react-icons/io5";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import useGetPlanBySlug from "@/hook/plans/useGetPlanBySlug";
import { AxiosResponse } from "axios";

const CheckoutSchema = yup.object().shape({
  cardNumber: yup.string().required("Número do cartão é obrigatório"),
  holderName: yup.string().required("Nome é obrigatório"),
  holderCpf: yup.string().required("Nome é obrigatório"),
  cvv: yup.string().required("CVV é obrigatório"),
  expirationDate: yup.string().required("Data de validade é obrigatória"),
});

type CheckoutFormData = yup.InferType<typeof CheckoutSchema>;

export default function CheckoutPage() {
  const router = useRouter();
  const params = useParams()
  const slug = params.slug;

  const { data: plan } = useGetPlanBySlug(slug as string);

  const {
    register,
    handleSubmit,
    setValue,
    formState,
    formState: { errors },
  } = useForm<CheckoutFormData>({
    resolver: yupResolver(CheckoutSchema),
  });

  const generatePaymentToken = async (
      data: CheckoutFormData
    ): Promise<{ payment_token: string; card_mask: string } | undefined> => {
      try {
        const brand = await EfiPay.CreditCard.setCardNumber(
          data.cardNumber
        ).verifyCardBrand();
        const [expirationMonth, expirationYear] = data.expirationDate.split("/");

        const result = await EfiPay.CreditCard.setAccount(
          process.env.NEXT_PUBLIC_PAYEE_CODE_EFI_API
            ? process.env.NEXT_PUBLIC_PAYEE_CODE_EFI_API
            : ""
        )
        .setEnvironment(process.env.NEXT_PUBLIC_EFI_ENVIRONMENT === "production" ? "production" : "sandbox")
        .setCreditCardData({
          brand: brand,
          number: data.cardNumber,
          cvv: data.cvv,
          expirationMonth: expirationMonth,
          expirationYear: expirationYear,
          holderName: data.holderName,
          holderDocument: data.holderCpf,
          reuse: true,
        })
        .getPaymentToken();
  
        if ("payment_token" in result && "card_mask" in result) {
          return {
            payment_token: result.payment_token,
            card_mask: result.card_mask,
          };
        }
      } catch (error: any) {
        toaster.error({
          title: "Erro",
          description: error.error_description,
        });
      }
    };

  const checkout = useMutation({
    mutationFn: async (data: { paymentToken: string; holderName: string; }) => {
      if (!plan) {
        throw new Error("Plano não encontrado");
      }

      const response: AxiosResponse = await api.post("/checkout", {
        paymentToken: data.paymentToken,
        holderName: data.holderName,
        planSecureId: plan?.secureId,
      });

      return response.data;
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Assinatura criada com sucesso!",
      });

      router.push('/app/profile#subscription')
    },
    onError: (error) => {
      console.error("Erro no checkout:", error);
    },
  });

  if (!slug || !plan) return null;

  const handleCheckout = async (formData: CheckoutFormData) => {
    try {
      const tokenResult = await generatePaymentToken(formData);

      if (!tokenResult || !tokenResult.payment_token) {
        return;
      }

      await checkout.mutateAsync({
        paymentToken: tokenResult.payment_token,
        holderName: formData.holderName,
      });
    } catch (error) {
      console.error("Erro no checkout:", error);
    }
  };

  return (
    <Flex
      flex={1}
      flexDirection={"column"}
      rounded={"2xl"}
      justifyContent={"center"}
      alignItems={"center"}
    >
      <HStack
        as={"form"}
        w={"fit-content"}
        h={"full"}
        p={4}
        alignItems={"start"}
        justifyContent={"center"}
        onSubmit={handleSubmit(handleCheckout)}
        gap={4}
        borderRadius={"2xl"}
        bgColor={"chatCardBackground"}
      >
        <VStack h={"full"} rounded={"2xl"} p={4}>
          <VStack w={"full"} h={"full"}>
            <Text color={"chatPrimary"} fontSize={"xl"} fontWeight={"bold"} mb={4}>Dados do cartão</Text>
            <VStack h={"full"}>
              <Input
                labelColor={"chatPrimary"}
                label="Nome do Titular"
                height="80px"
                borderRadius={20}
                {...register("holderName")}
              />
              <InputMaskIcon
                mask={"999.999.999-99"}
                labelColor={"chatPrimary"}
                label="CPF do Titular"
                fieldHeight="80px"
                borderRadius={20}
                {...register("holderCpf")}
              />
              <InputMaskIcon
                mask={"9999 9999 9999 9999"}
                labelColor={"chatPrimary"}
                label="Número do Cartão"
                fieldHeight="80px"
                borderRadius={20}
                {...register("cardNumber")}
              />
              <HStack w={"full"}>
                <Input
                  labelColor={"chatPrimary"}
                  label="CVV"
                  height="80px"
                  maxLength={3}
                  borderRadius={20}
                  {...register("cvv")}
                />
                <InputMaskIcon
                  mask={"99/9999"}
                  labelColor={"chatPrimary"}
                  label="Data de Validade"
                  fieldHeight="80px"
                  borderRadius={20}
                  {...register("expirationDate")}
                />
              </HStack>
            </VStack>
          </VStack>
          <HStack w={"full"} justifyContent={"space-between"}>
            <Button
              type="button"
              borderRadius={20}
              size={"md"}
              fontWeight="700"
              bgColor="transparent"
              color="text"
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              onClick={() => router.push('/app/choose-plans')}
            >
              <IoArrowBackOutline />
              Voltar
            </Button>
            <Button
              type="submit"
              borderRadius={20}
              size={"md"}
              fontWeight="700"
              bgColor="chatPrimary"
              color="white"
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
              loading={formState.isSubmitting}
            >
              Concluir pagamento
            </Button>
          </HStack>
        </VStack>
        {plan && (
          <CardPlanApp
            key={plan.secureId}
            name={plan.name}
            // w="600px"
            slug={plan.slug}
            description={plan.description}
            price={plan.price}
            details={plan.details}
            showButton={false}
          />
        )}
      </HStack>
    </Flex>
  )
}
