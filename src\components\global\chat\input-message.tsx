import {
  Textarea,
  TextareaProps,
} from "@chakra-ui/react";
import React, { forwardRef, ForwardRefRenderFunction, KeyboardEvent } from "react";
import { FieldError } from "react-hook-form";

interface InputMessageProps extends TextareaProps {
  name: string;
  label?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  onEnterPress?: () => void;
}

const InputBase: ForwardRefRenderFunction<HTMLTextAreaElement, InputMessageProps> = (
  { name, label, error, startElement, onEnterPress, onKeyDown, ...rest },
  ref
) => {
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
  
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (onEnterPress) {
        onEnterPress();
      }
    }

    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  return (
    <Textarea
      id={name}
      name={name}
      ref={ref}
      bg="white"
      color="chatPrimary"
      borderColor="#D6D6D6"
      borderWidth={2}
      _hover={{
        borderColor: "chatPrimary",
      }}
      _placeholder={{ color: "#B1A0A5" }}
      _focus={{
        borderColor: "chatPrimary",
      }}
      size="md"
      rows={1}
      minH="45px"
      maxH="120px"
      resize="none"
      overflow="auto"
      onKeyDown={handleKeyDown}
      paddingY={2}
      css={{
        "&::-webkit-scrollbar": {
          width: "6px",
        },
        "&::-webkit-scrollbar-track": {
          width: "6px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "#D6D6D6",
          borderRadius: "24px",
          "&:hover": {
            background: "#A6A6A6",
          },
        },
      }}
      {...rest}
    />
  );
};

export const InputMessage = forwardRef(InputBase);
