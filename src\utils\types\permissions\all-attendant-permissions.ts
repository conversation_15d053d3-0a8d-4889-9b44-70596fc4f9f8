export enum APPPERMISSIONS {
  VIEW = "app_view",
}

export enum CHATPERMISSIONS {
  VIEW = "attendant_chat_view",
  EDIT = "attendant_chat_edit",
  DELETE = "attendant_chat_create",
}

export enum CONTACTPERMISSIONS {
  VIEW = "attendant_contact_view",
  CREATE = "attendant_contact_create",
  EDIT = "attendant_contact_edit",
  DELETE = "attendant_contact_delete",
}

export enum DASHBOARDPERMISSIONS {
  VIEW = "attendant_dashboard_view",
}

export enum REPORTPERMISSIONS {
  VIEW = "attendant_report_view",
}

export enum CONFIGPERMISSIONS {
  VIEW = "attendant_config_view",
}

export enum WHATSAPPERMISSIONS {
  VIEW = "attendant_whatsapp_view",
  CREATE = "attendant_whatsapp_create",
  EDIT = "attendant_whatsapp_edit",
  DELETE = "attendant_whatsapp_delete",
}

export enum WEBCHATPERMISSIONS {
  VIEW = "attendant_webchat_view",
  CREATE = "attendant_webchat_create",
  EDIT = "attendant_webchat_edit",
  DELETE = "attendant_webchat_delete",
}

export enum CHATBOTPERMISSIONS {
  VIEW = "attendant_chatbot_view",
  CREATE = "attendant_chatbot_create",
  EDIT = "attendant_chatbot_edit",
  DELETE = "attendant_chatbot_delete",
}

export enum KNOWLEDGEBASEPERMISSIONS {
  CREATE = "attendant_knowledge_base_create",
  DELETE = "attendant_knowledge_base_delete",
}

export enum ATTENDANTPERMISSIONS {
  VIEW = "attendant_attendant_view",
  CREATE = "attendant_attendant_create",
  EDIT = "attendant_attendant_edit",
  DELETE = "attendant_attendant_delete",
}
