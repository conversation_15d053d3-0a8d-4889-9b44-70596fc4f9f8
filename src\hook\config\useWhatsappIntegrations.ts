import { api } from "@/services/api";
import { GetListWhatsappIntegrationDto } from "@/utils/types/DTO/config.dto";
import { useQuery } from "@tanstack/react-query";

async function listWhatsappIntegrations(
  page: number = 1,
  limit: number = 20,
  onlyType?: 'messager' | 'business'
) {
  const { data } = await api.get<GetListWhatsappIntegrationDto>(
    `/whatsapp-integration?page=${page}&limit=${limit}&isDeleted=false${
      onlyType ? `&onlyType=${onlyType}` : ""
    }`
  );
  return data;
}

export default function useWhatsappIntegrations({
  page,
  onlyType,
  limit,
}: {
  page?: number;
  limit?: number;
  onlyType?: 'messager' | 'business';
}) {
  return useQuery({
    queryKey: ["whatsapp-integrations"],
    queryFn: async () =>
      await listWhatsappIntegrations(page, limit, onlyType),
  });
}
