import ModalAccountEditUserBackoffice from "@/app/(dashboard)/backoffice/accounts/[id]/components/modal/modal-edit-user-account";
import { SubscriptionStatus } from "@/utils/types/subscription/subscription-status";
import { TransactionStatus } from "@/utils/types/transactions/transaction-status";
import { Badge, Box, HStack, Icon, Separator, Text, VStack } from "@chakra-ui/react";
import { useState } from "react";
import { FaPen } from "react-icons/fa6";

type CardAccountUserPros = {
  name: string;
  email: string;
  cpf: string;
  cellPhone: string | null;
  isOwner: boolean;
  accountSecureId: string;
  userSecureId: string;
  hasAllPermissions: boolean;
  permissions: string[];
}

export default function CardAccountUser({
  name,
  email,
  cpf,
  cellPhone,
  isOwner,
  accountSecureId,
  userSecureId,
  hasAllPermissions,
  permissions,
}: CardAccountUserPros) {
  const [openEditModal, setOpenEditModal] = useState(false);

  return (
    <Box
      p={4}
      borderRadius={20}
      bg="background"
      fontSize={"md"}
    >
      <VStack align="start" gap={2}>
        <HStack justifyContent={"space-between"} alignItems={"center"} w={"full"}>
          <Text fontSize="lg">
            {isOwner ? "Dono: " : "Usuário: "} <strong>{name}</strong>
          </Text>
          <Icon
            color="chatPrimary"
            fontSize="xl"
            _hover={{
              transition: "0.3s",
              color: "white",
              cursor: "pointer",
            }}
            onClick={() => (setOpenEditModal(true))}
          >
            <FaPen />
          </Icon>
        </HStack>
        <Separator/>
        <Text>
          Email:{" "}
          <strong>{email}</strong>
        </Text>
        <Text>
          CPF:{" "}
          <strong>{cpf}</strong>
        </Text>
        <Text>
          Telefone:{" "}
          <strong>{cellPhone ? cellPhone : "Não informado"}</strong>
        </Text>
      </VStack>
      <ModalAccountEditUserBackoffice
        openModal={openEditModal}
        setOpenModal={setOpenEditModal}
        accountSecureId={accountSecureId}
        userSecureId={userSecureId}
        userName={name}
        userEmail={email}
        userCpf={cpf}
        userCellPhone={cellPhone}
        userIsOwner={isOwner}
        userHasAllPermissions={hasAllPermissions}
        userPermissions={permissions}
      />
    </Box>
  )
}