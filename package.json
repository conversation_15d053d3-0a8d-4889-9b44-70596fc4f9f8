{"name": "plyrflow-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@chakra-ui/react": "^3.2.3", "@emotion/react": "^11.13.3", "@hookform/resolvers": "^3.9.1", "@tanstack/react-query": "^5.61.5", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "15.5.13", "@xyflow/react": "^12.3.6", "ai": "^4.0.4", "axios": "^1.7.8", "date-fns": "^4.1.0", "framer-motion": "^12.10.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.487.0", "next": "15.0.3", "next-themes": "^0.4.3", "payment-token-efi": "^3.1.0", "react": "19.0.0-rc-66855b96-20241106", "react-datepicker": "^7.5.0", "react-dom": "19.0.0-rc-66855b96-20241106", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.3", "react-quill-new": "^3.3.3", "react-responsive": "^10.0.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "use-mask-input": "^3.4.2", "uuid": "^11.0.3", "yup": "^1.4.0"}, "devDependencies": {"@chakra-ui/cli": "^3.2.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "typescript": "^5"}}