import { api } from "@/services/api";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import { useQuery } from "@tanstack/react-query";

async function getChat(chatSecureId: string) {
  const { data } = await api.get<GetOneChatDtoInput>(
    `public/chats/${chatSecureId}`
  );

  return data;
}

export default function useChat(chatSecureId: string) {
  return useQuery({
    queryKey: ["get-webchat-public"],
    queryFn: async () => await getChat(chatSecureId),
  });
}
