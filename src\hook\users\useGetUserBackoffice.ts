import { api } from "@/services/api";
import { GetUserBackofficeDto, GetUsersBackofficeDto } from "@/utils/types/DTO/users.dto";
import { useQuery } from "@tanstack/react-query";

async function getUsersBackoffice(page: number) {
  const limit = 7;
  const { data } = await api.get<GetUsersBackofficeDto>(
    `/backoffice/users?page=${page}&limit=${limit}`
  );

  return data;
}

export function useGetUsersBackoffice(page: number) {
  return useQuery({
    queryFn: async () => await getUsersBackoffice(page),
    queryKey: ["users-backoffice", page],
    refetchInterval: false,
  });
}

async function getUserBackoffice(secureId: string) {
  const { data } = await api.get<GetUserBackofficeDto>(`/backoffice/users/${secureId}`);

  return data;
}

export function useGetUserBackoffice(secureId: string) {
  return useQuery({
    queryFn: async () => await getUserBackoffice(secureId),
    queryKey: ["users-backoffice", secureId],
    refetchInterval: false,
  });
}

