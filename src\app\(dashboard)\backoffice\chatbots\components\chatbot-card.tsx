import {
  <PERSON>,
  Grid,
  <PERSON>ridI<PERSON>,
  Text,
  VStack,
  H<PERSON><PERSON>ck,
  Badge,
  useBreakpointValue
} from "@chakra-ui/react";
import { BackofficeChatbotDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuBot, LuBrain, LuBrainCircuit } from "react-icons/lu";
import { useRouter } from "next/navigation";

type ChatbotCardProps = {
  chatbot: BackofficeChatbotDto;
};

export default function ChatbotCard({ chatbot }: ChatbotCardProps) {
  const isMobile = useBreakpointValue({ base: true, sm: false });
  const router = useRouter();

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const handleCardClick = () => {
    router.push(`/backoffice/chatbots/${chatbot.secureId}`);
  };

  if (isMobile) {
    return (
      <Box
        bg="background"
        color="white"
        borderRadius="xl"
        p={4}
        _hover={{
          bg: "gray.800",
        }}
        _active={{
          bg: "gray.800",
        }}
        transition="all 0.2s"
        cursor="pointer"
        onClick={handleCardClick}
      >
        <VStack align="stretch" gap={3}>
          <HStack justify="space-between" align="center">
            <HStack>
              {chatbot.isAI ? (
                <LuBrain size={20} color={chatbot.isActive ? "#fd2264" : "#bbb4b6"} />
              ) : (
                <LuBot size={20} color={chatbot.isActive ? "#fd2264" : "#bbb4b6"} />
              )}
              <Text fontWeight="bold" fontSize="md">
                {chatbot.name}
              </Text>
            </HStack>
            <Badge
              colorScheme={chatbot.isActive ? "green" : "red"}
              variant="subtle"
              fontSize="xs"
            >
              {chatbot.isActive ? "Ativo" : "Inativo"}
            </Badge>
          </HStack>

          <Box>
            <Text fontSize="sm" color="gray.500" fontWeight="medium">
              Conta:
            </Text>
            <Text fontSize="sm">
              {chatbot.account.companyName}
            </Text>
          </Box>

          <Grid templateColumns="repeat(2, 1fr)" gap={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Tokens Entrada
              </Text>
              <Text fontSize="sm" fontWeight="bold" color="blue.600">
                {formatNumber(chatbot.statistics.tokensInput)}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Tokens Saída
              </Text>
              <Text fontSize="sm" fontWeight="bold" color="purple.600">
                {formatNumber(chatbot.statistics.tokensOutput)}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Mensagens
              </Text>
              <Text fontSize="sm" fontWeight="bold" color="green.600">
                {formatNumber(chatbot.statistics.messagesSent)}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Sessões
              </Text>
              <Text fontSize="sm" fontWeight="bold" color="orange.600">
                {formatNumber(chatbot.statistics.sessionsResponded)}
              </Text>
            </Box>
          </Grid>
        </VStack>
      </Box>
    );
  }

  return (
    <Grid
      templateColumns="repeat(6, 1fr)"
      gap={4}
      width="100%"
      px={5}
      py={4}
      bg="background"
      borderRadius="xl"
      alignItems="center"
      cursor="pointer"
      onClick={handleCardClick}
      _hover={{ bg: "gray.800" }}
      transition="all 0.2s"
    >
      <GridItem>
        <HStack>
          {chatbot.isAI ? (
            <LuBrain size={30} color={chatbot.isActive ? "#fd2264" : "#bbb4b6"} />
          ) : (
            <LuBot size={30} color={chatbot.isActive ? "#fd2264" : "#bbb4b6"} />
          )}
          <VStack align="start" gap={0}>
            <Text 
              fontWeight="bold" 
              fontSize="sm" 
              color="white"
              textAlign="center"
            >
              {chatbot.name}
            </Text>
            {/* <Badge
              color={chatbot.isActive ? "blue.300" : "red.300"}
              variant="subtle"
              fontSize="xs"
            >
              {chatbot.isActive ? "Ativo" : "Inativo"}
            </Badge> */}
          </VStack>
        </HStack>
      </GridItem>

      <GridItem>
        <Text 
          fontSize="sm" 
          color="white" 
          textAlign="center"
          fontWeight="medium"
        >
          {chatbot.account.companyName}
        </Text>
      </GridItem>

      <GridItem>
        <Text 
          fontSize="sm" 
          fontWeight="bold" 
          color="blue.600" 
          textAlign="center"
        >
          {formatNumber(chatbot.statistics.tokensInput)}
        </Text>
      </GridItem>

      <GridItem>
        <Text 
          fontSize="sm" 
          fontWeight="bold" 
          color="purple.600" 
          textAlign="center"
        >
          {formatNumber(chatbot.statistics.tokensOutput)}
        </Text>
      </GridItem>

      <GridItem>
        <Text 
          fontSize="sm" 
          fontWeight="bold" 
          color="green.600" 
          textAlign="center"
        >
          {formatNumber(chatbot.statistics.messagesSent)}
        </Text>
      </GridItem>

      <GridItem>
        <Text 
          fontSize="sm" 
          fontWeight="bold" 
          color="orange.600" 
          textAlign="center"
        >
          {formatNumber(chatbot.statistics.sessionsResponded)}
        </Text>
      </GridItem>
    </Grid>
  );
}
