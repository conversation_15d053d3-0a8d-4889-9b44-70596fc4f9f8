import { useAuthContext } from "@/providers/AuthProvider";
import {
  CHATBOTPERMISSIONS,
  CONFIGPERMISSIONS,
  WEBCHATPERMISSIONS,
  WHATSAPPERMISSIONS,
} from "../types/permissions/all-attendant-permissions";
import { UserAuthProps } from "../types/global/UserAuth";

export default function HasPermission(
  permission: string,
  thisUser?: UserAuthProps
) {
  //Essa caso é muito especifico, pois so sera usado na hora que o user estiver logando
  if (thisUser) {
    const viewPermissions = thisUser?.activeAccount.viewPermissionsSlugs || [];

    if (thisUser?.activeAccount.roleSlug === "MASTER") {
      return true;
    }
    //Praticamente o user master da conta
    if (viewPermissions.includes("app_view")) {
      return true;
    }

    if (viewPermissions.includes(permission)) {
      return true;
    }

    return false;
  }

  const { viewPermissions, user } = useAuthContext();

  if (user?.activeAccount.roleSlug === "MASTER") {
    return true;
  }
  //Praticamente o user master da conta
  if (viewPermissions.includes("app_view")) {
    return true;
  }

  //NOTE: Juro que não sei o q eu pensei quando criei uma permissão para ver a aba de configuracoes, sabendo q o maluco do usuario pode dar todas permissoes da config, porem nao marcar o config_view. Talvez eu precisasse arrumar isso no back, caso o user tenho alguma permissão da config, dar a ele a permissão de ver a aba de configurações
  if (permission === CONFIGPERMISSIONS.VIEW) {
    return (
      viewPermissions.includes(CONFIGPERMISSIONS.VIEW) ||
      viewPermissions.includes(CHATBOTPERMISSIONS.VIEW) ||
      viewPermissions.includes(WHATSAPPERMISSIONS.VIEW) ||
      viewPermissions.includes(WEBCHATPERMISSIONS.VIEW)
    );
  }

  return viewPermissions.includes(permission);
}
