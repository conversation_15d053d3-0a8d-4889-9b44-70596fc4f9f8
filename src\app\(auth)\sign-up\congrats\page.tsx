"use client";

import { Flex, Text, VStack } from "@chakra-ui/react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Congratulations() {
  // const checkoutData = JSON.parse(localStorage.getItem('checkoutData') || '{}');
  const router = useRouter();
  const [checkoutData, setCheckoutData] = useState<{ userName: string; planName: string }>({ userName: '', planName: '' });
  
  useEffect(() => {
    const data = localStorage.getItem('checkoutData');
    if (data) {
      setCheckoutData(JSON.parse(data));
    }
  }, []);

  return (
    <Flex
      flex={1}
      justifyContent={"center"}
      alignSelf={"center"}
      >
    <VStack
      h={{ md: "auto"}}
      w={{ base: "100%", md: "xl" }}
      m={{ base: 5, md: 0 }}
      bgColor={"chatLoginCardBackground"}
      borderRadius={20}
      justify={"center"}
      align={"center"}
      gap={{ base: 6, md: 2 }}
      textAlign={"center"}
      px={{ md: 10 }}
      py={8}
    >
      <Flex
        h={"70px"}
        w={"226px"}
        bgImage="url(/logo.png)"
        bgPos="initial"
        bgRepeat="no-repeat"
        bgSize="contain"  
      />
      <Flex justifyContent={"center"} gap={4} alignItems={"center"}>
      {/* <IoCheckmarkCircleOutline size={64} color="#fd2264"/> */}
      <Text fontSize="3xl" fontWeight="bold" color="chatPrimary" mt={4} mb={2}>
        Parabéns, {checkoutData.userName}!
      </Text>
      </Flex>
      <Text fontSize="xl" color="gray.600" mb={4}>
        Sua assinatura do plano <strong>{checkoutData.planName}</strong> foi confirmada com sucesso.
      </Text>
      <Text fontSize="md" color="gray.500" mb={4}>
        Agradecemos por escolher nossos serviços. Aproveite todos os benefícios do seu novo plano!
      </Text>

      <Flex direction="row" justifyContent="center" w="100%">
        <Button
          type="button"
          w={"100%"}
          borderRadius={20}
          size="lg"
          fontWeight="700"
          bgColor="chatPrimary"
          color="white"
          transitionDuration="0.2s"
          _hover={{
            color: "chatPrimary",
            bgColor: "transparent",
            borderColor: "chatPrimary",
          }}
          _active={{
            transform: "scale(0.95)",
          }}
          onClick={() => {
            router.push('/');
          }}
        >
          ACESSAR
        </Button>
      </Flex>
    </VStack>
    </Flex>
  );
}

