"use client";
import { Tabs, Text } from "@chakra-ui/react";
import {
  LuBrainCircuit,
  LuBrainCog,
  LuCodeXml,
  LuHouse,
  LuPalette,
  Lu<PERSON>ser,
} from "react-icons/lu";
import { useState } from "react";
import { useAuthContext } from "@/providers/AuthProvider";
import GeneralTab from "./general-tab";
import { GetChatBotsDto } from "@/utils/types/DTO/chat-bots.dto";
import LeadCaptureTab from "./lead-capture-tab";
import ArtificialIntelligenceTab from "../artificial-intelligence-tab/index";
import KnowledgeBaseTab from "@/components/chatbot/knowledge-base";


type EditTabsProps = {
  chatBotData: GetChatBotsDto;
};

export default function EditTabs({ chatBotData }: EditTabsProps) {
  const [secureId, setSecureId] = useState(chatBotData.secureId);
  const [isAI, setIsAI] = useState(chatBotData.isAI);
  const [isLeadCapture, setIsLeadCapture] = useState(
    chatBotData.isLeadCaptureActive
  );
  const { user } = useAuthContext();
  const collectInfosArrayFromObject = chatBotData?.leadCaptureJson
    ? Object.entries(chatBotData.leadCaptureJson)
        .filter(([_, value]) => value === true)
        .map(([key]) => key)
    : [];

  return (
    <Tabs.Root
      defaultValue="general"
      activationMode="manual"
      orientation="vertical"
      gap={1}
      flex={1}
      size={"lg"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
        maxW={{ base: "25%", "2xl": "15%" }}
        minW={{ base: "25%", "2xl": "15%" }}
        alignItems={"flex-start"}
      >
        <Tabs.Trigger
          value="general"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <LuHouse />
          <Text fontWeight={"normal"}>Geral</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="lead-capture"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          disabled={!(isLeadCapture && !!secureId)}
          pl={0}
        >
          <LuUser />
          <Text fontWeight={"normal"}>Captura de Lead</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="artificial-intelligence"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
          disabled={!(isAI && !!secureId)}
        >
          <LuBrainCog />
          <Text fontWeight={"normal"}>Inteligência Artificial</Text>
        </Tabs.Trigger>
        <>
          <Tabs.Trigger
            value="knowledge-base"
            justifyContent={"flex-start"}
            _selected={{
              bgColor: "chatCardBackground",
              color: "chatPrimary",
              borderRightColor: "chatPrimary",
              shadow: "none",
              borderRadius: 0,
            }}
            width={"100%"}
            border={"1px solid"}
            borderColor={"transparent"}
            pl={0}
            // disabled={!(isAI && !!secureId)}
          >
            <LuBrainCircuit />
            <Text fontWeight={"normal"}>Base de Conhecimento</Text>
          </Tabs.Trigger>
        </>

        <>
          <Tabs.Trigger
            value="flow"
            justifyContent={"flex-start"}
            _selected={{
              bgColor: "chatCardBackground",
              color: "chatPrimary",
              borderRightColor: "chatPrimary",
              shadow: "none",
              borderRadius: 0,
            }}
            width={"100%"}
            border={"1px solid"}
            borderColor={"transparent"}
            pl={0}
            disabled={!(!isAI && !!secureId)}
          >
            <LuPalette />
            <Text fontWeight={"normal"}>Fluxo</Text>
          </Tabs.Trigger>
        </>
      </Tabs.List>

      <Tabs.Content value="general" width={"100%"}>
        <GeneralTab
          secureId={secureId}
          isAi={chatBotData.isAI}
          chatBotName={chatBotData.name}
          isLeadCaptureActive={chatBotData.isLeadCaptureActive}
          setIsLeadCapture={setIsLeadCapture}
        />
      </Tabs.Content>

      <Tabs.Content value="knowledge-base" width={"100%"}>
        <KnowledgeBaseTab chatBotSecureId={secureId} />
      </Tabs.Content>
      <Tabs.Content value="lead-capture" width={"100%"}>
        <LeadCaptureTab
          chatBotSecureId={secureId}
          leadTriggerMessageLimit={chatBotData.leadTriggerMessageLimit}
          leadCaptureThankYouMessage={chatBotData.leadCaptureThankYouMessage}
          leadCaptureMessage={chatBotData.leadCaptureMessage}
          collectInfos={collectInfosArrayFromObject}
        />
      </Tabs.Content>
      <Tabs.Content value="artificial-intelligence" width={"100%"}>
        <ArtificialIntelligenceTab
          chatBotSecureId={secureId}
          emotionalTone={chatBotData.emotionalTone}
          mood={chatBotData.mood}
          responseSize={chatBotData.responseSize}
          responseStyle={chatBotData.responseStyle}
          temperature={chatBotData.temperature}
          greetingMessage={chatBotData.greetingMessage}
        />
      </Tabs.Content>
      <Tabs.Content value="flow" width={"100%"}>
        {/* <Flows /> */}
      </Tabs.Content>
    </Tabs.Root>
  );
}
