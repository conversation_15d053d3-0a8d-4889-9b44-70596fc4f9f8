import { jwtDecode } from "jwt-decode";
import { UserAuthProps } from "../types/global/UserAuth";

/**
 * Validates if a JWT token is valid and not expired
 * @param token The JWT token to validate
 * @returns An object with isValid flag and the decoded token if valid
 */
export function validateToken(token: string): { 
  isValid: boolean; 
  decodedToken?: UserAuthProps;
  errorMessage?: string;
} {
  try {
    if (!token) {
      return { isValid: false, errorMessage: "No token provided" };
    }

    const decodedToken: UserAuthProps = jwtDecode(token);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedToken.exp && decodedToken.exp < currentTime) {
      return { 
        isValid: false, 
        decodedToken, 
        errorMessage: "Token expired" 
      };
    }

    // Check if token has required fields
    if (!decodedToken.subject || !decodedToken.activeAccount) {
      return { 
        isValid: false, 
        decodedToken, 
        errorMessage: "Invalid token structure" 
      };
    }

    return { isValid: true, decodedToken };
  } catch (error) {
    return { 
      isValid: false, 
      errorMessage: error instanceof Error ? error.message : "Invalid token" 
    };
  }
}
