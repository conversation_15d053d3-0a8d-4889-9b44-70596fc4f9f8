import { MetaDTO } from "./meta.dto";

export type GetUsersBackofficeDto = {
  meta: MetaDTO;
  data: UsersBackofficeDto[];
};

type UsersBackofficeDto = {
  secureId: string;
  email: string;
  name: string;
  cpf: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type GetUserBackofficeDto = {
  secureId: string;
  email: string;
  name: string;
  cpf: string;
  hasAllPermissions: boolean;
  permissions: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
