"use client";
import {
  <PERSON><PERSON>,
  VStack,
  Text,
  Center,
  Spinner,
  Button,
  HStack,
  useBreakpointValue,
  Tabs,
} from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import BackofficePageContainer from "../../components/backoffice-page-container";
import useBackofficeChatbotDetail from "@/hook/backoffice/useBackofficeChatbotDetail";
import ConfigurationDisplay from "./components/configuration-display";
import MessageHistory from "./components/message-history";
import { LuArrowLeft, LuRefreshCw } from "react-icons/lu";
import Link from "next/link";
import { IoArrowBackOutline } from "react-icons/io5";
import { useMemo, useState } from "react";
import StatisticsOverview from "./components/statistics-overview";

export default function BackofficeChatbotDetail() {
  const params = useParams();
  const router = useRouter();
  const secureId = params.secureId as string;
  const isMobile = useBreakpointValue({ base: true, md: false });
  
  const [currentPage, setCurrentPage] = useState(1);

  const { 
    data: chatbot, 
    isLoading, 
    error, 
    refetch, 
    isFetching 
  } = useBackofficeChatbotDetail({ secureId, page: currentPage });

  console.log('chatbot: ', chatbot)

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  if (isLoading) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Spinner size="xl" color="white" />
            <Text color="white" mt={4}>
              Carregando detalhes do chatbot...
            </Text>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  if (error) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Text color="red.400" fontSize="lg" textAlign="center">
              Erro ao carregar detalhes do chatbot
            </Text>
            <Text color="gray.400" fontSize="sm" textAlign="center">
              Verifique se o ID do chatbot está correto
            </Text>
            <Button
              mt={4}
              colorScheme="red"
              variant="outline"
              onClick={() => router.push("/backoffice/chatbots")}
            >
              Voltar para lista
            </Button>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  if (!chatbot) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Text color="gray.400" fontSize="lg">
              Chatbot não encontrado
            </Text>
            <Button
              mt={4}
              colorScheme="purple"
              variant="outline"
              onClick={() => router.push("/backoffice/chatbots")}
            >
              Voltar para lista
            </Button>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  return (
    <BackofficePageContainer>
      <Flex direction="column" gap={6} w="100%">
        {/* Header */}
        <Flex
          direction={{ base: "column", md: "row" }}
          justify="space-between"
          align={{ base: "start", md: "center" }}
          gap={4}
        >
          <VStack align="start" gap={2}>
            <Text
              fontSize={{ base: "xl", md: "2xl" }}
              fontWeight="bold"
              color="white"
            >
              Detalhes do Chatbot
            </Text>
            <Text fontSize="md" color="gray.400">
              Visualização completa das configurações e estatísticas
            </Text>
          </VStack>

          <Button
            size="sm"
            colorScheme="purple"
            variant="outline"
            onClick={() => refetch()}
            disabled={isFetching}
          >
            <LuRefreshCw />
            {isFetching ? "Atualizando..." : "Atualizar"}
          </Button>
        </Flex>

        <Tabs.Root defaultValue="statistics" variant={"line"} w={"100%"}>
          <Tabs.List>
            <Link href="/backoffice/chatbots" passHref>
              <Tabs.Trigger value="back">
                <IoArrowBackOutline size={22}/>
              </Tabs.Trigger>
            </Link>
            <Tabs.Trigger value="statistics">
              Estátisticas
            </Tabs.Trigger>
            <Tabs.Trigger value="configuration">
              Configurações
            </Tabs.Trigger>
            <Tabs.Trigger value="messages">
              Mensagens
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="statistics">
            <StatisticsOverview chatbot={chatbot} />
          </Tabs.Content>
          <Tabs.Content value="configuration">
            <ConfigurationDisplay chatbot={chatbot} />
          </Tabs.Content>
          <Tabs.Content value="messages">
            <MessageHistory
              chatbot={chatbot} 
              isLoading={isFetching}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </BackofficePageContainer>
  );
}
