"use client";
import CardUserAccounts from "@/components/cards/card-user-accounts";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { InputPassword } from "@/components/global/inputs/input-password";
import { toaster } from "@/components/ui/toaster";
import { useGetProfile } from "@/hook/profile/useGetProfile";
import { useAuthContext } from "@/providers/AuthProvider";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  Separator,
  Text,
} from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

const updateProfileSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  cpf: yup
    .string()
    .required("CPF é obrigatório")
    .test("is-valid-cpf", "CPF inválido", (value) => {
      if (!value) return false;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 11;
    }),
  email: yup.string().required("Email é obrigatório"),
  cellPhone: yup
    .string()
    .test("is-valid-phone", "Telefone inválido", (value) => {
      if (!value) return true;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 13 || onlyNumbers.length === 12;
    }),
  password: yup.string().test(
    "is-valid-password",
    "A senha deve ter pelo menos 8 caracteres",
    (value) => {
      if (!value) return true;
      return value.length >= 8;
    }),
  confirmPassword: yup.string()
  .oneOf([yup.ref('password')], 'As senhas devem coincidir')
});

type UpdateProfileFormData = yup.InferType<typeof updateProfileSchema>;

export default function BackofficeProfile() {
  const [editMode, setEditMode] = useState(false);
  const [phoneMask, setPhoneMask] = useState("+99 (99) 99999-9999[9]");
  const { user } = useAuthContext();

  if (!user) return null;

  const { data: profile } = useGetProfile();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<UpdateProfileFormData>({
    resolver: yupResolver(updateProfileSchema),
    defaultValues: {
      name: "",
      cpf: "",
      email: "",
      cellPhone: "",
    },
  });

  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);

  useEffect(() => {
    if (profile) {
      setValue("name", profile.user.name);
      setValue("cpf", profile.user.cpf);
      setValue("email", profile.user.email);
      setValue("cellPhone", profile.user.cellPhone || "");
    }
  }, [profile, setValue]);

  const updateProfile = useMutation({
    mutationFn: async (data: UpdateProfileFormData) => {
      return await api.put(`/profile`, {
        name: data.name,
        cpf: data.cpf,
        email: data.email,
        cellPhone: data.cellPhone ? data.cellPhone : null,
        password: data.password ? data.password : null
      });
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso!",
      })
      queryClient.invalidateQueries({
        queryKey: ["getProfile"],
      });
      setEditMode(false);
    },
    onError: (error) => {
      console.error("Erro ao atualizar perfil:", error);
    },
  });

  const handleEdit = (data: UpdateProfileFormData) => {
    try {
      updateProfile.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <Flex
      flex={1}
      h="100%"
      w="100%"
      flexDirection="column"
      marginTop={{ base: "12rem", md: 0 }}
    >
      <Flex
        as={"form"}
        flex={1}
        flexWrap={"wrap"}
        p={{ base: 2, md: 5 }}
        m={{ base: 0, md: 5 }}
        borderRadius={{ base: 0, md: 15 }}
        bgColor="backgroundCard"
        flexDirection="column"
        onSubmit={handleSubmit(handleEdit)}
      >
        <Text
          fontSize={{ base: "xl", sm: "2xl" }}
          fontWeight="bold"
          marginBottom={4}
        >
          Meu Perfil
        </Text>
        <Separator />
        <Grid
          marginTop={4}
          templateColumns={{ base: "1fr", sm: "repeat(2, 1fr)" }}
          gap={{ base: editMode ? 0 : 6, sm: 6 }}
        >
          <GridItem>
            <Input
              label="Nome"
              borderRadius={20}
              color="gray.800"
              size={{ base: "sm", sm: "md" }}
              {...register("name")}
              error={errors.name}
            />
          </GridItem>
          <GridItem>
            <Input
              label="Email"
              borderRadius={20}
              color="gray.800"
              size={{ base: "sm", sm: "md" }}
              {...register("email")}
              error={errors.email}
            />
          </GridItem>
          <GridItem>
            <InputMaskIcon
              label="CPF"
              borderRadius={20}
              mask="999.999.999-99"
              color="gray.800"
              size={{ base: "sm", sm: "md" }}
              {...register("cpf")}
              error={errors.cpf}
            />
          </GridItem>
          <GridItem>
            <InputMaskIcon
              label="Celular"
              mask={phoneMask}
              color="gray.800"
              borderRadius={20}
              size={{ base: "sm", sm: "md" }}
              {...register("cellPhone")}
              error={errors.cellPhone}
            />
          </GridItem>
          <GridItem>
            <InputPassword
              height="80px"
              label="Senha"
              borderRadius={20}
              type="password"
              color="gray.800"
              size={{ base: "sm", sm: "md" }}
              {...register("password")}
              error={errors.password}
            />
          </GridItem>
          <GridItem>
            <InputPassword
              height="80px"
              label="Confirme sua senha"
              borderRadius={20}
              type="password"
              color="gray.800"
              size={{ base: "sm", sm: "md" }}
              {...register("confirmPassword")}
              error={errors.confirmPassword}
            />
          </GridItem>
        </Grid>
        <Flex justifyContent="flex-end" gap={4}>
          <Button
            borderRadius={20}
            size={"md"}
            type="submit"
            fontWeight="700"
            bgColor={"background"}
            transitionDuration={"0.2s"}
            color={"gray.200"}
            _hover={{
              bg: "gray.100",
              color: "background",
            }}
          >
            Salvar
          </Button>
        </Flex>
        {/* <Separator />
        <Box>
          <Text
            fontSize={{ base: "xl", sm: "2xl" }}
            fontWeight="bold"
            marginBottom={2}
          >
            Contas
          </Text>
          {profile && profile.userAccounts.length > 0
            ? profile.userAccounts.map((userAccount) => (
                <CardUserAccounts
                  key={userAccount.secureId}
                  userAccount={userAccount}
                />
              ))
            : null}
        </Box> */}
      </Flex>
    </Flex>
  );
}
