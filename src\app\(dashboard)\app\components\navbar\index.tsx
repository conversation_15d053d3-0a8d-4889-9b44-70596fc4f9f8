import { usePathname } from "next/navigation";
import { VStack, Box } from "@chakra-ui/react";
import { motion, AnimatePresence } from "framer-motion";
import NavButtons from "../navbuttons";
import {
  Lu<PERSON><PERSON>User,
  LuBrainCircuit,
  LuChartNoAxesCombined,
  LuSettings,
  LuMessageSquareMore 
} from "react-icons/lu";
import { useAuthContext } from "@/providers/AuthProvider";
import {
  CHATBOTPERMISSIONS,
  CHATPERMISSIONS,
  CONFIGPERMISSIONS,
  CONTACTPERMISSIONS,
  DASHBOARDPERMISSIONS,
  REPORTPERMISSIONS,
  WE<PERSON><PERSON>TPERMISSIONS,
  WHATSAPPERMISSIONS,
} from "@/utils/types/permissions/all-attendant-permissions";
import HasPermission from "@/utils/funcs/has-permission";
import { useState } from "react";

// Criando um componente de motion para o VStack
const MotionVStack = motion.create(VStack);

export default function AppNavBar() {
  const { viewPermissions } = useAuthContext();
  const pathname = usePathname();
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Configuração da animação
  const variants = {
    expanded: { width: "8rem", transition: { duration: 0.3, ease: "easeInOut" } },
    collapsed: { width: "4rem", transition: { duration: 0.3, ease: "easeInOut" } }
  };
  
  return (
    <Box 
      h="100%" 
      hideBelow={"md"}
      position="relative"
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      <MotionVStack
        initial="collapsed"
        animate={isExpanded ? "expanded" : "collapsed"}
        variants={variants}
        h="100%"
        justifyContent={"space-between"}
        rounded={"2xl"}
        bgColor={"chatCardBackground"}
        p={5}
        overflow="hidden"
      >
        <VStack gap={4} width="100%">
          {HasPermission(DASHBOARDPERMISSIONS.VIEW) && (
            <NavButtons
              icon={<LuChartNoAxesCombined size={"30"} />}
              label="Inicio"
              redirect={"/app/dashboard"}
              active={pathname === "/app/dashboard"}
              showLabel={isExpanded}
            />
          )}
          {HasPermission(CHATPERMISSIONS.VIEW) && (
            <NavButtons
              icon={<LuMessageSquareMore size={"30"} />}
              label="Chat"
              redirect={"/app/chat"}
              active={pathname === "/app/chat"}
              showLabel={isExpanded}
            />
          )}
          {HasPermission(CONTACTPERMISSIONS.VIEW) && (
            <NavButtons
              icon={<LuBookUser size={"30"} />}
              label="Contatos"
              redirect={"/app/contacts"}
              active={pathname === "/app/contacts"}
              showLabel={isExpanded}
            />
          )}
        </VStack>
        {HasPermission(CONFIGPERMISSIONS.VIEW) && (
          <NavButtons
            icon={<LuSettings size={"30"} />}
            label="Configurações"
            redirect={"/app/config"}
            active={pathname === "/app/config"}
            showLabel={isExpanded}
          />
        )}
      </MotionVStack>
    </Box>
  );
}
