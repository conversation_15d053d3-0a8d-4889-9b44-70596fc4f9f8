import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  Bold,
  <PERSON>ing1,
  Heading2,
  Heading3,
  Highlighter,
  Italic,
  List,
  ListOrdered,
  Strikethrough,
} from "lucide-react";
import { Editor } from "@tiptap/react";
import { <PERSON><PERSON>, HStack, Box } from "@chakra-ui/react";
import { useCallback } from "react";

// Remove the memo wrapper as it prevents proper updates
export default function MenuBar({ editor }: { editor: Editor | null }) {
  if (!editor) {
    return null;
  }
  
  // Create a helper function for button clicks to ensure proper focus
  const handleCommand = useCallback((callback: () => boolean) => {
    return () => {
      if (editor) {
        callback();
      }
    };
  }, [editor]);
  
  const Options = [
    {
      icon: <Heading1 size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleHeading({ level: 1 }).run()),
      pressed: editor.isActive("heading", { level: 1 }),
      tooltip: "Heading 1",
    },
    {
      icon: <Heading2 size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleHeading({ level: 2 }).run()),
      pressed: editor.isActive("heading", { level: 2 }),
      tooltip: "Heading 2",
    },
    {
      icon: <Heading3 size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleHeading({ level: 3 }).run()),
      pressed: editor.isActive("heading", { level: 3 }),
      tooltip: "Heading 3",
    },
    {
      icon: <Bold size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleBold().run()),
      pressed: editor.isActive("bold"),
      tooltip: "Bold",
    },
    {
      icon: <Italic size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleItalic().run()),
      pressed: editor.isActive("italic"),
      tooltip: "Italic",
    },
    {
      icon: <Strikethrough size={16} />,
      onClick: handleCommand(() => editor.chain().focus().toggleStrike().run()),
      pressed: editor.isActive("strike"),
      tooltip: "Strikethrough",
    },
    {
      icon: <AlignLeft size={16} />,
      onClick: handleCommand(() => editor.chain().focus().setTextAlign("left").run()),
      pressed: editor.isActive({ textAlign: "left" }),
      tooltip: "Align Left",
    },
    {
      icon: <AlignCenter size={16} />,
      onClick: handleCommand(() => editor.chain().focus().setTextAlign("center").run()),
      pressed: editor.isActive({ textAlign: "center" }),
      tooltip: "Align Center",
    },
    {
      icon: <AlignRight size={16} />,
      onClick: handleCommand(() => editor.chain().focus().setTextAlign("right").run()),
      pressed: editor.isActive({ textAlign: "right" }),
      tooltip: "Align Right",
    },
    {
      icon: <List size={16} />,
      onClick: handleCommand(() => {
        if (editor.can().toggleBulletList()) {
          return editor.chain().focus().toggleBulletList().run();
        }
        return false;
      }),
      pressed: editor.isActive("bulletList"),
      tooltip: "Bullet List",
    },
  ];
  
  return (
    <Box 
      borderRadius="md" 
      padding="4px" 
      marginBottom="4px" 
      zIndex="50"
      width="100%"
    >
      <HStack gap={2} wrap="wrap"  justifyContent={'center'}>
        {Options.map((option, index) => (
          <Button
            key={`editor-option-${index}`}
            size="md"
            onClick={option.onClick}
            color={option.pressed ? "chatPrimary" : "chatTextColor"}
            _hover={{
              backgroundColor: "transparent",
              color: "chatPrimary",
            }}
            aria-label={option.tooltip}
          >
            {option.icon}
          </Button>
        ))}
      </HStack>
    </Box>
  );
}
