import { api } from "@/services/api";
import { GetAllContactsDto } from "@/utils/types/DTO/get-all-contacts.dto";
import { useQuery } from "@tanstack/react-query";

export function useGetAllContacts() {
  return useQuery({
    queryKey: ["contacts"],
    queryFn: async () => await getAllContacts(),
  });
}

async function getAllContacts() {
  const { data } = await api.get<GetAllContactsDto>(`/contact`);
  return data;
}
