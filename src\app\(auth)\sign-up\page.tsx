"use client";

import { Flex, VStack, Text, Grid, } from "@chakra-ui/react";
import React, { useEffect } from "react";
import CardSignUp from "@/app/(auth)/sign-up/components/card-sign-up";
import ChatBubbleGeneric from "./components/chat-bubble-generic";
import SignUpForm from "./components/sign-up-form";

export default function SignUp() {
  // Google Tag Manager
  useEffect(() => {
    const gtmScript = document.createElement("script");
    gtmScript.innerHTML = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-5CMQ655Q');
    `;
    document.head.appendChild(gtmScript);

    const noscriptElement = document.createElement("noscript");
    noscriptElement.innerHTML = `
      <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5CMQ655Q"
      height="0" width="0" style="display:none;visibility:hidden"></iframe>
    `;
    document.body.insertBefore(noscriptElement, document.body.firstChild);
  }, []);

  return (
    <Flex flex={1} w={"100vw"} minH={"100vh"} flexDir={"column"} background={"#FFF4F8"} px={{base: "15px", md: "80px"}} py={"30px"}>
      {/* Banner Section - Takes most of the screen */}
      <Flex
        w="full"
        alignSelf="center"
        bgImage={{ base: "url(/signUpBannerMobile.jpg)", md: "url(/signUpBanner.jpg)" }}
        bgPos="center"
        bgRepeat="no-repeat"
        bgSize="cover"
        position="relative"
        borderRadius="2xl"
        aspectRatio={{ base: 0.92, md: 2.43 }} 
      />

      {/* Main Content Section */}
      <Flex
        w={"full"}
        flexDir={{ base: "column", lg: "row" }}
        bgColor={"chatLoginCardBackground"}
        gap={{ base: 6, md: 8 }}
        marginTop={{base: 4, sm: 8, lg: 12}}
        flex={1}
      >
        {/* Left Side - Benefits and Testimonial */}
        <VStack
          w={{ base: "full", lg: "50%", xl: "60%" }}
          align={{base: "center", lg: "flex-start"}}
          gap={6}
        >
          {/* Benefits Section */}
          <VStack align={{base: "center", lg: "flex-start"}} w="full" gap={4}>
            <Text
              color={"chatPrimary"}
              fontSize={{ base: "lg", md: "xl" }}
              fontWeight={"bold"}
            >
              Benefícios que fazem a diferença, na prática
            </Text>
            <Grid
              templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(3, 1fr)", lg: "repeat(2, 1fr)", xl: "repeat(3, 1fr)" }}
              justifyItems={{base: "center", lg: "start"}}
              gap={4}
              w={{base: "auto", lg: "full"}}
            >
              <CardSignUp
                title="Atendimento automático com IA"
              />
              <CardSignUp
                title="Personalização do seu chatbot"
              />
              <CardSignUp
                title="Multi-canal: WhatsApp, site, Instagram, e mais"
              />
              <CardSignUp
                title="Até 100 conversas gratuitas no plano inicial"
              />
              <CardSignUp
                title="Grátis para sempre"
              />
              <CardSignUp
                title="Não precisa cadastrar o cartão de crédito"
              />
            </Grid>
          </VStack>

          {/* Testimonial Section */}
          <VStack align="center" w="full" gap={4}>
            <Text
              color={"chatPrimary"}
              fontSize={{ base: "lg", md: "xl" }}
              fontWeight={"bold"}
            >
              Quem usa comprova!
            </Text>
            <ChatBubbleGeneric
              name="Ricardo Cassemiro"
              message="Automatizei 70% dos atendimentos com a Plyrchat. O bot responde com precisão, 24/7, treinado com meu conteúdo. Equipe mais leve, cliente mais feliz e resultado no caixa. Nunca mais fico refém de FAQ."
            />
          </VStack>
        </VStack>
        {/* Right Side - Form */}
        <SignUpForm
          w={{ base: "full", lg: "50%", xl: "40%" }}
        />
      </Flex>
    </Flex>
  );
}

