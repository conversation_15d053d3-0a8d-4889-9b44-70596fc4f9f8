import { Button } from "@/components/ui/button";
import { Box, Flex, Text, VStack } from "@chakra-ui/react";
import { useRouter } from "next/navigation";

type CardPlanProps = {
  name: string;
  w?: string;
  slug: string;
  description: string;
  price: string;
  details: string;
  showButton?: boolean;
  isCurrentPlan?: boolean;
};

export default function CardPlanApp({ name, w='full', slug, description, price, details, showButton = true, isCurrentPlan = false }: CardPlanProps) {
  const router = useRouter();

  return (
    <Box
      // w={{base: "full", '2xl': "400px"}}
      w={w}
      maxW={'400px'}
      h={"full"}
      border={"1px solid"}
      borderColor={isCurrentPlan ? "chatPrimary" : "#d0d0d0"}
      borderRadius={10}
      mb={4}
    >
      <Flex
        justifyContent={"center"}
        alignItems={"center"}
        flexDirection={"column"}
        borderBottom={"1px solid #d0d0d0"}
        backgroundColor={"chatPrimary"}
        borderTopRadius={10}
        py={6}
        px={4}
      >
        <Text fontSize="xl" justifySelf={"center"} textAlign={"center"} fontWeight="bold" color="white" mb={2}>
          {name}
        </Text>
        <Text fontSize="sm" justifySelf={"center"} textAlign={"center"} color="gray.200">
          {description}
        </Text>
      </Flex>
      <Flex
        justifyContent={"center"}
        alignItems={"center"}
        flexDirection={"column"}
        py={6}
        borderBottom={"1px solid #d0d0d0"}
      >
        <Text fontSize="xl" fontWeight="bold" color="chatPrimary">
          {price}
        </Text>
        <Text fontSize="sm" color="gray.600">
          por mês
        </Text>
        {showButton && (
          <Button
            bgColor={"chatPrimary"}
            alignSelf={"center"}
            color="white"
            w="auto"
            mt={2}
            borderRadius={20}
            size={"sm"}
            fontWeight="700"
            disabled={isCurrentPlan}
            _hover={{
              color: "chatPrimary",
              bgColor: "transparent",
              border: "1px solid",
              borderColor: "chatPrimary",
            }}
            onClick={() => router.push(`/app/checkout/${slug}`)}
          >
            {
              isCurrentPlan ? "Plano atual" : "Assine agora"
            }
          </Button>
        )}
      </Flex>
      <Flex justifyContent={"center"}  alignItems={"center"} flexDirection={"column"} p={4}>
        <div
          dangerouslySetInnerHTML={{ __html: details }}
          style={{
            fontSize: "14px",
            color: "#5e5e5e"
          }}
        />
      </Flex>
    </Box>
  )
}
