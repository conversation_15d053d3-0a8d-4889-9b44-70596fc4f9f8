"use client";
import { Tabs, Text } from "@chakra-ui/react";
import { HiOutlinePencilSquare } from "react-icons/hi2"
import AppProfileTab from "./tabs/profile-tab";
import AppSubscriptionTab from "./tabs/subscription-tab";
import { IoPersonCircleOutline } from "react-icons/io5";
import { useGetProfile } from "@/hook/profile/useGetProfile";
import { useEffect, useState } from "react";

export default function ProfileTabs() {
  const { data: profileData } = useGetProfile();

    const [activeTab, setActiveTab] = useState<string | undefined>(undefined);
  
    // Function to get tab value from URL hash
    const getTabFromHash = () => {
      if (typeof window !== "undefined") {
        const hash = window.location.hash.replace("#", "");
        return hash || "my-account"; // Default to whatsapp if no hash
      }
      return "my-account";
    };
  
    // Set active tab based on URL hash when component mounts
    useEffect(() => {
      const tabFromHash = getTabFromHash();
      setActiveTab(tabFromHash);
    }, []);
  
    // Listen for hash changes
    useEffect(() => {
      const handleHashChange = () => {
        const tabFromHash = getTabFromHash();
        setActiveTab(tabFromHash);
      };
  
      window.addEventListener("hashchange", handleHashChange);
      return () => {
        window.removeEventListener("hashchange", handleHashChange);
      };
    }, []);
  
    // Handle tab change
    const handleTabChange = (details: { value: string }) => {
      setActiveTab(details.value);
      // Update URL hash
      if (typeof window !== "undefined") {
        window.location.hash = details.value;
      }
    };

  return (
    <Tabs.Root
     value={activeTab}
      onValueChange={handleTabChange}
      activationMode="manual"
      orientation="vertical"
      gap={1}
      flex={1}
      size={"lg"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={"5"}
        width={{ base: "25%", "2xl": "15%" }}
        rounded={"2xl"}
        border={"none"}
        alignItems={"flex-start"}
      >
        <Tabs.Trigger
          value="my-account"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <IoPersonCircleOutline />
          <Text fontWeight={"normal"}>Minha conta</Text>
        </Tabs.Trigger>
        <Tabs.Trigger
          value="subscription"
          justifyContent={"flex-start"}
          _selected={{
            bgColor: "chatCardBackground",
            color: "chatPrimary",
            borderRightColor: "chatPrimary",
            shadow: "none",
            borderRadius: 0,
          }}
          width={"100%"}
          border={"1px solid"}
          borderColor={"transparent"}
          pl={0}
        >
          <HiOutlinePencilSquare />
          <Text fontWeight={"normal"}>Assinatura</Text>
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="my-account" width={"100%"}>
      {profileData?.user && (
        <AppProfileTab user={profileData.user} />
      )}
      </Tabs.Content>
      <Tabs.Content value="subscription" width={"100%"}>
      {profileData && (
        <AppSubscriptionTab
          subscription={profileData.subscription}
          transactions={profileData.transactions}
        />
      )}
      </Tabs.Content>
    </Tabs.Root>
  );
}
