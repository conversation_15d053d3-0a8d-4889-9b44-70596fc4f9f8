import { But<PERSON> } from "@/components/ui/button";
import { HStack, Icon } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import ModalAlterAttendant from "./modal-alter-attendant";
import { LuArchive, LuArchiveX, LuArrowRightLeft, LuBookmarkCheck, LuBookmarkX, LuMessageSquare, LuMessageSquareLock, LuMessageSquareOff, LuMessageSquareReply, LuUndo } from "react-icons/lu";
import { useChatSessionContext } from "@/providers/SessionProvider";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { useMutation } from "@tanstack/react-query";
import { queryClient } from "@/services/queryClient";
import { Tooltip } from "@/components/ui/tooltip";

type SessionsButtonsProps = {
  chatSessionSecureId: string;
};

export default function SessionsButtons({
  chatSessionSecureId,
}: SessionsButtonsProps) {
  const [isOpenedAlterAtendantModal, setIsOpenedAlterAtendantModal] =
    useState(false);
  const [archived, setArchived] = useState(false);
  const [finalized, setFinalized] = useState(false);
  const { sessionInfo } = useChatSessionContext();

  useEffect(() => {
    setArchived(sessionInfo?.isArchived || false);
    setFinalized(sessionInfo?.isFinalized || false);
  }, [sessionInfo]);

  const changeArchive = useMutation({
    mutationFn: async () => {
      await api.put(`/chat-sessions/${chatSessionSecureId}`, {
        isArchived: !archived,
      });
    },
    onSuccess: () => {
      setArchived(!archived);
      toaster.success({
        title: "Sucesso",
        description: !archived
          ? "Sessão arquivada com sucesso!"
          : "Sessão desarquivada com sucesso!",
      });
    },
    onError: (error) => {
      toaster.error({
        title: "Erro",
        description: "Erro ao alterar o status da sessão.",
      });
    },
  });

  const handleChangeArchive = async () => {
    await changeArchive.mutateAsync();
    queryClient.invalidateQueries({ queryKey: ["get-all-chat-sessions"] });
  };

  const finalizeSession = useMutation({
    mutationFn: async () => {
      await api.put(`/chat-sessions/${chatSessionSecureId}`, {
        isFinalized: !finalized,
      });
    },
    onSuccess: () => {
      setFinalized(!finalized);
      toaster.success({
        title: "Sucesso",
        description: !finalized
          ? "Sessão finalizada com sucesso!"
          : "Sessão retomada com sucesso!",
      });
    },
    onError: (error) => {
      toaster.error({
        title: "Erro",
        description: "Erro ao finalizar a sessão.",
      });
    },
  });

  const handleFinalizeSession = async () => {
    await finalizeSession.mutateAsync();
    queryClient.invalidateQueries({ queryKey: ["get-all-chat-sessions"] });
  };


  return (
    <>
      <HStack
        width={"100%"}
        gap={3}
        alignContent={"center"}
        justifyContent={"flex-end"}
        h={"45px"}
      >
        <Tooltip showArrow content="Alterar Atendente">
          <Button
            type={"button"}
            borderRadius={10}
            size={"lg"}
            w={"45px"}
            fontWeight="700"
            bgColor="transparent"
            color="chatPrimary"
            textWrap={"wrap"}
            onClick={() => setIsOpenedAlterAtendantModal(true)}
            transitionDuration={"0.2s"}
            _hover={{
              color: "white",
              bgColor: "chatPrimary",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
          >
            <Icon size={"lg"}>
              <LuArrowRightLeft />
            </Icon>
          </Button>
				</Tooltip>
        <Tooltip showArrow content={!archived ? "Arquivar Conversa" : "Desarquivar Conversa"}>
          <Button
            type={"button"}
            borderRadius={10}
            size={"lg"}
            w={"45px"}
            fontWeight="700"
            bgColor="transparent"
            color="chatPrimary"
            textWrap={"wrap"}
            onClick={handleChangeArchive}
            transitionDuration={"0.2s"}
            _hover={{
              color: "white",
              bgColor: "chatPrimary",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
          >
            <Icon size={"lg"}>
              {archived ? <LuArchiveX /> : <LuArchive />}
            </Icon>
          </Button>
        </Tooltip>
        <Tooltip showArrow content={!finalized ? "Finalizar Conversa" : "Retomar Conversa"}>
          <Button
            type={"button"}
            borderRadius={10}
            size={"lg"}
            w={"45px"}
            fontWeight="700"
            bgColor="transparent"
            color="chatPrimary"
            textWrap={"wrap"}
            onClick={handleFinalizeSession}
            transitionDuration={"0.2s"}
            _hover={{
              color: "white",
              bgColor: "chatPrimary",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
          >
            <Icon size={"lg"}>
              {finalized ? <LuMessageSquareReply /> : <LuMessageSquareLock />}
            </Icon>
          </Button>
        </Tooltip>
      </HStack>
      <ModalAlterAttendant
        isOpen={isOpenedAlterAtendantModal}
        setIsOpen={setIsOpenedAlterAtendantModal}
        chatSessionSecureId={chatSessionSecureId}
      />
    </>
  );
}
