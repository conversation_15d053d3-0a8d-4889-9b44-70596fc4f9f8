import { UserAccount } from "@/utils/types/DTO/user-account.dto";
import { Box, Text } from "@chakra-ui/react";

type CardUserAccountsProps = {
  userAccount: UserAccount;
}

export default function CardUserAccounts({ userAccount }: CardUserAccountsProps) {
  return (
    <Box
      key={userAccount.secureId}
      p={4}
      borderWidth={1}
      borderRadius={8}
      marginBottom={2}
      bg="background"
      _hover={{
        transition: "0.3s",
        transform: "scale(1.01)",
        cursor: "pointer",
      }}
    >
      <Text mb={2} fontSize={{base: "md", sm: "lg"}} fontWeight="bold">{userAccount.account.companyName}</Text>
      <Text fontSize={{base: "sm", sm: "md"}}>Proprietário: {userAccount.isOwner ? "Sim" : "Não"}</Text>
      <Text fontSize={{base: "sm", sm: "md"}}>Ativo: {userAccount.account.isActive ? "Sim" : "Não"}</Text>
      <Text fontSize={{base: "sm", sm: "md"}}>Deletado: {userAccount.account.isDeleted ? "Sim" : "Não"}</Text>
    </Box>
  )
}