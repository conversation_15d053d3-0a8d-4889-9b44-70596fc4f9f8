import { CheckboxGroup, Fieldset, VStack } from "@chakra-ui/react";
import { Checkbox } from "@/components/ui/checkbox";

interface CheckboxItem {
  value: string;
  label: string;
}

interface PermissionsCheckboxProps {
  name: string;
  label?: string;
  labelColor?: string;
  invalid?: boolean;
  items: CheckboxItem[];
  value: string[] | undefined;
  onValueChange: (...event: any[]) => void;
  hasLegend?: boolean;
}

export const PermissionsCheckbox = ({
  name,
  label,
  invalid,
  items,
  value,
  onValueChange,
  labelColor,
  hasLegend = true,
}: PermissionsCheckboxProps) => {
  return (
    <Fieldset.Root invalid={invalid}>
      {hasLegend ? (
        <Fieldset.Legend color={"chatTextColor"} fontSize={"md"}>
          Selecione as Permissões
        </Fieldset.Legend>
      ) : null}
      <CheckboxGroup
        invalid={invalid}
        value={value}
        onValueChange={onValueChange}
        name={name}
      >
        <Fieldset.Content>
          <VStack
            gap={4}
            align="start"
            flexWrap={"wrap"}
            maxH={{ base: "300px", "2xl": "500px" }}
            m={5}
            _scrollbar={{
              h: "8px",
              scrollbarColor: "#D6D6D6",
            }}
            _scrollbarThumb={{
              background: "#D6D6D6",
              borderRadius: "20px",
            }}
            _scrollbarTrack={{
              background: "chatBackground",
              height: "2px",
              borderRadius: "20px",
            }}
            pb={3}
            overflow={"auto"}
          >
            {items.map((item) => (
              <Checkbox key={item.value} value={item.value} size={"md"}>
                {item.label}
              </Checkbox>
            ))}
          </VStack>
        </Fieldset.Content>
      </CheckboxGroup>
    </Fieldset.Root>
  );
};
