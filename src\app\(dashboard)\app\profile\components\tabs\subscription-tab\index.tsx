import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Flex, Separator, Text } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import CardTransactionApp from "./components/card-transaction";
import { useState } from "react";
import BasicModal from "@/components/global/modal/basic-modal";
import { ProfileSubscriptionAndTransactions } from "@/utils/types/profile/subscription-transactions";
import CardPlanApp from "@/app/(dashboard)/app/components/cards/card-plan";
import { useRouter } from "next/navigation";

export default function AppSubscriptionTab({ subscription, transactions }: ProfileSubscriptionAndTransactions) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const cancelSubscription = useMutation({
    mutationFn: async () => {
      await api.delete(`/subscriptions`);
    },
    onSuccess: () => {
      toaster.create({
        description: "Assinatura cancelada com sucesso",
        title: "Assinatura cancelada",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
      setIsModalOpen(false);
    },
    onError: () => {},
  });

  const handleCancelSubscription = async () => {
    try {
      cancelSubscription.mutateAsync();
    } catch (e) {}
  };

  return (
    <Flex
      flex={1}
      w={"100%"}
      h={"100%"}
      justifyContent={"center"}
      bgColor={"chatCardBackground"}
      color={"chatTextColor"}
      p={8}
      rounded={"2xl"}
    >
      <Flex width={{base: "100%", md: "90%"}} h={"full"} gap={2} flexDir={"column"}>
        <Text
          color={"chatTextColor"}
          fontSize={"xl"}
          fontWeight={"medium"}
          mb={4}
        >
          Assinatura
        </Text>
        <Flex flex={1} width={"100%"} flexDirection={"row"} gap={6} justifyContent={"space-between"}>
          <Flex
            width={"60%"}
            flexDirection={"column"}
            maxH={{ base: "500px", "2xl": "700px" }}
            gap={4}
          >
            <Text fontWeight={"medium"} fontSize={"lg"}>Transações:</Text>
            <Flex
              flexDirection={"column"}
              gap={4}
              overflowY={"auto"}
              maxH={{base: "calc(500px - 2rem)", "2xl": "calc(700px - 2rem)"}}
              css={{
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  width: '10px',
                  background: 'var(--chakra-colors-chatBackground)',
                  borderRadius: '24px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'var(--chakra-colors-chatPrimary)',
                  borderRadius: '24px',
                },
              }}
            >
              {transactions && transactions.length > 0 ? (
                transactions.map((transaction) => (
                  <CardTransactionApp
                    key={transaction.secureId}
                    transactionAmount={transaction.amount}
                    transactionPayedAt={transaction.payedAt}
                    transactionStatus={transaction.status}
                  />
                ))
              ) : (
                <Text color="chatPrimary">Nenhuma transação nesta conta.</Text>
              )}
            </Flex>
          </Flex>
          <Flex borderLeft={"1px solid #D6D6D6"}/>
          <Flex flexDirection={"column"}>
            <Text mb={4} fontWeight={"medium"} fontSize={"lg"}>Plano ativo:</Text>
          {subscription?.plan ? (
            <>
              <CardPlanApp
                key={subscription.plan.secureId}
                name={subscription.plan.name}
                slug={subscription.plan.secureId}
                description={subscription.plan.description}
                price={subscription.plan.price}
                details={subscription.plan.details}
                showButton={false}
              />
              {subscription.plan.slug !== "freemium" ? (
                <Text
                  alignSelf={"center"}
                  color="gray.400"
                  fontSize={"xs"}
                  transitionDuration={"0.2s"}
                  _hover={{
                    color: "red.500",
                    cursor: "pointer",
                  }}
                  onClick={() => setIsModalOpen(true)}
                >
                  Cancelar assinatura
                </Text>
              ) : (
                <Text
                  alignSelf={"center"}
                  color="chatPrimary"
                  fontSize={"md"}
                  transitionDuration={"0.2s"}
                  _hover={{
                    textDecoration: "underline",
                    color: "chatPrimary",
                    cursor: "pointer",
                  }}
                  onClick={() => router.push("/app/choose-plans")}
                >
                  Assine um novo plano!
                </Text>
              )}
            </>
            ) : (
            <Text color="chatPrimary">Nenhum plano ativo nesta conta.</Text>
          )}
          </Flex>
        </Flex>
      </Flex>
      <BasicModal
        open={isModalOpen}
        placement="center"
        setOpen={setIsModalOpen}
        title="Tem certeza que deseja cancelar a assinatura?"
        cancelText="Voltar"
        deleteText="Cancelar assinatura"
        handleDelete={handleCancelSubscription}
      >
        <Text color={"gray.600"}>
          Ao cancelar a assinatura, você perderá acesso a todos os recursos do seu plano ativo.
        </Text>
      </BasicModal>
    </Flex>
  );
}

