"use client";
import { NavLink } from "@/components/global/navlink/navlink";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEACCOUNTPERMISSIONS, BACKOFFICEDASHBOARDPERMISSIONS, BACKOFFICEPLANSPERMISSIONS, BACKOFFICEUSERPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { Box, Stack } from "@chakra-ui/react";
import { IoAlbumsOutline, IoAppsOutline, IoBusinessOutline, IoPersonOutline } from "react-icons/io5";
import { LuBotMessageSquare } from "react-icons/lu";

export default function NavLinks() {
  return (
    <Stack flex={1} alignContent="center" w="100%" p={3}>
      <Box
        w="100%"
        my={7}
        h={"9"}
        bgImage="url(/logo.png)"
        bgPos="center"
        bgRepeat="no-repeat"
        bgSize="contain"
      />
      {HasBackofficePermission(BACKOFFICEDASHBOARDPERMISSIONS.VIEW) && (
        <NavLink
          href="/backoffice/dashboard"
          icon={<IoAppsOutline />}
          shouldMatchExactHref={true}
        >
          Dashboard
        </NavLink>
      )}
      {HasBackofficePermission(BACKOFFICEACCOUNTPERMISSIONS.VIEW) && (
        <NavLink
          href="/backoffice/accounts"
          icon={<IoBusinessOutline />}
          shouldMatchExactHref={true}
        >
          Contas
        </NavLink>
      )}
      {HasBackofficePermission(BACKOFFICEUSERPERMISSIONS.VIEW) && (
        <NavLink
          href="/backoffice/users"
          icon={<IoPersonOutline />}
          shouldMatchExactHref={true}
        >
          Usuários
        </NavLink>
      )}
      {HasBackofficePermission(BACKOFFICEPLANSPERMISSIONS.VIEW) && (
        <NavLink
          href="/backoffice/plans"
          icon={<IoAlbumsOutline />}
          shouldMatchExactHref={true}
        >
          Planos
        </NavLink>
      )}
      {HasBackofficePermission(BACKOFFICEPLANSPERMISSIONS.VIEW) && (
        <NavLink
          href="/backoffice/chatbots"
          icon={<LuBotMessageSquare />}
          shouldMatchExactHref={true}
        >
          Chatbots
        </NavLink>
      )}
      {/* <NavLink
        href="/backoffice/config"
        icon={<PiGearSix />}
        shouldMatchExactHref={true}
      >
        Configurações
      </NavLink> */}
    </Stack>
  );
}
