import { api } from "@/services/api";
import { GetBackofficeChatbotsDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { useQuery } from "@tanstack/react-query";

async function getBackofficeChatbots(page: number = 1, limit: number = 10) {
  const { data } = await api.get<GetBackofficeChatbotsDto>("/backoffice/chatbots", {
    // params: {
    //   page,
    //   limit,
    //   isDeleted: false,
    // },
  });
  return data;
}

export default function useBackofficeChatbots(page: number = 1, limit: number = 10) {
  return useQuery({
    queryFn: async () => await getBackofficeChatbots(page, limit),
    queryKey: ["backoffice-chatbots", page, limit],
    refetchInterval: 30000, // Refetch a cada 30 segundos para manter dados atualizados
  });
}