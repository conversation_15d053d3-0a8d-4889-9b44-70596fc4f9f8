import { PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, PaginationRootProps } from "@/components/ui/pagination";
import { HStack } from "@chakra-ui/react";

interface PaginationProps extends Omit<PaginationRootProps, "count" | "pageSize" | "page" | "onPageChange"> {
  totalItems: number;
  itemsPerPage: number;
  page: number;
  setPage: (page: number) => void;
  color?: string
  hoverColor?: string
}

export default function Pagination({
  totalItems,
  itemsPerPage,
  page,
  setPage,
  color= "chatPrimary",
  hoverColor= "white",
  ...rest
}: PaginationProps) {
  return (
    <PaginationRoot
      variant="subtle"
      count={totalItems}
      pageSize={itemsPerPage}
      page={page}
      onPageChange={(e) => setPage(e.page)}
      color={color}
      {...rest}
    >
      <HStack>
        <PaginationPrevTrigger color={color}_hover={{ bgColor: color, color: hoverColor }} />
        <PaginationPageText color={color} />
        <PaginationNextTrigger color={color} _hover={{ bgColor: color, color: hoverColor }} />
      </HStack>
    </PaginationRoot>
  )
}