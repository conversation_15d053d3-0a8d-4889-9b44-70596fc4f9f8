import { Stack, Text } from "@chakra-ui/react";
import { Switch } from "@/components/ui/switch";
import { forwardRef, ForwardRefRenderFunction, useState } from "react";
import { Controller } from "react-hook-form";

interface SwitchLabelProps {
  name: string;
  label: string;
  labelColor?: string;
  disabled?: boolean;
  labelSize?: string;
  onCheckedValue: (value: boolean) => void;
  checkedValue: boolean;
  onChange: () => void;
  control: any;
}
const SwitchBase = ({
  checkedValue,
  disabled,
  labelColor,
  labelSize,
  control,
  label,
  name,
  onChange,
  onCheckedValue,
}: SwitchLabelProps) => {
  return (
    <Stack gap="2" align="flex-start" mb={4}>
      <Stack align="center" direction="column" gap="2" px="4">
        <Text
          minW="8ch"
          fontSize={labelSize}
          color={labelColor}
          textAlign={"center"}
        >
          {label}
        </Text>
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <Switch
              disabled={disabled}
              id={field.name}
              name={field.name}
              onChange={onChange}
              checked={field.value}
              onCheckedChange={({ checked }) => {
                field.onChange(checked);
                onCheckedValue(checked);
              }}
              inputProps={{ onBlur: field.onBlur }}
            />
          )}
        />
      </Stack>
    </Stack>
  );
};

export const SwitchLabel = SwitchBase;
