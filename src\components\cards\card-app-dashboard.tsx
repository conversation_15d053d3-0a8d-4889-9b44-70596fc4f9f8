import { Box, HStack, VStack, Text } from "@chakra-ui/react";
import React from "react";

// Função para converter segundos para o formato HH:MM:SS
const formatSecondsToTime = (seconds: number): string => {
  if (!seconds && seconds !== 0) return "00:00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = remainingSeconds.toString().padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

type CardDashboardProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
  value: number;
};

export default function CardDashboard({
  icon,
  title,
  description,
  value,
}: CardDashboardProps) {
  return (
    <VStack
      bgColor={"chatPrimary"}
      justifyContent={"space-between"}
      p={5}
      h="100%"
      minH="150px"
      w="100%"
      rounded={"2xl"}
      gap={3}
      transition="transform 0.3s, box-shadow 0.3s"
      _hover={{
        transform: "translateY(-5px)",
        shadow: "md",
      }}
    >
      <HStack w="100%" align={"center"} justify={"flex-start"}>
        <Box color={"white"}>
          {icon}
        </Box>
        <Text fontSize={{base: "md", "2xl": "lg"}}>{title}</Text>
      </HStack>
      <Text
        fontWeight={"light"}
        color={"gray.300"}
         fontSize={{base: "xs", "2xl": "sm"}}
        alignSelf={"flex-start"}
      >
        {description}
      </Text>
      <VStack w="100%" align={"flex-end"}>
        <Text fontSize={{base: "lg", "2xl": "xl"}} fontWeight={"medium"}>
          {title === "Primeira Resposta" || title === "Finalizar Atendimento"
            ? formatSecondsToTime(value)
            : value}
        </Text>
      </VStack>
    </VStack>
  );
}
