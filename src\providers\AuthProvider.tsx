"use client";

import { UserAuthProps } from "@/utils/types/global/UserAuth";
import { usePathname, useRouter } from "next/navigation";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { api } from "@/services/api";
import Cookies from "js-cookie";
import { jwtDecode } from "jwt-decode";
import { DASHBOARDPERMISSIONS } from "@/utils/types/permissions/all-attendant-permissions";
import HasPermission from "@/utils/funcs/has-permission";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEDASHBOARDPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { validateToken } from "@/utils/funcs/validate-token";

type UserFormProps = {
  email: string;
  password: string;
};

type NewUserFormProps = {
  name: string;
  email: string;
  companyName: string;
  cellPhone: string;
  cpf: string;
  password: string;
};

type RegisterAPIResponseInputDTO = {
  newUserSecureId: string;
  newAccountSecureId: string;
};

type AuthContextData = {
  isAuthenticated: boolean;
  user: UserAuthProps | undefined;
  viewPermissions: string[];
  signOut: () => void;
  signIn: UseMutationResult<void, AxiosError<any, any>, UserFormProps, unknown>;
  signUp: UseMutationResult<
    void,
    AxiosError<any, any>,
    NewUserFormProps,
    unknown
  >;
  signUpEdit: UseMutationResult<
    void,
    AxiosError<any, any>,
    { userSecureId: string; data: NewUserFormProps },
    unknown
  >;
};

type AuthProviderProps = {
  userLogged?: UserAuthProps;
  children: ReactNode;
};

const AuthContext = createContext({} as AuthContextData);

export function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<UserAuthProps | undefined>();

  const isAuthenticated = useMemo(() => {
    return !!user;
  }, [user]);

  const signIn = useMutation({
    mutationFn: async (data: UserFormProps) => {
      const response: AxiosResponse = await api.post("/login", {
        email: data.email,
        password: data.password,
      });

      const { accessToken } = response.data;

      Cookies.set("__PlyrChat_Token", accessToken, {
        expires: 7,
        path: "/",
      });

      const decode: UserAuthProps = jwtDecode(accessToken);

      setUser(decode);

      api.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;

      //timer necessário, pois as vezes acaba que o user não é setado a tempo
      await new Promise((resolve) => setTimeout(resolve, 2000));
    },
    onSuccess: () => {
      const role = user!.activeAccount.roleSlug;

      if (role === "BACKOFFICE" || role === "MASTER") {
        if (
          HasBackofficePermission(BACKOFFICEDASHBOARDPERMISSIONS.VIEW, user)
        ) {
          router.push("/backoffice/dashboard");
        } else {
          router.push("/backoffice");
        }
      }
      if (role === "APP" || role === "ATTENDANT") {
        if (HasPermission(DASHBOARDPERMISSIONS.VIEW, user)) {
          router.push("/app/dashboard");
        } else {
          router.push("/app/");
        }
      }
    },
    onError: (error: AxiosError<any>) => {},
  });

  const signUp = useMutation({
    mutationFn: async (data: NewUserFormProps) => {
      const response: AxiosResponse = await api.post("/register", {
        name: data.name,
        email: data.email,
        companyName: data.companyName,
        cellPhone: data.cellPhone,
        cpf: data.cpf,
        password: data.password,
      });

      const { accessToken } = response.data;

      Cookies.set("__PlyrChat_Token", accessToken, {
        expires: 7,
        path: "/",
      });

      const decode: UserAuthProps = jwtDecode(accessToken);

      setUser(decode);

      api.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;

      //timer necessário, pois as vezes acaba que o user não é setado a tempo
      await new Promise((resolve) => setTimeout(resolve, 2000));
    },
    onSuccess: () => {
      const role = user!.activeAccount.roleSlug;

      if (role === "BACKOFFICE" || role === "MASTER") {
        if (
          HasBackofficePermission(BACKOFFICEDASHBOARDPERMISSIONS.VIEW, user)
        ) {
          router.push("/backoffice/dashboard");
        } else {
          router.push("/backoffice");
        }
      }
      if (role === "APP" || role === "ATTENDANT") {
        if (HasPermission(DASHBOARDPERMISSIONS.VIEW, user)) {
          router.push("/app/choose-plans");
        } else {
          router.push("/app/");
        }
      }
    },
    onError: (error: AxiosError<any>) => {},
  });

  const signUpEdit = useMutation({
    mutationFn: async ({
      userSecureId,
      data,
    }: {
      userSecureId: string;
      data: NewUserFormProps;
    }) => {
      const response: AxiosResponse = await api.put(
        `/register/${userSecureId}`,
        {
          name: data.name,
          email: data.email,
          companyName: data.companyName,
          cellPhone: data.cellPhone,
          cpf: data.cpf,
          password: data.password,
        }
      );
    },
    onSuccess: () => {},
    onError: (error: AxiosError<any>) => {},
  });

  function signOut() {
    Cookies.remove("__PlyrChat_Token");
    setUser(undefined);
    router.push("/");
  }

  // Check token validity on every route change
  useEffect(() => {
    const token = Cookies.get("__PlyrChat_Token");

    if (token) {
      const { isValid, decodedToken, errorMessage } = validateToken(token);

      if (isValid && decodedToken) {
        // Token is valid, set user and API headers
        api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
        setUser(decodedToken);
      } else {
        // Token is invalid or expired
        console.log(`Token validation failed: ${errorMessage}`);
        signOut();
      }
    }
  }, [pathname]);

  // Periodically check token validity (every minute)
  useEffect(() => {
    const checkTokenInterval = setInterval(() => {
      const token = Cookies.get("__PlyrChat_Token");

      if (token) {
        const { isValid, errorMessage } = validateToken(token);

        if (!isValid) {
          console.log(`Token periodic check failed: ${errorMessage}`);
          signOut();
        }
      }
    }, 60000); // Check every minute

    return () => clearInterval(checkTokenInterval);
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user: user,
        signIn,
        signOut,
        signUpEdit,
        signUp,
        isAuthenticated,
        viewPermissions: user?.activeAccount.viewPermissionsSlugs || [],
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  return useContext(AuthContext);
}
