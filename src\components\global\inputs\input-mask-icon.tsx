import {
  Input as ChakraInput,
  InputProps as ChakraInputProps,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";
import { withMask } from "use-mask-input";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  mask?: string;
  fieldHeight?: string;
  color?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  { name, label, labelColor, error, startElement, fieldHeight, mask = "", color, ...rest },
  ref
) => {
  // Usar useEffect para lidar com o ref corretamente
  const maskedRef = withMask(mask);
  color = color ? color : "chatPrimary";

  return (
    <Field
      invalid={!!error}
      h={fieldHeight ? fieldHeight : "60px"}
      errorText={error?.message}
      label={label}
      color={labelColor}
    >
      <InputGroup w="100%" startElement={startElement}>
        <ChakraInput
          id={name}
          name={name}
          ref={(e) => {
            maskedRef(e); // Chama a máscara
            if (ref) {
              // Propaga o ref para o react-hook-form
              if (typeof ref === "function") {
                ref(e);
              } else {
                ref.current = e;
              }
            }
          }}
          bg="white"
          color={color}
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: error ? "red.400" : color,
          }}
          _placeholder={{ color: "#B1A0A5" }}
          _focus={{
            borderColor: error ? "red.500" : color,
          }}
          size="md"
          {...rest}
        />
      </InputGroup>
    </Field>
  );
};

export const InputMaskIcon = forwardRef(InputBase);
