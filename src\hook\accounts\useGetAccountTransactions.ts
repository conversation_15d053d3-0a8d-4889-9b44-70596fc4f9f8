import { api } from "@/services/api";
import { GetAccountTransactionDto, GetAllAccountsTransactionsDto } from "@/utils/types/DTO/accounts-transactions.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllAccountsTransactions(page: number, accountSecureId?: string) {
  const limit = 4;
  const { data } = await api.get<GetAllAccountsTransactionsDto>(`/accounts-transactions?page=${page}&limit=${limit}`, {
    params: {
      accountSecureId
    }
  });
  return data;
}

export function useGetAllAccountsTransactions(page: number, accountSecureId?: string) {
  return useQuery({
    queryKey: ["accounts-transactions", page, accountSecureId],
    queryFn: async () => await getAllAccountsTransactions(page, accountSecureId),
  });
}

async function getAccountTransactions(secureId: string) {
  const { data } = await api.get<GetAccountTransactionDto>(`/accounts-transactions/${secureId}`);
  return data;
}

export function useGetAccountTransactions(secureId: string) {
  return useQuery({
    queryKey: ["accounts-transactions", secureId],
    queryFn: async () => await getAccountTransactions(secureId),
  });
}
