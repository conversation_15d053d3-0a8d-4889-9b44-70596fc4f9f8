"use client";
import { InputPassword } from "@/components/global/inputs/input-password";
import { InputIcon } from "@/components/global/inputs/input-icon";
import { Flex, VStack, Text } from "@chakra-ui/react";
import { Button } from "@/components/ui/button";
import { FaRegUser } from "react-icons/fa";
import Link from "next/link";
import * as yup from "yup";
import React, { useEffect } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { IoLockClosedOutline, IoMailOutline } from "react-icons/io5";
import { useAuthContext } from "@/providers/AuthProvider";
import { toaster } from "@/components/ui/toaster";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";

const SignInSchema = yup.object().shape({
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
  password: yup.string().required("A senha é obrigatória"),
});

type SignInFormData = yup.InferType<typeof SignInSchema>;

export default function SignIn() {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState,
    formState: { errors },
  } = useForm<SignInFormData>({
    resolver: yupResolver(SignInSchema),
  });

  const { signIn, signOut, isAuthenticated, user } = useAuthContext();

  useEffect(() => {
   const hasCookie = Cookies.get("__PlyrChat_Token");
   if (hasCookie) {
     user?.activeAccount.roleSlug === "BACKOFFICE" ||
     user?.activeAccount.roleSlug === "MASTER"
       ? router.push("/backoffice")
       : router.push("/app");
   }
  }, [user]);

  const handleSignIn = async (data: SignInFormData) => {
    try {
      await signIn.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <Flex flex={1} justifyContent={"center"} alignSelf={"center"}>
      <VStack
        h={{ md: "md" }}
        w={{ base: "80%", md: "lg" }}
        m={{ base: 5, md: 0 }}
        bgColor={"chatLoginCardBackground"}
        borderRadius={20}
        justify={"center"}
        align={"center"}
        gap={{ base: 10, md: 20 }}
      >
        <Flex
          mt={{ base: 5, md: 0 }}
          h={{ base: "55px", md: "70px" }}
          w={{ base: "206px", md: "306px" }}
          bgImage="url(/logo.png)"
          bgPos="initial"
          bgRepeat="no-repeat"
          bgSize="contain"
        />
        <VStack
          as="form"
          w={{ base: "80%", md: "100%" }}
          px={{ md: 20 }}
          onSubmit={handleSubmit(handleSignIn)}
          gap={4}
        >
          <InputIcon
            startElement={<IoMailOutline />}
            borderRadius={20}
            placeholder="Digite seu Email"
            size={"md"}
            {...register("email")}
            error={errors.email}
          />
          <InputPassword
            startElement={<IoLockClosedOutline />}
            borderRadius={20}
            placeholder="Senha"
            size={"md"}
            type="password"
            {...register("password")}
            error={errors.password}
          />
          <Button
            type="submit"
            w={"100%"}
            borderRadius={20}
            size={"md"}
            fontWeight="700"
            bgColor="chatPrimary"
            color="white"
            transitionDuration={"0.2s"}
            _hover={{
              color: "chatPrimary",
              bgColor: "transparent",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
            // loading={formState.isSubmitting|| signIn.isPending}}
            loading={formState.isSubmitting}
          >
            <FaRegUser />
            ENTRAR
          </Button>
          <Flex
            w="100%"
            mb={{ base: 5, md: 0 }}
            justifyContent={"space-between"}
            direction={"row"}
          >
            <Text
              fontSize="sm"
              color="#84767A"
              _hover={{
                color: "chatPrimary",
              }}
            >
              <Link href="/forgot-password">Esqueceu a senha?</Link>
            </Text>
            <Text
              fontSize="sm"
              color="#84767A"
              _hover={{
                color: "chatPrimary",
              }}
            >
              <Link href="/sign-up">Cadastrar-se</Link>
            </Text>
          </Flex>
        </VStack>
      </VStack>
    </Flex>
  );
}
