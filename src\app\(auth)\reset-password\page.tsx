"use client";
import { Flex, VStack, Text } from "@chakra-ui/react";
import * as yup from "yup";
import React, { Suspense } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { IoLockClosedOutline } from "react-icons/io5";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { InputPassword } from "@/components/global/inputs/input-password";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";

const ResetPasswordSchema = yup.object().shape({
  newPassword: yup.string().required("Digite sua nova senha"),
});

type ResetPasswordFormData = yup.InferType<typeof ResetPasswordSchema>;

function ResetPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const token = searchParams.get("token");

  const {
    register,
    handleSubmit,
    formState,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: yupResolver(ResetPasswordSchema),
  });

  const resetPassword = useMutation({
    mutationFn: async (data: ResetPasswordFormData) => {
      return await api.post(`/forgot-password/reset-password`, {
        token: token,
        newPassword: data.newPassword,
      });
    },
    onSuccess: () => {
      router.push("/");
      toaster.success({
        title: "Sucesso",
        description: "Senha alterada com sucesso!",
      });
    },
    onError: (error: any) => {
      console.error("Erro:", error);
    },
  });

  const handleResetPassword = async (data: ResetPasswordFormData) => {
    try {
      await resetPassword.mutateAsync(data);
    } catch (error) {}
  };

  return (
    <Flex flex={1} justifyContent={"center"} alignSelf={"center"}>
      <VStack
        h={{ base: "auto", sm: "sm", md: "md" }}
        w={{ base: "80%", md: "lg" }}
        m={{ base: 5, md: 0 }}
        bgColor={"chatLoginCardBackground"}
        borderRadius={20}
        justify={"center"}
        align={"center"}
        gap={{ base: 5, md: 10 }}
      >
        <Flex
          mt={{ base: 5, md: 0 }}
          h={{ base: "55px", md: "70px" }}
          w={{ base: "206px", md: "306px" }}
          bgImage="url(/logo.png)"
          bgPos="initial"
          bgRepeat="no-repeat"
          bgSize="contain"
        />
        <VStack
          as="form"
          w={{ base: "80%", md: "100%" }}
          px={{ md: 20 }}
          onSubmit={handleSubmit(handleResetPassword)}
          gap={{ base: 4, md: 6 }}
        >
          <VStack gap={1} textAlign={"center"}>
            <Text color={"chatPrimary"} fontWeight={"bold"} fontSize={"2xl"}>
              Redefinir Senha
            </Text>
            <Text color={"text"} justifySelf={"center"} fontSize={"md"}>
              Digite sua nova senha para atualizar seu acesso
            </Text>
          </VStack>

          <InputPassword
            startElement={<IoLockClosedOutline />}
            borderRadius={20}
            placeholder="Digite sua nova senha"
            size={"md"}
            {...register("newPassword")}
            error={errors.newPassword}
          />
          <VStack gap={2} w={"100%"}>
            <Button
              children={"ALTERAR"}
              loading={formState.isSubmitting}
              bgColor={"chatPrimary"}
              type="submit"
              w={"100%"}
              borderRadius={"full"}
              fontWeight={"bold"}
              color={"white"}
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
              }}
              _active={{
                transform: "scale(0.95)",
              }}
            />
          </VStack>
        </VStack>
      </VStack>
    </Flex>
  );
}

export default function ResetPassword() {
  return (
    <Suspense fallback={<div>Carregando...</div>}>
      <ResetPasswordContent />
    </Suspense>
  );
}
