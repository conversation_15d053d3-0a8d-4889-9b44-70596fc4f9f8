import { Tooltip } from "@/components/ui/tooltip"; // Presumindo que este é o seu componente Tooltip customizado
import { IconButton } from "@chakra-ui/react";
import React from "react"; // Necessário para React.ReactNode e React.MouseEvent

type ActionButtonProps = {
  icon: React.ReactNode; // O elemento do ícone (ex: <LuFilter />)
  tooltipLabel: string;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  bgColor?: string;
  color?: string;
  hoverBgColor?: string;
  hoverColor?: string;
  isActive?: boolean;
  activeBgColor?: string;
  activeColor?: string;
  // Permite outras props como 'transform', 'aria-expanded', etc.
  [key: string]: any;
};

export default function ActionButton({
  icon,
  tooltipLabel,
  onClick,
  bgColor: customBgColor = "#f2eeef",
  color: customColor = "chatPrimary",
  hoverBgColor,
  hoverColor,
  isActive,
  activeBgColor = "#f2eeef",
  activeColor = "chatPrimary",
  ...rest
}: ActionButtonProps) {
  const currentBg = isActive ? activeBgColor : customBgColor;
  const currentColor = isActive ? activeColor : customColor;

  return (
    <Tooltip showArrow content={tooltipLabel}>
      <IconButton
        aria-label={tooltipLabel}
        children={icon}
        fontSize="24px"
        size="lg"
        borderRadius={10}
        bg={currentBg}
        color={currentColor}
        onClick={onClick}
        transition="background-color 0.2s, color 0.2s, transform 0.2s, box-shadow 0.2s"
        _hover={{filter: "brightness(90%)"}}
        _active={{
          transform: "scale(0.95)",
        }}
        {...rest}
        boxShadow={isActive ? "inset 0 1px 3px rgba(0,0,0,0.2)" : "none"}
      />
    </Tooltip>
  );
}