import { HStack, Icon<PERSON><PERSON>on } from "@chakra-ui/react";
import { InputMessage } from "./input-message";
import { TbSend } from "react-icons/tb";
import { FormEventHandler, useRef } from "react";

interface ChatFooterStreamProps {
  handleSubmit: FormEventHandler<HTMLDivElement> | undefined;
  handleInputChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>
  ) => void;
  input: string;
  isLoading: boolean;
  inputMessageColor: string | null;
  inputBgColor: string | null;
  buttonBgColor: string | null;
}

// Renamed function to match export name
export default function ChatFooterStream({
  handleSubmit,
  buttonBgColor,
  inputBgColor,
  handleInputChange,
  isLoading,
  input,
  inputMessageColor,
}: ChatFooterStreamProps) {
  return (
    <HStack w="100%" as="form" onSubmit={handleSubmit}>
      <InputMessage
        name="message"
        rounded={20}
        color={inputMessageColor || "chatPrimary"}
        bgColor={inputBgColor || "chatBackground"}
        placeholder="Digite a mensagem"
        value={input}
        onChange={handleInputChange}
        _focus={{
          borderColor: inputBgColor || "chatTextColor",
        }}
        _hover={{
          borderColor: inputBgColor || "chatTextColor",
        }}
      />
      <IconButton
        type="submit"
        rounded="full"
        bgColor={buttonBgColor || "chatPrimary"}
        color="white"
        aria-label="Enviar mensagem"
        size="xl"
        borderWidth={2}
        transition="all 0.3s"
        disabled={isLoading}
        _active={{
          bgColor: "transparent",
          borderColor: "chatPrimary",
          color: "chatPrimary",
          borderWidth: 2,
        }}
      >
        <TbSend />
      </IconButton>
    </HStack>
  );
}
