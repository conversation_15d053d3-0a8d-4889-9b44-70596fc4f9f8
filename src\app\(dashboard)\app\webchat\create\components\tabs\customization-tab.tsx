"use client";
import {
  Flex,
  Grid,
  GridItem,
  VStack,
  HStack,
  parseColor,
  Text,
} from "@chakra-ui/react";
import HeaderTab from "@/components/tabs/header";
import InputPickColor from "@/components/global/inputs/input-color-picker";
import { useEffect, useState } from "react";
import ChatColorExample from "./chat-color-example";
import { toaster } from "@/components/ui/toaster";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { set } from "date-fns";
import { queryClient } from "@/services/queryClient";

type CustomizationTabProps = {
  secureId: string;
};

export default function CustomizationTab({ secureId }: CustomizationTabProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState(parseColor("#E2E2E2"));
  const [inputBg, setInputBg] = useState(parseColor("#FFF"));
  const [inputTextColor, setInputTextColor] = useState(parseColor("#FD2264"));
  const [buttonTextColor, setButtonTextColor] = useState(parseColor("#FFF"));
  const [buttonBgColor, setButtonBgColor] = useState(parseColor("#FD2264"));
  const [clientMessageBg, setClientMessageBg] = useState(parseColor("#FFF"));
  const [attendantMessageBg, setAttendantMessageBg] = useState(
    parseColor("#FFF")
  );
  const [clientTextColor, setClientTextColor] = useState(parseColor("#09090B"));
  const [attendantTextColor, setAttendantTextColor] = useState(
    parseColor("#09090B")
  );
  const [webChatButtonColor, setWebChatButtonColor] = useState(
    parseColor("#FD2264")
  );

  const handleSave = useMutation({
    mutationFn: async () => {
      const formData = new FormData();
      formData.append("chatBgColor", backgroundColor.toString("hex"));
      formData.append("inputChatBgColor", inputBg.toString("hex"));
      formData.append("inputChatTextColor", inputTextColor.toString("hex"));
      formData.append("chatIconColor", buttonTextColor.toString("hex"));
      formData.append("chatButtonColor", buttonBgColor.toString("hex"));
      formData.append(
        "customerMessageBubbleColor",
        clientMessageBg.toString("hex")
      );
      formData.append(
        "attendantMessageBubbleColor",
        attendantMessageBg.toString("hex")
      );
      formData.append(
        "customerMessageTextColor",
        clientTextColor.toString("hex")
      );
      formData.append(
        "attendantMessageTextColor",
        attendantTextColor.toString("hex")
      );
      formData.append(
        "webchatButtonBgColor",
        webChatButtonColor.toString("hex")
      );
      await api.put(`/chats/${secureId}`, formData);
    },
    onSuccess: () => {
      toaster.success({
        description: "Alterações salvas com sucesso",
        title: "Sucesso",
      });
      queryClient.invalidateQueries({
        queryKey: ["findChat", secureId],
      });
      setIsSubmitting(false);
    },
    onError: () => {
      setIsSubmitting(false);
    },
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    try {
      setIsSubmitting(true);
      await handleSave.mutateAsync();
    } catch (e) {
      setIsSubmitting(false);
    }
  };

  return (
    <Flex
      flex={1}
      h={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      <VStack
        flex={1}
        alignItems={"flex-start"}
        gap={5}
        as={"form"}
        onSubmit={handleSubmit}
      >
        <HeaderTab
          buttonTitle="Salvar Alterações"
          title="Customização"
          isSubmit={isSubmitting}
          hrefBack="/app/config#webchats"
        />
        <Grid templateColumns="repeat(5, 1fr)" w={"100%"}>
          <GridItem colSpan={3}>
            <VStack
              flex={1}
              alignItems={"flex-start"}
              gap={5}
              marginRight={1}
              maxHeight={"calc(100vh - 200px)"}
              overflow={"auto"}
              css={{
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  width: "6px",
                  marginBottom: "10px",
                },
                "&::-webkit-scrollbar-thumb": {
                  background: "#D6D6D6",
                  borderRadius: "24px",
                  "&:hover": {
                    background: "#A6A6A6",
                  },
                },
              }}
            >
              <Text
                color={"chatTextColor"}
                w={"50%"}
                borderBottom={"1px solid #E2E2E2"}
                pb={3}
              >
                WebChat
              </Text>
              <InputPickColor
                label="Cor do fundo do chat"
                color={backgroundColor}
                setColor={setBackgroundColor}
              />
              <HStack flexWrap="wrap">
                <InputPickColor
                  label="Cor do fundo da caixa de texto"
                  color={inputBg}
                  setColor={setInputBg}
                />
                <InputPickColor
                  label="Cor da fonte da caixa de texto"
                  color={inputTextColor}
                  setColor={setInputTextColor}
                />
              </HStack>
              <HStack flexWrap="wrap">
                <InputPickColor
                  label="Cor do ícone do botão"
                  color={buttonTextColor}
                  setColor={setButtonTextColor}
                />
                <InputPickColor
                  label="Cor do botão"
                  color={buttonBgColor}
                  setColor={setButtonBgColor}
                />
              </HStack>
              <HStack flexWrap="wrap">
                <InputPickColor
                  label="Cor do fundo da mensagem do cliente"
                  color={clientMessageBg}
                  setColor={setClientMessageBg}
                />
                <InputPickColor
                  label="Cor do fundo da mensagem do atendente"
                  color={attendantMessageBg}
                  setColor={setAttendantMessageBg}
                />
              </HStack>
              <HStack flexWrap="wrap">
                <InputPickColor
                  label="Cor do texto do cliente"
                  color={clientTextColor}
                  setColor={setClientTextColor}
                />
                <InputPickColor
                  label="Cor do texto do atendente"
                  color={attendantTextColor}
                  setColor={setAttendantTextColor}
                />
              </HStack>
              <Text
                color={"chatTextColor"}
                w={"50%"}
                borderBottom={"1px solid #E2E2E2"}
                pb={3}
              >
                Botão para abrir WebChat
              </Text>
              <InputPickColor
                label="Cor do botão parar abrir o chat"
                color={webChatButtonColor}
                setColor={setWebChatButtonColor}
              />
            </VStack>
          </GridItem>
          <GridItem colSpan={2}>
            <ChatColorExample
              attendantMessageBg={attendantMessageBg.toString("hex")}
              attendantTextColor={attendantTextColor.toString("hex")}
              backgroundColor={backgroundColor.toString("hex")}
              buttonBgColor={buttonBgColor.toString("hex")}
              buttonTextColor={buttonTextColor.toString("hex")}
              clientMessageBg={clientMessageBg.toString("hex")}
              clientTextColor={clientTextColor.toString("hex")}
              inputBg={inputBg.toString("hex")}
              inputTextColor={inputTextColor.toString("hex")}
            />
          </GridItem>
        </Grid>
      </VStack>
    </Flex>
  );
}
