import { api } from "@/services/api";
import { GetChatBotsDto } from "@/utils/types/DTO/chat-bots.dto";
import { MetaDTO } from "@/utils/types/DTO/meta.dto";
import { useQuery } from "@tanstack/react-query";

type GetChatBotsDtoInput = {
  meta: MetaDTO;
  data: GetChatBotsDto[];
};

async function findAllChatsBots() {
  const { data } = await api.get<GetChatBotsDtoInput>("/chatbots", {
    params: {
      isDeleted: false,
    },
  });
  return data;
}

export default function useListChatBots() {
  return useQuery({
    queryFn: async () => await findAllChatsBots(),
    queryKey: ["list-chat-bots"],
  });
}
