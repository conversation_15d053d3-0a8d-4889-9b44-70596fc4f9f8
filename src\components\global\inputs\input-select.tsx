import {
  InputProps as ChakraInputProps,
  Input as ChakraInput,
  Box,
} from "@chakra-ui/react";

import { forwardRef, ForwardRefRenderFunction, useState, useMemo } from "react";
import { Controller, FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";
import { IoChevronDown } from "react-icons/io5";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  height?: string;
  placeholder?: string;
  control: any;
  itensList: { label: string; value: string }[];
  multiple?: boolean;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  {
    name,
    label,
    error,
    startElement,
    endElement,
    height,
    labelColor,
    control,
    itensList,
    placeholder,
    multiple = false,
    ...rest
  },
  ref
) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Filtrar itens baseado no termo de busca
  const filteredItems = useMemo(() => {
    if (!searchTerm) return itensList;
    return itensList.filter(item =>
      item.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [itensList, searchTerm]);

  return (
    <Field
      invalid={!!error}
      h={"60px"}
      errorText={error?.message}
      label={label}
      labelColor={labelColor}
    >
      <Controller
        control={control}
        name={name}
        render={({ field }) => {
          // Determinar o valor atual baseado no tipo (array ou string)
          const currentValue = multiple ?
            (Array.isArray(field.value) ? field.value : (field.value ? [field.value] : [])) :
            (Array.isArray(field.value) ? field.value[0] : field.value);

          // Encontrar o label do valor selecionado para exibição
          let displayValue = "";
          if (multiple && Array.isArray(currentValue)) {
            const selectedItems = itensList.filter(item => currentValue.includes(item.value));
            displayValue = selectedItems.length > 0 ?
              `${selectedItems.length} selecionado(s)` : "";
          } else if (!multiple && currentValue) {
            const selectedItem = itensList.find(item => item.value === currentValue);
            displayValue = selectedItem ? selectedItem.label : "";
          }

          return (
            <Box position="relative" w="100%">
              <InputGroup w="100%" endElement={
                <Box
                  cursor="pointer"
                  onClick={() => setIsOpen(!isOpen)}
                  display="flex"
                  alignItems="center"
                  pr={2}
                >
                  <IoChevronDown
                    size={20}
                    style={{
                      transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s'
                    }}
                  />
                </Box>
              }>
                <ChakraInput
                  ref={ref}
                  id={name}
                  name={name}
                  bg="white"
                  color={"chatPrimary"}
                  borderColor="#D6D6D6"
                  borderWidth={2}
                  borderRadius={20}
                  _hover={{
                    borderColor: error ? "red.400" : "chatPrimary",
                  }}
                  _placeholder={{ color: "#B1A0A5" }}
                  _focus={{
                    borderColor: error ? "red.500" : "chatPrimary",
                  }}
                  size="md"
                  placeholder={placeholder}
                  value={isOpen ? searchTerm : displayValue}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onFocus={() => {
                    setIsOpen(true);
                    setSearchTerm("");
                  }}
                  onBlur={() => {
                    // Delay para permitir clique nos itens
                    setTimeout(() => {
                      setIsOpen(false);
                      setSearchTerm("");
                      field.onBlur();
                    }, 150);
                  }}
                  autoComplete="off"
                  readOnly={!isOpen}
                  {...rest}
                />
              </InputGroup>

              {/* Dropdown com opções filtradas */}
              {isOpen && (
                <Box
                  position="absolute"
                  top="100%"
                  left={0}
                  right={0}
                  bg="white"
                  borderColor="#D6D6D6"
                  borderWidth={2}
                  borderRadius={10}
                  mt={1}
                  maxH="200px"
                  overflowY="auto"
                  zIndex={"dropdown"}
                  boxShadow="md"
                  color={"chatPrimary"}
                >
                  {filteredItems.length > 0 ? (
                    filteredItems.map((item) => (
                      <Box
                        key={item.value}
                        p={3}
                        cursor="pointer"
                        _hover={{ bgColor: "#f5f5f5" }}
                        onClick={() => {
                          if (multiple) {
                            // Para seleção múltipla, sempre trabalhar com arrays
                            const currentArray = Array.isArray(field.value) ? field.value : (field.value ? [field.value] : []);
                            if (currentArray.includes(item.value)) {
                              // Remove se já estiver selecionado
                              field.onChange(currentArray.filter(val => val !== item.value));
                            } else {
                              // Adiciona se não estiver selecionado
                              field.onChange([...currentArray, item.value]);
                            }
                          } else {
                            // Para seleção única, sempre retornar array (compatibilidade com schemas existentes)
                            field.onChange([item.value]);
                            setIsOpen(false);
                            setSearchTerm("");
                          }
                        }}
                        borderBottom="1px solid #e2e8f0"
                        _last={{ borderBottom: "none" }}
                      >
                        {item.label}
                      </Box>
                    ))
                  ) : (
                    <Box p={3} color="gray.500" textAlign="center">
                      Nenhum item encontrado
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          );
        }}
      />
    </Field>
  );
};

export const InputSelect = forwardRef(InputBase);
