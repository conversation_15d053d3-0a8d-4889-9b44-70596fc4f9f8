import { Button } from "@/components/ui/button";
import {
  DialogActionTrigger,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

type BasicModalProps = {
  placement?: "top" | "bottom" | "center";
  open: boolean;
  setOpen: (open: boolean) => void;
  children?: React.ReactNode;
  title?: string;
  cancelText?: string;
  confirmText?: string;
  deleteText?: string;
  handleDelete?: () => void;
  handleConfirm?: () => void;
  asForm?: boolean;
  handleSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  isSubmitting?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "xs" | "cover" | "full";
  closeOnInteractOutside?: boolean;
  closeOnEscape?: boolean;
};

export default function BasicModal({
  placement,
  open,
  setOpen,
  children,
  title,
  cancelText,
  confirmText,
  deleteText,
  handleDelete,
  asForm,
  isSubmitting,
  handleSubmit,
  handleConfirm,
  size,
  closeOnInteractOutside = false,
  closeOnEscape = false,
}: BasicModalProps) {
  return (
    <DialogRoot
      closeOnEscape={closeOnEscape}
      closeOnInteractOutside={closeOnInteractOutside}
      placement={placement}
      size={size}
      open={open}
      onOpenChange={(e) => setOpen(e.open)}
    >
      <DialogContent
        shadow={"none"}
        bgColor={"chatCardBackground"}
        rounded={"2xl"}
        color={"chatTextColor"}
        as={asForm ? "form" : undefined}
        onSubmit={handleSubmit}
      >
        {title && (
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
        )}
        <DialogBody pb="4">{children}</DialogBody>
        <DialogFooter>
          {cancelText && (
            <DialogActionTrigger asChild>
              <Button
                size={"md"}
                fontWeight="700"
                bgColor="gray.300"
                color="black"
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                }}
                loading={isSubmitting}
              >
                {cancelText}
              </Button>
            </DialogActionTrigger>
          )}
          {confirmText && (
            <Button
              type={asForm ? "submit" : "button"}
              onClick={handleConfirm}
              size={"md"}
              fontWeight="700"
              bgColor="chatPrimary"
              color="white"
              transitionDuration={"0.2s"}
              _hover={{
                color: "chatPrimary",
                bgColor: "transparent",
                borderColor: "chatPrimary",
              }}
              loading={isSubmitting}
            >
              {confirmText}
            </Button>
          )}
          {deleteText && (
            <Button
              type="button"
              onClick={handleDelete}
              size={"md"}
              fontWeight="700"
              bgColor="red.500"
              color="white"
              transitionDuration={"0.2s"}
              _hover={{
                color: "red.500",
                bgColor: "transparent",
                borderColor: "red.500",
              }}
              loading={isSubmitting}
            >
              {deleteText}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
}
