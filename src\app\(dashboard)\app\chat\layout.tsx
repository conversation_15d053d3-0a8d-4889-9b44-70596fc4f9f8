"use client";
import { AbsoluteCenter, Box, Center, Flex, Grid, GridItem, Text } from "@chakra-ui/react";
import Conversations from "./components/conversations";
import { useParams } from "next/navigation";
import { SessionProvider } from "@/providers/SessionProvider";
import { SocketProvider } from "@/providers/SocketProvider";
import SessionInfosTabs from "./components/sessions-infos/tabs";
import SessionsButtons from "./components/sessions-buttons";
import InfoText from "./components/info-text";
import SelectChatComponent from "@/components/global/chat/select-chat";

type LayoutChatAppProps = {
  children: React.ReactNode;
};

export default function LayoutChatApp({ children }: LayoutChatAppProps) {
  const params = useParams();
  const secureId = params.id as string | undefined;

  return (
    <Flex flex={1} rounded={"2xl"} h="100%" w="100%" overflow="hidden" mt={{
      base: 8,
      md: 0
    }}>
      <SocketProvider>
        <SessionProvider sessionId={secureId}>
          <Grid
            h="100%"
            w="100%"
            templateColumns={{
              base: "1fr",
              md: "minmax(250px, 1fr) minmax(400px, 2fr) minmax(250px, 1fr)"
            }}
            gap={3}
            overflow="hidden"
          >
            {/* Conversations - Coluna da esquerda */}
            <GridItem h="100%" display={{ base: "block", md: "grid" }}>
              <Flex
                h="100%"
                bgColor="chatCardBackground"
                rounded={"xl"}
                p={4}
                overflow="hidden"
                direction="column"
              >
                <Conversations activeConversationId={secureId} />
              </Flex>
            </GridItem>

            {secureId ? (
              <>
                {/* Coluna central - Oculta no mobile */}
                <GridItem h="100%" hideBelow={"md"}>
                  <Grid
                    h="100%"
                    templateRows="auto 1fr"
                    gap={3}
                  >
                    {/* Info Text - Parte superior */}
                    <GridItem>
                      <Flex
                        // bgColor="chatCardBackground"
                        rounded={"xl"}
                        px={6}
                        overflow="hidden"
                        w="100%"
                      >
                        {secureId && (
                          <InfoText sessionId={secureId} />
                        )}
                      </Flex>
                    </GridItem>

                    {/* Children - Área principal */}
                    <GridItem overflow="hidden">
                      <Box
                        bgColor={secureId ? "chatCardBackground" : "transparent"}
                        h="100%"
                        w="100%"
                        rounded={"xl"}
                        p={4}
                        overflow="hidden"
                      >
                        {children}
                      </Box>
                    </GridItem>
                  </Grid>
                </GridItem>

                {/* Coluna da direita - Session Info - Oculta no mobile */}
                <GridItem h="100%" hideBelow={"md"}>
                  <Grid
                    h="100%"
                    templateRows="auto 1fr"
                    gap={3}
                  >
                    {/* Session Buttons - Parte superior */}
                    <GridItem>
                      <Flex
                        // bgColor="chatCardBackground"
                        rounded={"xl"}
                        px={4}
                        overflow="hidden"
                        w="100%"
                      >
                        {secureId && (
                          <SessionsButtons chatSessionSecureId={secureId} />
                        )}
                      </Flex>
                    </GridItem>

                    {/* Session Info Tabs - Parte inferior */}
                    <GridItem overflow="hidden">
                      <Flex
                        h="100%"
                        bgColor={secureId ? "chatCardBackground" : "transparent"}
                        rounded={"xl"}
                        p={4}
                        overflow="hidden"
                        direction="column"
                      >
                        {secureId && (
                          <SessionInfosTabs />
                        )}
                      </Flex>
                    </GridItem>
                  </Grid>
                </GridItem>
              </>
            ) : (
              <>
                <GridItem h="100%" colSpan={2} hideBelow={"md"}>
                  <SelectChatComponent />
                </GridItem>
              </>
            )}


          </Grid>
        </SessionProvider>
      </SocketProvider>
    </Flex>
  );
}
