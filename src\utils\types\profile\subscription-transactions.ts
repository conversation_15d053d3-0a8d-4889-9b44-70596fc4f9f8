import { SubscriptionStatus } from "../subscription/subscription-status";
import { SubscriptionType } from "../subscription/subscription-type";
import { TransactionStatus } from "../transactions/transaction-status";

type Plan = {
  secureId: string;
  name: string;
  slug: string;
  price: string;
  description: string;
  details: string;
  knowledgeBaseLimit: number;
  attendantsLimit: number;
  chatbotsLimit: number;
  iaMessagesLimit: number;
  whatsappNumberLimit: number;
}
type Subscription = {
  secureId: string;
  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;
  plan: Plan;
}
type Transactions = {
  secureId: string;
  amount: string;
  status: TransactionStatus;
  payedAt: string;
}

export type ProfileSubscriptionAndTransactions = {
  subscription: Subscription | null;
  transactions: Transactions[] | null;
}