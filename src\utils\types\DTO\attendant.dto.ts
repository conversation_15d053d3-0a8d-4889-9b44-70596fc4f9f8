import { MetaDTO } from "./meta.dto";

export type ListAttendantInputDTO = {
  meta: MetaDTO;
  data: {
    secureId: string;
    name: string;
    email: string;
    cellPhone: string;
    isActive: boolean;
    currentAccount: {
      secureId: string;
      isActive: boolean;
    };
    createdAt: string;
    updatedAt: string;
  }[];
};

export type AttendantInputDTO = {
  secureId: string;
  name: string;
  email: string;
  cellPhone: string;
  cpf: string;
  isActive: boolean;
  currentAccount: {
    secureId: string;
    participatesInRotation: boolean,
    isActive: boolean;
  };
  permissions?: string[];
  hasAllPermissions: boolean;
  createdAt: string;
  updatedAt: string;
};
