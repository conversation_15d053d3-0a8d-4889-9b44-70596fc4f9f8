import { SubscriptionStatus } from "@/utils/types/subscription/subscription-status";
import { TransactionStatus } from "@/utils/types/transactions/transaction-status";
import { Badge, Box, Separator, Text, VStack } from "@chakra-ui/react";

type CardAccountTransactionPros = {
  transactionAmount: number,
  transactionStatus: TransactionStatus,
  transactionPayedAt: string
}

export default function CardAccountTransaction({
  transactionAmount,
  transactionStatus,
  transactionPayedAt
}: CardAccountTransactionPros) {
  function mapTransactionStatus(status: string): { label: string, color: string } {
    switch (status) {
      case "new":
        return { label: "Nova", color: "white" };
      case "waiting":
        return { label: "Aguardando", color: "yellow.500" };
      case "identified":
        return { label: "Identificada", color: "dodgerblue" };
      case "approved":
        return { label: "Aprovada", color: "green.500" };
      case "paid":
        return { label: "Paga", color: "green.500" };
      case "unpaid":
        return { label: "Não Paga", color: "red" };
      case "refunded":
        return { label: "Reembolsada", color: "gray" };
      case "contested":
        return { label: "Contestada", color: "gray" };
      case "canceled":
        return { label: "Cancelada", color: "red" };
      case "settled":
        return { label: "Marcar como Pago", color: "white" };
      case "link":
        return { label: "Link", color: "dodgerblue" };
      case "expired":
        return { label: "Expirada", color: "gray" };
      default:
        return { label: "Desconhecido", color: "gray" };
    }
  }

  return (
    <Box
      p={4}
      borderRadius={20}
      bg="background"
      fontSize={"md"}
    >
      <VStack align="start" gap={2}>
        <Text fontSize="lg">
          Valor: <strong>{transactionAmount}{" "}</strong>        
        </Text>
        <Separator/>
        <Text>
          Status:{" "} 
          <Badge color={mapTransactionStatus(transactionStatus).color} fontSize={"md"}>
            {mapTransactionStatus(transactionStatus).label}
          </Badge>
        </Text>
        <Text>
          Data do Pagamento:{" "}
          <strong>
            {transactionPayedAt ? new Date(transactionPayedAt).toLocaleString() : "Pendente"}
          </strong>
        </Text>
      </VStack>
      {/* <Separator my={4} />
      <Text fontSize="sm" color="gray.600">
        ID da Transação: <strong>{transactionGatewayId}</strong>
      </Text> */}
    </Box>
  )
}