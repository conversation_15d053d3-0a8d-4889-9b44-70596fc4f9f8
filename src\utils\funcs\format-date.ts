import { format, isToday, isThisWeek, isThis<PERSON><PERSON>h, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Formata uma data baseada em regras específicas:
 * - Se for hoje: retorna apenas a hora (HH:mm)
 * - Se for esta semana: retorna o dia da semana por extenso
 * - Se for este mês ou anterior: retorna a data completa (dd/mm/yyyy)
 * 
 * @param dateString - String de data ISO ou objeto Date
 * @returns String formatada de acordo com as regras
 */
export function formatMessageDate(dateString: string | Date): string {
  const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
  
  if (isToday(date)) {
    return format(date, 'HH:mm');
  } else if (isThisWeek(date, { weekStartsOn: 1 })) {
    // Primeira letra maiúscula para o dia da semana
    const weekDay = format(date, 'EEEE', { locale: ptBR });
    return weekDay.charAt(0).toUpperCase() + weekDay.slice(1);
  } else {
    return format(date, 'dd/MM/yyyy');
  }
}
