import CardAccountTransaction from "@/components/cards/card-account-transaction";
import Pagination from "@/components/global/pagination/pagination";
import { useGetAllAccountsTransactions } from "@/hook/accounts/useGetAccountTransactions";
import { Flex, Heading, Text } from "@chakra-ui/react";
import { useState } from "react";

type AccountDataTabProps = {
  accountSecureId: string
}

export default function AccountTransactionTab({accountSecureId}: AccountDataTabProps) {
  const [page, setPage] = useState(1);
  const { data: accountTransactionData } = useGetAllAccountsTransactions(page, accountSecureId as string);
  return (
    <Flex direction="column" gap={4} w="100%">
      <Heading size="md">Transações da Conta</Heading>
      <Text fontSize="sm" color="gray.400">
        Total de transações: {accountTransactionData?.meta.totalItems}
      </Text>

      {accountTransactionData?.data.map((transaction) => (
        <CardAccountTransaction
          key={transaction.secureId}
          transactionAmount={transaction.amount}
          transactionStatus={transaction.status}
          transactionPayedAt={transaction.payedAt}
        />
      ))}
      {accountTransactionData &&
        <Pagination
          totalItems={accountTransactionData.meta.totalItems}
          itemsPerPage={accountTransactionData.meta.itemsPerPage}
          page={page}
          setPage={setPage}
          alignSelf={"flex-end"}
          color="white"
          hoverColor="backgroundCard"
        />
      }
    </Flex>
  )
}