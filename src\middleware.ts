import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { WithSSRAuth } from "./utils/validator/WithSSRAuth";
import { validateToken } from "./utils/funcs/validate-token";

export async function middleware(request: NextRequest) {
  const cookie = request.cookies.get("__PlyrChat_Token")?.value;
  if (!cookie) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  // Validate token before proceeding
  const { isValid, errorMessage } = validateToken(cookie);
  if (!isValid) {
    console.log(`Middleware token validation failed: ${errorMessage}`);
    const response = NextResponse.redirect(new URL("/", request.url));
    response.cookies.set("__PlyrChat_Token", "", {
      path: "/",
      expires: new Date(0),
    });
    return response;
  }

  const isApp = request.url.includes("/app");
  if (isApp) {
    const isAuthenticated = await With<PERSON><PERSON><PERSON>(
      { roles: ["APP", "ATTENDANT"] },
      cookie
    );

    if (!isAuthenticated) {
      const response = NextResponse.redirect(new URL("/", request.url));
      response.cookies.set("__PlyrChat_Token", "", {
        path: "/",
        expires: new Date(0),
      });
      return response;
    }

    return NextResponse.next();
  }

  const isAuthenticated = await WithSSRAuth({ roles: ["BACKOFFICE"] }, cookie);

  if (!isAuthenticated) {
    const response = NextResponse.redirect(new URL("/", request.url));
    response.cookies.set("__PlyrChat_Token", "", {
      path: "/",
      expires: new Date(0),
    });
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/backoffice/:path*", "/app/:path*"],
};
