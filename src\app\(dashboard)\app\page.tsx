"use client";
import { Flex } from "@chakra-ui/react";
import CardDashboard from "../../../components/cards/card-app-dashboard";
import { useAuthContext } from "@/providers/AuthProvider";
import HasPermission from "@/utils/funcs/has-permission";
import { DASHBOARDPERMISSIONS } from "@/utils/types/permissions/all-attendant-permissions";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Home() {
  const { user } = useAuthContext();
  const router = useRouter();
  const role = user!.activeAccount.roleSlug;

  useEffect(() => {
    if (
      (role === "APP" || role === "ATTENDANT" || role === "MASTER") &&
      HasPermission(DASHBOARDPERMISSIONS.VIEW, user)
    ) {
      router.push("/app/dashboard");
    }
  }, [role, user, router]);

  return (
    <Flex flex={1} rounded={"2xl"} bgColor={"chatCardBackground"}>
      <Flex flex={1} h="100%" w="100%">
        <Flex
          flex={1}
          flexDir={"row"}
          flexWrap={"wrap"}
          mt={{ md: 0, base: 20 }}
          gap={4}
          p={5}
          alignContent="flex-start"
          w="100%"
          justifyContent={"space-evenly"}
        ></Flex>
      </Flex>
    </Flex>
  );
}
