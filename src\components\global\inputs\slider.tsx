import { HStack, Stack, Text } from "@chakra-ui/react";
import { Controller } from "react-hook-form";
import { Slider } from "@/components/ui/slider";

interface SliderProps {
  name: string;
  label: string;
  labelColor?: string;
  disabled?: boolean;
  labelSize?: string;
  minValue?: number;
  maxValue?: number;
  valueMarks?: { value: number; label: string }[];
  onValueChange?: (value: number[]) => void; // Tornando opcional
  value?: number[]; // Tornando opcional
  optionalText?: React.ReactNode;
  control: any;
}

const SliderBase = ({
  name,
  label,
  optionalText,
  labelColor,
  disabled,
  labelSize,
  control,
  valueMarks,
  maxValue = 15,
  minValue = 5,
  onValueChange,
  value,
}: SliderProps) => {
  return (
    <Stack gap="2" align="flex-start" mb={4} w={"100%"}>
      <Stack direction="column" gap="2" w={"100%"}>
        <HStack>
          <Text
            fontWeight={"500"}
            fontSize={labelSize}
            color={labelColor}
            textAlign={"center"}
          >
            {label}
          </Text>
          {optionalText}
        </HStack>

        <Controller
          name={name}
          control={control}
          defaultValue={value || [1]} // Valor inicial
          render={({ field }) => (
            <Slider
              id={field.name}
              name={field.name}
              w={"100%"}
              value={[field.value]}
              onValueChange={(val) => {
                field.onChange(val.value); // Atualiza o valor do campo
                if (onValueChange) {
                  onValueChange(val.value); // Chama a função de callback, se existir
                }
              }}
              min={minValue}
              max={maxValue}
              marks={
                valueMarks || [
                  { value: 5, label: "5" },
                  { value: 10, label: "10" },
                  { value: 15, label: "15" },
                ]
              }
              step={1}
              disabled={disabled}
            />
          )}
        />
      </Stack>
    </Stack>
  );
};

export const CustomSlider = SliderBase;
