import { Box, Flex, Grid, GridItem, HStack, VStack } from "@chakra-ui/react";
import HeaderTab from "@/components/tabs/header";
import { Input } from "@/components/global/inputs/input";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import { Tooltip } from "@/components/ui/tooltip";
import { LuCircleHelp } from "react-icons/lu";
import { CustomSlider } from "@/components/global/inputs/slider";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useState } from "react";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { InputSelect } from "@/components/global/inputs/input-select";
import { useRouter } from "next/navigation";

const LeadCaptureTabSchema = yup.object().shape({
  leadCaptureMessage: yup
    .string()
    .required("Mensagem para captura de lead é obrigatória"),
  leadCaptureThankYouMessage: yup
    .string()
    .required("Mensagem de agradecimento é obrigatória"),
  leadTriggerMessageLimit: yup
    .number()
    .required(
      "Número de mensagens para começar a captura de lead é obrigatório"
    ),
  collectInfos: yup
    .array()
    .of(yup.string())
    .required("Informações para coleta de dados são obrigatórias"),
});

type LeadCaptureTabFormData = yup.InferType<typeof LeadCaptureTabSchema>;

type LeadCaptureTabProps = {
  chatBotSecureId: string;
};

export default function LeadCaptureTab({
  chatBotSecureId,
}: LeadCaptureTabProps) {
  const route = useRouter();
  const [msgLimit, setMsgLimit] = useState([5]);
  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<LeadCaptureTabFormData>({
    resolver: yupResolver(LeadCaptureTabSchema),
  });

  const handleUpdate = async (data: LeadCaptureTabFormData) => {
    const { collectInfos, ...restData } = data;
    const collectInfosObject = Object.fromEntries(
      collectInfos.map((item) => [item, true])
    );

    await api.put(`/chatbots/${chatBotSecureId}`, {
      leadCaptureJson: {
        ...collectInfosObject,
      },
      ...restData,
    });

    toaster.success({
      description: "Alterações salvas com sucesso!",
      title: "Sucesso",
    });
  };

  return (
    <>
      <Flex
        height={"100%"}
        flex={1}
        p={"5"}
        bgColor={"chatCardBackground"}
        rounded={"2xl"}
        border={"none"}
      >
        <VStack flex={1} alignItems={"flex-start"} w={"100%"} gap={"5"} as={'form'} onSubmit={handleSubmit(handleUpdate)}>
          <HeaderTab
            buttonTitle="Salvar Alterações"
            title="Geral"
            hrefBack="/app/config#chatbots"
            isSubmit={isSubmitting}
          />
          <VStack
            flex={1}
            w={"100%"}
            gap={"8"}
            alignItems={"start"}
            justifyContent={"start"}
          >
            <HStack w={"100%"} gap={"8"} justifyContent={"space-between"}>
              <InputTextArea
                {...register("leadCaptureMessage")}
                height={"100px"}
                label="Mensagem de captura de lead."
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.leadCaptureMessage}
                optionalText={
                  <Box pl={1} _hover={{ cursor: "help" }}>
                    <Tooltip
                      showArrow
                      content="É o texto que o chatbot usa para solicitar dados do cliente, como nome, e-mail ou telefone. Essa mensagem ajuda a iniciar um relacionamento com quem demonstra interesse no seu produto, serviço ou conteúdo, permitindo contatos futuros e ações personalizadas."
                    >
                      <LuCircleHelp color="#FD2264" />
                    </Tooltip>
                  </Box>
                }
              />
              <InputTextArea
                {...register("leadCaptureThankYouMessage")}
                height={"100px"}
                label="Mensagem de Agradecimento."
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.leadCaptureThankYouMessage}
                optionalText={
                  <Box pl={1} _hover={{ cursor: "help" }}>
                    <Tooltip
                      showArrow
                      content="É a mensagem que o chatbot exibe após o cliente fornecer seus dados de contato. Ela serve para agradecer o interesse, reforçar a confiança e informar que a empresa entrará em contato em breve, criando uma experiência positiva para o usuário."
                    >
                      <LuCircleHelp color="#FD2264" />
                    </Tooltip>
                  </Box>
                }
              />
            </HStack>
            <HStack
              w={"100%"}
              gap={8}
              justifyContent={"space-between"}
              alignItems={"start"}
            >
              <CustomSlider
                control={control}
                label="Qtd. de mensagens."
                labelSize="sm"
                onValueChange={setMsgLimit}
                value={msgLimit}
                labelColor="chatTextColor"
                {...register("leadTriggerMessageLimit")}
                optionalText={
                  <Box pl={1} _hover={{ cursor: "help" }}>
                    <Tooltip
                      showArrow
                      content="Quantidade de interações necessárias para avaliar e determinar o momento ideal de enviar a mensagem de captura do lead. Caso o lead ainda não tenha demonstrado interesse, a conversa continuará fluindo até que a análise indique uma mudança e o chatbot solicite os dados do cliente."
                    >
                      <LuCircleHelp color="#FD2264" />
                    </Tooltip>
                  </Box>
                }
              />
              <InputSelect
                multiple
                control={control}
                {...register("collectInfos")}
                label="Informações para coleta"
                labelColor={"chatTextColor"}
                placeholder="(Selecione)"
                rounded={"2xl"}
                error={errors.collectInfos as any}
                itensList={[
                  {
                    value: "collectName",
                    label: "Nome",
                  },
                  {
                    value: "collectEmail",
                    label: "E-mail",
                  },
                  {
                    value: "collectPhone",
                    label: "Telefone",
                  },
                  {
                    value: "collectCPF",
                    label: "CPF",
                  },
                ]}
              />
            </HStack>
          </VStack>
        </VStack>
      </Flex>
    </>
  );
}
