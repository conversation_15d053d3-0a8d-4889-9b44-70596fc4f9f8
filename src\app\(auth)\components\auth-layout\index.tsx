"use client";
import { Flex } from "@chakra-ui/react";

type AuthLayoutContainerProps = {
  children: React.ReactNode;
};

export default function AuthLayoutContainer({
  children,
}: AuthLayoutContainerProps) {
  return (
    <Flex
      w={"100vw"}
      minH={"100vh"}
      style={{
        background:
          "linear-gradient(243deg, #FF004C 0%, #FD2264 47.5%, #97143C 100%)",
      }}
    >
      {children}
    </Flex>
  );
}
