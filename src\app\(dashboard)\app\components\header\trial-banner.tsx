import { Button } from "@/components/ui/button";
import { AbsoluteCenter, Box, Flex, ProgressCircle, Text } from "@chakra-ui/react";
import { useRouter } from "next/navigation";

type TrialBannerProps = {
  daysLeft?: number;
};

export default function TrialBanner({ daysLeft = 7 }: TrialBannerProps) {
  const router = useRouter();

  const handleSubscribe = () => {
    router.push("/app/choose-plans");
  };

  return (
    <Flex
      py={2}
      px={4}
      alignItems="center"
      justifyContent="center"
      w="auto"
      gap={2}
    >
      {daysLeft > 0 ? (
        <>
          <Box
            position="relative"
            width="40px"
            height="40px"
            boxShadow="0px 2px 4px rgba(0, 0, 0, 0.1)"
            borderRadius="full"
          >
            <ProgressCircle.Root
              value={daysLeft * 100 / 7}
              size="md"
            >
              <ProgressCircle.Circle css={{ "--thickness": "2px" }}>
                <ProgressCircle.Track stroke="#E0E0E0" />
                <ProgressCircle.Range
                  stroke="chatPrimary"
                  strokeLinecap="round"
                />
              </ProgressCircle.Circle>
              <AbsoluteCenter
                color="chatPrimary"
                fontWeight="400"
                fontSize="md"
              >
                {daysLeft}
              </AbsoluteCenter>
            </ProgressCircle.Root>
          </Box>
          <Text
            color="#5E5E5E"
            pl={2}
            fontSize="sm"
            fontWeight="400"
            w={"40%"}
            lineBreak={"normal"}
          >
            Aproveite seus {daysLeft} dias grátis para testar 100% do Plyr.Chat
          </Text>
        </>
      ) : (
        <>
          <Text
            color="#5E5E5E"
            pl={2}
            fontSize="sm"
            fontWeight="400"
            w={"40%"}
            lineBreak={"normal"}
          >
            Sua avaliação gratuita terminou. Assine agora e continue usando o
            Plyr.Chat
          </Text>
        </>
      )}
      <Button
        onClick={handleSubscribe}
        bgColor="chatPrimary"
        color="white"
        borderRadius="full"
        size="md"
        fontWeight="700"
        px={6}
        _hover={{
          transform: "scale(1.05)",
          transitionDuration: "0.2s",
        }}
      >
        Já quero Assinar
      </Button>
    </Flex>
  );
}
