import { Button } from "@/components/ui/button";
import {
  DialogActionTrigger,
  Dialog<PERSON>ody,
  <PERSON>alog<PERSON>ontent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogRoot,
  DialogTitle,
} from "@/components/ui/dialog";

type BasicModalProps = {
  placement?: "top" | "bottom" | "center";
  open: boolean;
  setOpen: (open: boolean) => void;
  children?: React.ReactNode;
  title?: string;
  cancelText?: string;
  confirmText?: string;
  deleteText?: string;
  handleDelete?: () => void;
  handleConfirm?: () => void;
  asForm?: boolean;
  handleSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  isSubmitting?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "xs" | "cover" | "full";
};

export default function BasicBackofficeModal({
  placement,
  open,
  setOpen,
  children,
  title,
  cancelText,
  confirmText,
  deleteText,
  handleDelete,
  asForm,
  isSubmitting,
  handleSubmit,
  handleConfirm,
  size,
}: BasicModalProps) {
  return (
    <DialogRoot
      closeOnEscape={false}
      closeOnInteractOutside={false}
      placement={placement}
      size={size}
      open={open}
      onOpenChange={(e) => setOpen(e.open)}
    >
      <DialogContent
        shadow={"none"}
        bgColor={"backgroundCard"}
        rounded={"2xl"}
        color={"whiter"}
        as={asForm ? "form" : undefined}
        onSubmit={handleSubmit}
      >
        {title && (
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
        )}
        <DialogBody pb="4">{children}</DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button
              size={"md"}
              fontWeight="700"
              bgColor="gray.300"
              color="black"
              transitionDuration={"0.2s"}
              _hover={{
                color: "white",
                bgColor: "transparent",
                borderColor: "white",
              }}
              loading={isSubmitting}
            >
              {cancelText}
            </Button>
          </DialogActionTrigger>
          {confirmText && (
            <Button
              type={asForm ? "submit" : "button"}
              onClick={handleConfirm}
              size={"md"}
              fontWeight="700"
              bgColor="background"
              color="white"
              transitionDuration={"0.2s"}
              _hover={{
                color: "background",
                bgColor: "white",
                borderColor: "background",
              }}
              loading={isSubmitting}
            >
              {confirmText}
            </Button>
          )}
          {deleteText && (
            <Button
              type="button"
              onClick={handleDelete}
              size={"md"}
              fontWeight="700"
              bgColor="red.500"
              color="white"
              transitionDuration={"0.2s"}
              _hover={{
                color: "red.500",
                bgColor: "transparent",
                borderColor: "red.500",
              }}
              loading={isSubmitting}
            >
              Apagar
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
}
