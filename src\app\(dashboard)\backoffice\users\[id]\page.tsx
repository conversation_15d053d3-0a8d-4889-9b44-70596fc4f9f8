"use client"
import AllPermissionCheckbox from "@/components/global/checkbox/all-permissions-checkbox";
import { PermissionsCheckbox } from "@/components/global/checkbox/permissions-checkbox";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { toaster } from "@/components/ui/toaster";
import useBackofficePermissions from "@/hook/permissions/useBackofficePermissions";
import { useGetUserBackoffice } from "@/hook/users/useGetUserBackoffice";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEUSERPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { Button, Flex, Grid, GridItem, Separator, Text } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useController, useForm } from "react-hook-form";
import { IoArrowBackOutline } from "react-icons/io5";
import * as yup from "yup";

const updateUserBackofficeSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  cpf: yup
    .string()
    .required("CPF é obrigatório")
    .test("is-valid-cpf", "CPF inválido", (value) => {
      if (!value) return false;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 11;
  }),
  email: yup.string().required("Email é obrigatório"),
  hasAllPermissions: yup.boolean().optional(),
  permissions: yup.array().of(yup.string()).optional(),
});

type UpdateUserBackofficeFormData = yup.InferType<typeof updateUserBackofficeSchema>;


export default function BackofficeUsers() {
  const hasEditPermissions = HasBackofficePermission(BACKOFFICEUSERPERMISSIONS.EDIT)
  const { id: secureId } = useParams();
  const { data: user } = useGetUserBackoffice(secureId as string);
  const { data: permissionsData } = hasEditPermissions ? useBackofficePermissions() : { data: null };
  const [permissionsItems, setPermissionsItems] = useState<
  { label: string; value: string }[]
  >([]);

  useEffect(() => {
    if (permissionsData?.permissions) {
      setPermissionsItems(
        permissionsData.permissions.map((permission) => ({
          label: permission.description,
          value: permission.secureId,
        }))
      );
    }
  }, [permissionsData]);


  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<UpdateUserBackofficeFormData>({
    resolver: yupResolver(updateUserBackofficeSchema),
    defaultValues: {
      name: "",
      cpf: "",
      email: "",
      hasAllPermissions: false,
      permissions: []
    },
  });

  const permissions = useController({
    control,
    name: "permissions",
    defaultValue: user?.permissions || [],
  });

  useEffect(() => {
    if (user) {
      setValue("name", user.name || "");
      setValue("cpf", user.cpf || "");
      setValue("email", user.email || "");
      setValue("hasAllPermissions", user.hasAllPermissions || false);
      setValue("permissions", user.permissions || []);

      permissions.field.onChange(user.permissions || []);
    }
  }, [user, setValue]);


  const updateUser = useMutation({
    mutationFn: async (data: UpdateUserBackofficeFormData) => {
      return await api.put(`/backoffice/users/${user?.secureId}`, {
        permissions: data.hasAllPermissions ? undefined : data.permissions,
        hasAllPermissions: data.hasAllPermissions,
        name: data.name,
        cpf: data.cpf,
        email: data.email,
      });
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Usuário atualizado com sucesso!",
      })
      queryClient.invalidateQueries({
        queryKey: ["users-backoffice"],
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar usuário:", error);
    },
  });
  
  const handleEdit = async (data: UpdateUserBackofficeFormData) => {
    try {
      await updateUser.mutateAsync(data);
    } catch (error) {
      console.error("Erro ao atualizar usuário:", error);
    }
  };

  return (
    <Flex flex={1} h="100%" w="100%" flexDirection="column" marginTop={{ base: "12rem", md: 0 }}>
      <Flex
        as= {"form"}
        flex={1}
        flexWrap={"wrap"}
        p={{ base: 2, md: 5 }}
        m={{ base: 0, md: 5 }}
        gap={4}
        borderRadius={{ base: 0, md: 15 }}
        bgColor="backgroundCard"
        flexDirection="column"
        onSubmit={handleSubmit(handleEdit)}
      >
        <Flex gap={4} marginBottom={4} alignItems={"center"}>
          <Link href="/backoffice/users" passHref>
            <IoArrowBackOutline size={22}/>
          </Link>
          <Text fontSize={{base:"lg", sm:"xl"}} fontWeight="bold">
            Usuário
          </Text>
        </Flex>
        <Grid templateColumns={{base:"1fr", sm:"repeat(2, 1fr)"}} gap={{base: 0, sm:6}}>
          <GridItem>
              <Input
                label="Nome"
                borderRadius={20}
                color="gray.800"
                size={{base:"sm", sm:"md"}}
                {...register("name")}
                error={errors.name}
              />
          </GridItem>

          <GridItem>
              <Input
                label="Email"
                borderRadius={20}
                color="gray.800"
                size={{base:"sm", sm:"md"}}
                {...register("email")}
                error={errors.email}
              />
          </GridItem>

          <GridItem>
            <InputMaskIcon
              label="CPF"
              borderRadius={20}
              mask="999.999.999-99"
              color="gray.800"
              size={{base:"sm", sm:"md"}}
              {...register("cpf")}
              error={errors.cpf}
            />
          </GridItem>
        </Grid>
        <Flex justifyContent="flex-end" gap={4}>
          {hasEditPermissions &&
            <Button
              borderRadius={20}
              size={"md"}
              type="submit"
              fontWeight="700"
              bgColor={"background"}
              transitionDuration={"0.2s"}
              color={"gray.200"}
              _hover={{
                bg: "gray.100",
                color: "background",
              }}
            >
              Salvar
            </Button>
          }
        </Flex>
        <Separator />
        {hasEditPermissions ? (
          <>
            <Text fontSize={{base:"lg", sm:"xl"}} fontWeight="bold">
              Permissões
            </Text>
            <Flex
              flexDirection={"column"}
              width="100%" padding={4}
              bgColor="backgroundCard"
              borderRadius={15}
              gap={4}
            >
              <Text fontSize={"lg"}>Selecione as permissões</Text>
              <AllPermissionCheckbox
                control={control}
                label="Conceder todas as permissões"
                name="hasAllPermissions"
                invalid={!!errors.hasAllPermissions}
              />
              <PermissionsCheckbox
                invalid={!!errors.permissions}
                onValueChange={permissions.field.onChange}
                value={permissions.field.value as string[] | undefined}
                name={permissions.field.name}
                label="Permissões"
                items={permissionsItems}
                hasLegend={false}
              />
              <Button
                borderRadius={20}
                onClick={handleSubmit(handleEdit)}
                alignSelf={"flex-end"}
                size={"md"}
                type="submit"
                fontWeight="700"
                bgColor={"background"}
                transitionDuration={"0.2s"}
                color={"gray.200"}
                _hover={{
                  bg: "gray.100",
                  color: "background",
                }}
              >
                Atualizar Permissões
              </Button>
            </Flex>
          </>
        ) : null}
      </Flex>
    </Flex>
  );
}
