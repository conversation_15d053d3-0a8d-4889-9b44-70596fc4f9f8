import { Link, Tabs, Text } from "@chakra-ui/react";

type TabTriggerProps = {
  value: string;
  icon: React.ReactNode;
  label: string;
};

export const TabTrigger: React.FC<TabTriggerProps> = ({
  value,
  icon,
  label,
}) => {
  const isActive = value === (typeof window !== "undefined" ? window.location.hash.replace("#", "") : "");
  return (
    <Tabs.Trigger
      value={value}
      aria-selected={isActive}
      justifyContent={"flex-start"}
      _selected={{
        bgColor: "chatCardBackground",
        color: "chatPrimary",
        borderRightColor: "chatPrimary",
        shadow: "none",
        borderRadius: 0,
      }}
      width={"100%"}
      border={"1px solid"}
      borderColor={"transparent"}
      pl={0}
      asChild
    >
      <Link unstyled href={`/app/config#${value}`}>
        {icon}
        <Text fontWeight={"normal"}>{label}</Text>
      </Link>
    </Tabs.Trigger>
  );
};
