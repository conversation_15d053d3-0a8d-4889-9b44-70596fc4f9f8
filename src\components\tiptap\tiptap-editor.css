.tiptap-editor:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px var(--chakra-colors-chatTextColor) !important;
}

.tiptap-editor h1 { font-size: 1.875rem; font-weight: bold; margin: 0.5em 0;  }
.tiptap-editor h2 { font-size: 1.5rem; font-weight: bold; margin: 0.5em 0;  }
.tiptap-editor h3 { font-size: 1.25rem; font-weight: bold; margin: 0.5em 0;  }
.tiptap-editor strong { font-weight: bold; }
.tiptap-editor em { font-style: italic; }

/* Fixed list styling */
.tiptap-editor ul, .tiptap-editor ol { 
  padding-left: 1.5em; 
  margin: 0.375em 0; 
}
.tiptap-editor ul { list-style-type: disc; }

.tiptap-editor li { 
  display: list-item;
  position: relative;
}

.tiptap-editor li > p { 
  margin: 0;
  display: inline-block;
}

/* Add proper scroll for editor content */
/* style: "min-height: 100%; height: 100%; background-color: #F8FAFC; padding: 0.5rem 0.75rem; width: 100%; outline: none;", */
.tiptap-editor {
	background-color: #F8FAFC;
	padding: 0.5rem 0.75rem;
	border-radius: 10px;
  overflow: auto !important;
  max-height: calc(100vh - 260px);
  height: 100%;


	overflow-y: auto;
	&::-webkit-scrollbar {
		width: 10px;
		border-top-right-radius: 10px ;
		border-bottom-right-radius: 10px ;
		background-color: #dcdede;
	}
	&::-webkit-scrollbar-track {
		width: 6px;
		margin-bottom: 10px;
		&:hover {
			cursor: pointer;
		}
	}
	&::-webkit-scrollbar-thumb {
		background-color: #A6A6A6;
		border-radius: 24px;
		&:hover {
			background-color: #A6A6A6;
			cursor: pointer;
		}
	}


}

/* Ensure content flows properly */
.tiptap-editor p {
  margin: 0.2em 0;
}
