import CardAccountSubscription from "@/components/cards/card-account-subscription";
import Pagination from "@/components/global/pagination/pagination";
import { Button } from "@/components/ui/button";
import { useGetAllAccountsSubscriptions } from "@/hook/accounts/useGetAccountSubscriptions";
import { Flex, Heading, HStack, Text } from "@chakra-ui/react";
import { useState } from "react";
import ModalAccountCreateSubscriptionBackoffice from "./modal/modal-create-subscription";

type AccountDataTabProps = {
  accountSecureId: string
}

export default function AccountSubscriptionTab({accountSecureId}: AccountDataTabProps) {
  const [openCreateUserModal, setOpenCreateUserModal] = useState(false);
  const [page, setPage] = useState(1);
  const { data: accountSubscriptionData } = useGetAllAccountsSubscriptions(page, accountSecureId as string);
  
  return (
    <Flex direction="column" gap={4} w="100%">
      <Heading size="md">Assinaturas da Conta</Heading>
      <HStack justifyContent={"space-between"}>
        <Text fontSize="sm" color="gray.400">
          Total de assinaturas: {accountSubscriptionData?.meta.totalItems}
        </Text>
        <Button
          size="sm"
          color={"white"}
          bg={"chatPrimary"}
          borderRadius={"full"}
          colorScheme={"green"}
          _hover={{
            transition: "0.3s",
            bg: "gray.100",
            color: "chatPrimary",
          }}
          onClick={() => setOpenCreateUserModal(true)}
        >
          Criar Assinatura
        </Button>
      </HStack>

      {accountSubscriptionData?.data.map((subscription) => (
        <CardAccountSubscription
          key={subscription.secureId}
          subscriptionSecureId={subscription.secureId}
          subscriptionPlan={subscription.plan.name}
          subscriptionCycle={subscription.cycle}
          subscriptionStatus={subscription.status}
          subscriptionType={subscription.type}
          subscriptionStartsAt={subscription.startsAt}
          subscriptionEndsAt={subscription.endsAt}
          gatewayId={subscription.gatewaySubscriptionId}
        />
      ))}
      {accountSubscriptionData &&
        <Pagination
          totalItems={accountSubscriptionData.meta.totalItems}
          itemsPerPage={accountSubscriptionData.meta.itemsPerPage}
          page={page}
          setPage={setPage}
          alignSelf={"flex-end"}
          color="white"
          hoverColor="backgroundCard"
        />
      }
      <ModalAccountCreateSubscriptionBackoffice
        openModal={openCreateUserModal}
        setOpenModal={setOpenCreateUserModal}
        accountSecureId={accountSecureId}
      />
    </Flex>
  )
}
