import { Input } from "@/components/global/inputs/input";
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  HStack,
  Text,
  VStack,
} from "@chakra-ui/react";
import HeaderTab from "@/components/tabs/header";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { SwitchLabel } from "@/components/global/inputs/switch";
import { Dispatch, SetStateAction, useState } from "react";
import BasicModal from "@/components/global/modal/basic-modal";
import { LuCircleHelp, LuTrash2 } from "react-icons/lu";
import { queryClient } from "@/services/queryClient";
import { useRouter } from "next/navigation";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import { Tooltip } from "@/components/ui/tooltip";
import { CustomSlider } from "@/components/global/inputs/slider";
import StreamableChat from "@/components/chat-chatbot/streamable-chat";

const GeneralTabSchema = yup.object().shape({
  name: yup.string().required("Nome do chat bot é obrigatório"),
  isAI: yup.boolean().required("Tipo de chat bot é obrigatório"),
  isLeadCaptureActive: yup
    .boolean()
    .required("A Captura de lead no chatbot deve ser informada!"),
});

type GeneralTabFormData = yup.InferType<typeof GeneralTabSchema>;

type GeneralTabProps = {
  secureId: string;
  isAi: boolean;
  isLeadCaptureActive: boolean;
  chatBotName: string;
  setIsLeadCapture: Dispatch<SetStateAction<boolean>>;
};

export default function GeneralTab({
  secureId,
  isAi,
  chatBotName,
  isLeadCaptureActive,
  setIsLeadCapture,
}: GeneralTabProps) {
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const [isLeadCaptureActiveState, setIsLeadCaptureActiveState] =
    useState(isLeadCaptureActive);
  const route = useRouter();
  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<GeneralTabFormData>({
    resolver: yupResolver(GeneralTabSchema),
    defaultValues: {
      isAI: isAi,
      name: chatBotName,
      isLeadCaptureActive: isLeadCaptureActive,
    },
  });

  const handleUpdate = async (data: GeneralTabFormData) => {
    await api.put(`/chatbots/${secureId}`, {
      ...data,
    });
    toaster.success({
      description: secureId
        ? "Alterações salvas"
        : "ChatBot criado com sucesso",
      title: "Sucesso",
    });
    setIsLeadCapture(data.isLeadCaptureActive);
  };

  const handleDeleteWebchat = async () => {
    try {
      await api.delete(`/chatbots/${secureId}`);
      toaster.success({
        title: "Sucesso",
        description: "ChatBot deletado com sucesso",
      });
      setOpenModalDelete(false);
      route.push("/app/config");
      queryClient.invalidateQueries({
        queryKey: ["list-chat-bots"],
      });
    } catch (error) {}
  };

  return (
    <>
      <Flex
        height={"100%"}
        bgColor={"chatCardBackground"}
        p={"20px"}
        rounded={"2xl"}
        border={"none"}
      >
        <VStack flex={1} alignItems={"flex-start"}>
          <HeaderTab
            buttonTitle="Salvar Alterações"
            title="Geral"
            hrefBack="/app/config#chatbots"
            handleSubmit={handleSubmit(handleUpdate)}
            newButton={
              <Button
                w={"100%"}
                borderRadius={20}
                size={"md"}
                fontWeight="700"
                bgColor="gray.300"
                color="black"
                // loading={isSubmit}
                onClick={() => setOpenModalDelete(true)}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
              >
                <LuTrash2 />
              </Button>
            }
            isSubmit={isSubmitting}
          />
          <Grid templateColumns="repeat(5, 1fr)" w={"100%"}>
            <GridItem colSpan={{ base: 2, "2xl": 3 }}>
              <VStack
                flex={1}
                gap={8}
                h={"100%"}
                alignItems={"start"}
                justifyContent={"start"}
              >
                <Flex w={"100%"}>
                  <Input
                    {...register("name")}
                    label="Nome do Chatbot"
                    labelColor={"chatTextColor"}
                    rounded={"2xl"}
                    color={"#84767A"}
                    error={errors.name}
                  />
                </Flex>
                <Flex w={"100%"} justifyContent={"start"} gap={8}>
                  <SwitchLabel
                    control={control}
                    label="Inteligência Artificial"
                    labelColor={"chatTextColor"}
                    labelSize="sm"
                    disabled={true}
                    checkedValue={isAi}
                    {...register("isAI")}
                    onCheckedValue={() => {}}
                    onChange={() => {}}
                  />
                  {isAi && (
                    <SwitchLabel
                      control={control}
                      label="Captura de Lead"
                      labelColor={"chatTextColor"}
                      labelSize="sm"
                      checkedValue={isLeadCaptureActiveState}
                      {...register("isLeadCaptureActive")}
                      onCheckedValue={setIsLeadCaptureActiveState}
                      onChange={() => {}}
                    />
                  )}
                </Flex>
              </VStack>
            </GridItem>
            <GridItem colSpan={{ base: 3, "2xl": 2 }} ml={"12"}>
              <Text
                color={"chatTextColor"}
                fontSize={"lg"}
                textAlign={"center"}
                m={2}
              >
                Chat para Teste
              </Text>
              <StreamableChat chatbotSecureId={secureId} />
            </GridItem>
          </Grid>
        </VStack>
      </Flex>
      <BasicModal
        open={openModalDelete}
        size="md"
        setOpen={setOpenModalDelete}
        cancelText="Cancelar"
        handleDelete={handleDeleteWebchat}
        deleteText="Apagar"
        placement="center"
        children={
          <VStack gap={5} m={5}>
            <Text fontSize={"xl"} fontWeight={"bold"} color="red.500">
              ATENÇÃO
            </Text>
            <Text fontSize={"md"} fontWeight={"bold"}>
              Tem certeza que deseja apagar este chatbot?
            </Text>
          </VStack>
        }
      />
    </>
  );
}
