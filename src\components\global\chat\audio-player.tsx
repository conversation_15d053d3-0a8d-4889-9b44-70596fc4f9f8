import { Box, HStack, Icon, IconButton, Text } from "@chakra-ui/react";
import { useEffect, useRef, useState } from "react";
import { FaPlay, FaPause } from "react-icons/fa";
import { Slider } from "../../ui/slider";

interface AudioPlayerProps {
  url: string;
}

export default function AudioPlayer({ url }: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const setAudioData = () => {
      setDuration(audio.duration);
    };

    const setAudioTime = () => {
      setCurrentTime(audio.currentTime);
    };

    // Add event listeners
    audio.addEventListener('loadeddata', setAudioData);
    audio.addEventListener('timeupdate', setAudioTime);
    audio.addEventListener('ended', () => setIsPlaying(false));

    // Cleanup
    return () => {
      audio.removeEventListener('loadeddata', setAudioData);
      audio.removeEventListener('timeupdate', setAudioTime);
      audio.removeEventListener('ended', () => setIsPlaying(false));
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSliderChange = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = value;
    setCurrentTime(value);
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) return "0:00";
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  return (
    <Box bg="white" pt={2} px={3} borderRadius="md"  w={'60'}>
      <audio ref={audioRef} src={url} preload="metadata" />
      
      <HStack gap={4}>
        <IconButton
          aria-label={isPlaying ? "Pause" : "Play"}
          onClick={togglePlay}
          colorScheme="gray"
          bgColor="#FD2264"
          color="white"
          size={"2xs"}
          rounded={"full"}
        >
          <Icon size={"xs"}>
            {isPlaying ? <FaPause/> : <FaPlay/>}
          </Icon>
        </IconButton>
        
        <Box flex="1" >
          <Slider
            aria-label={["audio-progress"]}
            value={[currentTime]}
            max={duration || 100}
            onValueChange={(val) => handleSliderChange(val.value[0])}
          />

          <HStack justifyContent="space-between" width="100%">
            <Text fontSize="xs" color="chatTextColor">
              {formatTime(currentTime)}
            </Text>
            <Text fontSize="xs" color="chatTextColor">
              {formatTime(duration)}
            </Text>
          </HStack>
        </Box>
      </HStack>
    </Box>
  );
}
