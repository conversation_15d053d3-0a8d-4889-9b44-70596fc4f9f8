import { api } from "@/services/api";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { useQuery } from "@tanstack/react-query";

type UseBackofficeChatbotDetailProps = {
  secureId: string;
  page: number;
};

async function getBackofficeChatbotDetail({ secureId, page }: UseBackofficeChatbotDetailProps) {
  const { data } = await api.get<BackofficeChatbotDetailDto>(
    `/backoffice/chatbots/${secureId}`,
    {
      params: {
        page: page,
        limit: 50,
      }
    }
  );

  return data;
}

export default function useBackofficeChatbotDetail({ secureId, page }: UseBackofficeChatbotDetailProps) {
  return useQuery({
    queryKey: ["backoffice-chatbot-detail", secureId, page],

    queryFn: async () => await getBackofficeChatbotDetail({ secureId, page }),

    enabled: !!secureId && typeof page === 'number',
    refetchInterval: 60000, // Refetch a cada 60 segundos
  });
}