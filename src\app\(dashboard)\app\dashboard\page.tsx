"use client";
import CardDashboard from "@/components/cards/card-app-dashboard";
import useAppDashboard from "@/hook/app-dashboard/useAppDashboard";
import { Flex, Grid, Spinner } from "@chakra-ui/react";
import { LuBotMessageSquare, LuCircleCheckBig, LuClock, LuMessageSquare, LuMessageSquareLock, LuMessageSquareMore, LuMessageSquareText, LuMessagesSquare, LuUsers } from "react-icons/lu";

export default function Home() {
  const { data, isLoading } = useAppDashboard();
  return (
    <Flex flex={1} rounded={"2xl"} bgColor={"chatCardBackground"} p={{ base: 4, md: 6 }}>
      <Flex
        flex={1}
        direction="column"
        w="100%"
        mt={{ base: 8, md: 0 }} // Changed from base: 20
      >
        <Flex mb={4}>
          <Flex
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="chatPrimary"
          >
            Dashboard
          </Flex>
        </Flex>

        {isLoading || !data ? (
          <Flex justify="center" align="center" w="100%" h="200px">
            <Spinner color={"chatPrimary"} size="xl" />
          </Flex>
        ) : (
          <Grid
            templateColumns={{
              base: "1fr",
              sm: "repeat(2, 1fr)",
              lg: "repeat(3, 1fr)",
              xl: "repeat(4, 1fr)",
            }}
            gap={{ base: 4, md: 6 }}
            w="100%"
            autoRows="minmax(150px, auto)"
            maxH={{ base: "calc(100vh - 150px)", sm: "auto" }}
            overflowY={{ base: "auto", sm: "visible" }}
            pr={{ base: 2, sm: 0 }} // Add padding for scrollbar on mobile
          >
            <CardDashboard
              icon={<LuMessageSquare size={30} />}
              title="Total de conversas"
              description="Quantidade de conversas iniciadas"
              value={data?.chats.quantity || 0}
              key={1}
            />
            <CardDashboard
              icon={<LuBotMessageSquare size={30} />}
              title="Conversas com IA"
              description="Quantidade de conversas onde a IA respondeu"
              value={data?.aiSessions.quantity || 0}
              key={2}
            />
            <CardDashboard
              icon={<LuMessageSquareText size={30} />}
              title="Conversas com atendente"
              description="Quantidade de conversas onde o atendente respondeu"
              value={data?.attendantSessions.quantity || 0}
              key={3}
            />
            <CardDashboard
              icon={<LuMessageSquareMore size={30} />}
              title="Conversas em espera"
              description="Quantidade de conversas que estão na fila de espera"
              value={data?.waitingSessions.quantity || 0}
              key={4}
            />
            <CardDashboard
              icon={<LuMessagesSquare size={30} />}
              title="Conversas em andamento"
              description="Conversas que estão na caixa de entrada"
              value={data?.ongoingConversations.quantity || 0}
              key={5}
            />
            <CardDashboard
              icon={<LuMessageSquareLock size={30} />}
              title="Conversas finalizadas"
              description="Conversas que foram finalizadas pelo atendente"
              value={data?.finalizedConversations.quantity || 0}
              key={6}
            />
            <CardDashboard
              icon={<LuClock size={30} />}
              title="Primeira Resposta"
              description="Tempo médio para a primeira resposta"
              value={data?.metrics.averageFirstResponseTime || 0}
              key={7}
            />
            <CardDashboard
              icon={<LuCircleCheckBig size={30} />}
              title="Finalizar Atendimento"
              description="Tempo médio para finalizar o atendimento"
              value={0} // TODO: Usar data?.metrics.averageResolutionTime quando disponível
              key={8}
            />
            <CardDashboard
              icon={<LuUsers size={30} />}
              title="Leads"
              description="Quantidade de leads capturados"
              value={data?.leads.quantity || 0}
              key={9}
            />
          </Grid>
        )}
      </Flex>
    </Flex>
  );
}
