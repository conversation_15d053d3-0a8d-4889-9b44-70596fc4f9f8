import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { Box, VStack, Text, HStack, Flex, useBreakpointValue } from "@chakra-ui/react";
import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
  LuBot,
  LuBotMessageSquare,
  LuBrain,
  LuBrainCircuit,
  LuMessageSquareCode,
  LuTrash2,
} from "react-icons/lu";

type ChatBotCardProps = {
  name: string;
  hasKnowledgeBase: boolean;
  isAi: boolean;
  secureId: string;
  active: boolean;
};

export default function ChatBotCard({
  hasKnowledgeBase,
  isAi,
  name,
  secureId,
  active,
}: ChatBotCardProps) {
  const [isActive, setIsActive] = useState<boolean | undefined>();

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  useEffect(() => {
    setIsActive(active);
  }, [active]);

  const handleEdit = async () => {
    await changeStatusAttendant.mutateAsync();
  };

  const changeStatusAttendant = useMutation({
    mutationFn: async () => {
      await api.put(`/chatbots/${secureId}`, {
        isActive: isActive,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: "success",
        description: `ChatBot ${!isActive ? "desativado" : "ativado"} com sucesso`,
        title: `ChatBot ${!isActive ? "desativado" : "ativado"}`,
      });
    },
  });

  return (
    <>
      {isMobile ? (
        // Mobile Layout - Linear/Row Format
        <Box w={"100%"} position={"relative"}>
          {/* <Link href={`/app/chatbot/edit/${secureId}`}> */}
          <Flex
            bgColor={"chatBackground"}
            w={"100%"}
            p={4}
            rounded={"2xl"}
            borderWidth={1}
            borderColor={"transparent"}
            position={"relative"}
            alignItems={"center"}
            justifyContent={"space-between"}
            gap={3}
            opacity={isActive ? 1 : 0.5}
            _hover={{
              bgColor: "chatCardBackground",
              cursor: "pointer",
              transition: "0.3s",
              borderColor: "chatPrimary",
            }}
          // _active={{
          //   transform: "scale(0.95)",
          //   transition: "0.2s",
          // }}
          >
            <HStack gap={3} flex={1}>
              <LuBotMessageSquare size={"24"} color="#FD2264" />
              <VStack alignItems={"flex-start"} gap={1}>
                <Text color={"chatTextColor"} fontSize={"sm"} fontWeight={"medium"}>
                  {name}
                </Text>
                <HStack gap={2}>
                  {isAi ? (
                    <>
                      <LuBrainCircuit
                        size={"16"}
                        color={hasKnowledgeBase ? "#159729" : "#deae20"}
                      />
                      <LuBrain size={"16"} color="#FD2264" />
                    </>
                  ) : (
                    <LuBot size={"16"} color="#FD2264" />
                  )}
                </HStack>
              </VStack>
            </HStack>
          </Flex>
          {/* </Link> */}
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      ) : (
        // Desktop Layout - Card Format
        <Box w={"20%"} position={"relative"}>
          <Link href={`/app/chatbot/edit/${secureId}`}>
            <Box
              bgColor={"chatBackground"}
              h={"150px"}
              p={4}
              opacity={isActive ? 1 : 0.5}
              rounded={"2xl"}
              borderWidth={1}
              borderColor={"transparent"}
              _hover={{
                bgColor: "chatCardBackground",
                cursor: "pointer",
                transition: "0.3s",
                borderColor: "chatPrimary",
              }}
              _active={{
                transform: "scale(0.95)",
                transition: "0.2s",
              }}
              alignContent={"center"}
            >
              <VStack gap={2}>
                <LuBotMessageSquare size={"30"} color="#FD2264" />
                <Text color={"chatTextColor"} fontWeight={"medium"}>
                  {name}
                </Text>
                <HStack>
                  {isAi ? (
                    <>
                      <LuBrainCircuit
                        size={"20"}
                        color={hasKnowledgeBase ? "#159729" : "#deae20"}
                      />
                      <LuBrain size={"20"} color="#FD2264" />
                    </>
                  ) : (
                    <LuBot size={"20"} color="#FD2264" />
                  )}
                </HStack>
              </VStack>
            </Box>
          </Link>
          <Box position={"absolute"} top={2} right={2}>
            <Switch
              name="Ativo"
              checked={isActive}
              onCheckedChange={(value) => setIsActive(value.checked)}
              onChange={handleEdit}
            />
          </Box>
        </Box>
      )}
    </>
  );
}
