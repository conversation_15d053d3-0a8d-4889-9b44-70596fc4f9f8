import { Input } from "@/components/global/inputs/input";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import {
  Box,
  Flex,
  Grid,
  GridItem,
  HStack,
  Text,
  VStack,
} from "@chakra-ui/react";
import HeaderTab from "@/components/tabs/header";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toaster } from "@/components/ui/toaster";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { InputUpload } from "@/components/global/inputs/input-file";
import { SwitchLabel } from "@/components/global/inputs/switch";
import { useState } from "react";
import { GetChatBotsDto } from "@/utils/types/DTO/chat-bots.dto";
import { Tooltip } from "@/components/ui/tooltip";
import { LuCircleHelp } from "react-icons/lu";
import { CustomSlider } from "@/components/global/inputs/slider";
import StreamableChat from "@/components/chat-chatbot/streamable-chat";
import { useRouter } from "next/navigation";

const GeneralTabSchema = yup.object().shape({
  name: yup.string().required("Nome do chat bot é obrigatório"),
  isAI: yup.boolean().required("Tipo de chat bot é obrigatório"),
  isLeadCaptureActive: yup
    .boolean()
    .required("A Captura de lead no chatbot deve ser informada!"),
});

type GeneralTabFormData = yup.InferType<typeof GeneralTabSchema>;

type GeneralTabProps = {
  secureId?: string;
  setSecureId: (value: string) => void;
  setGlobalAI: (value: boolean) => void;
  setGlobaIsLeadCapture: (value: boolean) => void;
};

export default function GeneralTab({
  secureId,
  setSecureId,
  setGlobalAI,
  setGlobaIsLeadCapture,
}: GeneralTabProps) {
  const route = useRouter();
  const [isAI, setIsAI] = useState(false);
  const [msgLimit, setMsgLimit] = useState([1]);
  const [isLeadCaptureActive, setIsLeadCaptureActive] = useState(false);
  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<GeneralTabFormData>({
    resolver: yupResolver(GeneralTabSchema),
    defaultValues: {
      isAI: false,
      isLeadCaptureActive: false,
    },
  });

  const handleCreateOrUpdate = async (data: GeneralTabFormData) => {
    if (!secureId) {
      const { data: resData } = await api.post<GetChatBotsDto>(`/chatbots`, {
        ...data,
      });
      setSecureId(resData.secureId);
      setGlobalAI(data.isAI);
      setGlobaIsLeadCapture(data.isLeadCaptureActive);
    } else {
      await api.put(`/chatbots/${secureId}`, {
        ...data,
      });
    }
    toaster.success({
      description: secureId
        ? "Alterações salvas"
        : "ChatBot criado com sucesso",
      title: "Sucesso",
    });
  };

  return (
    <Flex
      height={"100%"}
      bgColor={"chatCardBackground"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      <VStack flex={1} alignItems={"flex-start"} as={'form'} onSubmit={handleSubmit(handleCreateOrUpdate)}>
        <HeaderTab
          buttonTitle="Salvar Alterações"
          title="Geral"
          hrefBack="/app/config#chatbots"
          isSubmit={isSubmitting}
        />
        <Grid templateColumns="repeat(5, 1fr)" w={"100%"}>
          <GridItem colSpan={{ base: 2, "2xl": 3 }}>
            <VStack
              flex={1}
              gap={8}
              h={"100%"}
              alignItems={"start"}
              justifyContent={"start"}
            >
              <Flex w={"100%"}>
                <Input
                  {...register("name")}
                  label="Nome do Chatbot"
                  labelColor={"chatTextColor"}
                  rounded={"2xl"}
                  color={"#84767A"}
                  error={errors.name}
                />
              </Flex>
              <Flex w={"100%"} justifyContent={"start"} gap={8}>
                <SwitchLabel
                  control={control}
                  label="Inteligência Artificial"
                  labelColor={"chatTextColor"}
                  labelSize="sm"
                  disabled={!!secureId}
                  checkedValue={isAI}
                  {...register("isAI")}
                  onCheckedValue={setIsAI}
                  onChange={() => {}}
                />
                {isAI && (
                  <SwitchLabel
                    control={control}
                    label="Captura de Lead"
                    labelColor={"chatTextColor"}
                    labelSize="sm"
                    checkedValue={isLeadCaptureActive}
                    {...register("isLeadCaptureActive")}
                    onCheckedValue={setIsLeadCaptureActive}
                    onChange={() => {}}
                  />
                )}
              </Flex>
            </VStack>
          </GridItem>
          {secureId && (
            <GridItem colSpan={{ base: 3, "2xl": 2 }} ml={"12"}>
              <Text
                color={"chatTextColor"}
                fontSize={"lg"}
                textAlign={"center"}
                m={2}
              >
                Chat para Teste
              </Text>
              <StreamableChat chatbotSecureId={secureId} />
            </GridItem>
          )}
        </Grid>
        <HStack w={"100%"} justifyContent={"space-between"} mt={5}></HStack>
      </VStack>
    </Flex>
  );
}
