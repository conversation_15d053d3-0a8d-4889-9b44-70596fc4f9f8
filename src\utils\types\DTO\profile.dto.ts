import { SubscriptionStatus } from "../subscription/subscription-status";
import { SubscriptionType } from "../subscription/subscription-type";
import { TransactionStatus } from "../transactions/transaction-status";

type ProfileUser = {
  secureId: string;
  name: string;
  email: string;
  cpf: string;
  cellPhone: string | null;
}

type SubscriptionPlan = {
  secureId: string;
  name: string;
  slug: string;
  description: string;
  details: string;
  price: string;
  knowledgeBaseLimit: number;
  attendantsLimit: number;
  chatbotsLimit: number;
  iaMessagesLimit: number;
  whatsappNumberLimit: number;
}

type ProfileSubscription = {
  secureId: string;
  cycle: number;
  status: SubscriptionStatus;
  type: SubscriptionType;
  plan: SubscriptionPlan
}

type ProfileTransaction = {
  secureId: string;
  amount: string;
  status: TransactionStatus;
  payedAt: string;
}

export type GetProfileDto = {
  user: ProfileUser
  account: {
    secureId: string
  }
  subscription: ProfileSubscription | null,
  transactions: ProfileTransaction[] | null
}