import { InputIcon } from "@/components/global/inputs/input-icon";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { InputPassword } from "@/components/global/inputs/input-password";
import { useAuthContext } from "@/providers/AuthProvider";
import { Flex, Text, FlexProps } from "@chakra-ui/react";
import { IoPersonOutline, IoMailOutline, IoBusinessOutline, IoPhonePortraitOutline, IoCardOutline, IoLockClosedOutline } from "react-icons/io5";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";

const SignUpSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
  companyName: yup.string().required("Nome da empresa é obrigatório"),
  cellPhone: yup
    .string()
    .required("Telefone é obrigatório")
    .test("is-valid-phone", "Telefone inválido", (value) => { // Faz validação ignorando a máscara
      if (!value) return false;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 13 || onlyNumbers.length === 12;
  }),
  cpf: yup
    .string()
    .required("CPF é obrigatório")
    .test("is-valid-cpf", "CPF inválido", (value) => { // Faz validação ignorando a máscara
      if (!value) return false;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 11;
  }),
  password: yup.string().required("A senha é obrigatória").min(6, "A senha deve ter pelo menos 6 caracteres"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password')], 'As senhas devem coincidir')
    .required('Confirmação de senha é obrigatória'),
});

type SignUpFormData = yup.InferType<typeof SignUpSchema>;

interface SignUpFormProps extends FlexProps {
}

export default function SignUpForm({...rest }: SignUpFormProps) {
  const [phoneValue, setPhoneValue] = useState("");
  const [phoneSelected, setPhoneSelected] = useState(false);
  const [phoneMask, setPhoneMask] = useState("+99 (99) 9999-9999[9]");
  const { signUp } = useAuthContext();

  const {
    register,
    handleSubmit,
    formState,
    watch,
    formState: { errors },
  } = useForm<SignUpFormData>({
    resolver: yupResolver(SignUpSchema),
  });
  
  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);


  const handleSignUp = async (data: SignUpFormData) => {
    try {
      await signUp.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <Flex
      id="leadInfo"
      as="form"
      flexDirection={"column"}
      onSubmit={handleSubmit(handleSignUp)}
      gap={{base: 2, sm: 2, md: 4}}
      bg="white"
      p={{ base: 6, md: 8 }}
      borderRadius={20}
      boxShadow="md"
      h="fit-content"
      {...rest}
    >
      <Text
        fontSize={{ base: "lg", md: "xl" }}
        fontWeight="bold"
        color="chatPrimary"
        textAlign="center"
        mb={4}
      >
        Preencha seus dados pessoais
      </Text>
      <InputIcon
        startElement={<IoPersonOutline />}
        borderRadius={20}
        placeholder="Digite seu Nome"
        size={"md"}
        {...register("name")}
        error={errors.name}
      />
      <InputIcon
        startElement={<IoMailOutline />}
        borderRadius={20}
        placeholder="Digite seu Email"
        size={"md"}
        {...register("email")}
        error={errors.email}
      />
      <InputIcon
        startElement={<IoBusinessOutline />}
        borderRadius={20}
        placeholder="Digite o Nome da empresa"
        size={"md"}
        {...register("companyName")}
        error={errors.companyName}
      />

      <Flex flexDir={{base: "column", sm: "row"}} gap={2} w={"100%"}>
        <InputMaskIcon
          mask={phoneMask}
          value={phoneValue}
          startElement={<IoPhonePortraitOutline />}
          borderRadius={20}
          placeholder="Telefone"
          onFocus={() => {
            if (!phoneValue && !phoneSelected) setPhoneValue("+55 ");
            setPhoneSelected(true)
          }}
          size={"md"}
          {...register("cellPhone", {
            onChange: (e) => {
              const value = e.target.value;
              setPhoneValue(value);
            }
          })}
          error={errors.cellPhone}
        />
        <InputMaskIcon
          mask={"999.999.999-99"}
          startElement={<IoCardOutline />}
          borderRadius={20}
          placeholder="CPF"
          size={"md"}
          {...register("cpf")}
          error={errors.cpf}
        />
      </Flex>

      <Flex flexDir={{base: "column", sm: "row"}} gap={2} w={"100%"}>
        <InputPassword
          startElement={<IoLockClosedOutline />}
          borderRadius={20}
          placeholder="Senha"
          size={"md"}
          type="password"
          {...register("password")}
          error={errors.password}
        />
        <InputPassword
          startElement={<IoLockClosedOutline />}
          borderRadius={20}
          placeholder="Confirme a Senha"
          size={"md"}
          type="password"
          {...register("confirmPassword")}
          error={errors.confirmPassword}
        />
      </Flex>

      <Button
        type="submit"
        w={"100%"}
        borderRadius={"full"}
        size={"lg"}
        fontWeight="700"
        bgColor="chatPrimary"
        color="white"
        py={6}
        fontSize="lg"
        _hover={{
          bg: "transparent",
          color: "chatPrimary",
          borderColor: "chatPrimary",
        }}
        loading={formState.isSubmitting}
      >
        COMEÇAR AGORA GRÁTIS
      </Button>

      <Text
        fontSize="sm"
        color="gray.500"
        textAlign="center"
      >
        (Sem cartão de crédito. Sem compromisso.)
      </Text>

      <Text
        fontSize="sm"
        color="gray.600"
        textAlign="center"
      >
        Já possui uma conta?{" "}
        <Link href="/">
          <Text
            as="span"
            color="chatPrimary"
            fontWeight="semibold"
            _hover={{ textDecoration: "underline" }}
            cursor="pointer"
          >
            Faça login
          </Text>
        </Link>
      </Text>
    </Flex>
  );
}
