import { api } from "@/services/api";
import { permissionsInputDTO } from "@/utils/types/DTO/permissions.dto";
import { useQuery } from "@tanstack/react-query";

type PermissionsResponse = {
  permissions: permissionsInputDTO[];
};

async function getBackofficePermissions() {
  const { data } = await api.get<PermissionsResponse>(
    "/register/backoffice-dependencies"
  );

  return data;
}

export default function useBackofficePermissions() {
  return useQuery({
    queryKey: ["getBackofficePermissions"],
    queryFn: async () => await getBackofficePermissions(),
  });
}
