import { api } from "@/services/api";
import { GetAccountTransactionDto, GetAllAccountsTransactionsDto } from "@/utils/types/DTO/accounts-transactions.dto";
import { GetAllAccountUsersDto } from "@/utils/types/DTO/accounts-users.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllAccountsUsers(page: number, accountSecureId?: string) {
  const limit = 4;
  const { data } = await api.get<GetAllAccountUsersDto>(`/accounts-users?page=${page}&limit=${limit}`, {
    params: {
      accountSecureId
    }
  });
  return data;
}

export function useGetAllAccountsUsers(page: number, accountSecureId?: string) {
  return useQuery({
    queryKey: ["accounts-users", page, accountSecureId],
    queryFn: async () => await getAllAccountsUsers(page, accountSecureId),
  });
}

// async function getAccountTransactions(secureId: string) {
//   const { data } = await api.get<GetAccountUserDto>(`/accounts-transactions/${secureId}`);
//   return data;
// }

// export function useGetAccountTransactions(secureId: string) {
//   return useQuery({
//     queryKey: ["accounts-transactions", secureId],
//     queryFn: async () => await getAccountTransactions(secureId),
//   });
// }
