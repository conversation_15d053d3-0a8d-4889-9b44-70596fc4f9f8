import { Input } from "@/components/global/inputs/input";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import { Button } from "@/components/ui/button";
import { toaster } from "@/components/ui/toaster";
import { useGetAccount } from "@/hook/accounts/useGetAccount";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Flex, Heading, HStack, Text, VStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

type AccountTabProps = {
  accountSecureId: string
}

const updateAccountSchema = yup.object().shape({
  companyName: yup.string().required("Nome é obrigatório"),
  openaiApiKey: yup.string(),
  prompt: yup.string(),
});

type UpdateAccountFormData = yup.InferType<typeof updateAccountSchema>;

export default function AccountTab({accountSecureId}: AccountTabProps) {
  const { data: accountData } = useGetAccount(accountSecureId as string);

  const { 
    register,
    setValue,
    handleSubmit,
    formState: { errors },
   } = useForm({
    resolver: yupResolver(updateAccountSchema),
    defaultValues: {
      companyName: "",
      openaiApiKey: "",
      prompt: "",
    },
  })

  useEffect(() => {
    if (accountData) {
      setValue("companyName", accountData.companyName);
      setValue("openaiApiKey", accountData.openaiApiKey);
      setValue("prompt", accountData.prompt);
    }
  }, [accountData, setValue])

  const updateAccount = useMutation({
    mutationFn: async (data: UpdateAccountFormData) => {
      return await api.put(`/accounts/${accountSecureId}`,
        data
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Conta atualizada com sucesso!",
      })
      queryClient.invalidateQueries({
        queryKey: ["accounts-backoffice"],
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar conta:", error);
    },
  })

  const handleUpdate = async (data: UpdateAccountFormData) => {
    try {
      updateAccount.mutateAsync(data);
    } catch (e) { }
  } 
  
  return (
    <Flex 
      flex={1}
      h="100%"
      w="100%"
      gap={4}
      flexDirection="column"
      marginTop={{ base: "12rem", md: 0 }}
    >
      <Heading size="md">Dados da Conta</Heading>
      <Flex
        as={"form"}
        flex={1}
        flexWrap={"wrap"}
        flexDirection="column"
        onSubmit={handleSubmit(handleUpdate)}
      >
        <HStack gap={4}>
          <Input
            height="80px"
            label="Nome da Empresa"
            borderRadius={20}
            color="gray.900"
            size={{ base: "sm", sm: "md" }}
            {...register("companyName")}
            error={errors.companyName}
          />
          <Input
            height="80px"
            label="Chave API OpenAI"
            borderRadius={20}
            color="gray.900"
            size={{ base: "sm", sm: "md" }}
            {...register("openaiApiKey")}
            error={errors.openaiApiKey}
          />
        </HStack>
        <InputTextArea
          label="Prompt"
          height={"420px"}
          borderRadius={20}
          color="gray.900"
          {...register("prompt")}
          error={errors.prompt}
        />
        <Flex justifyContent="flex-end" mt={8} gap={4}>
          <Button
            h={"40px"}
            size={"md"}
            type="submit"
            borderRadius={20}
            fontWeight="700"
            bgColor={"background"}
            transitionDuration={"0.2s"}
            color={"gray.200"}
            _hover={{
              bg: "gray.100",
              color: "background",
            }}
          >
            Salvar
          </Button>
        </Flex>
      </Flex>
    </Flex>
  )
}