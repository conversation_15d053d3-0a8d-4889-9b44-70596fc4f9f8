import { ChatSendMessageInputDTO } from "@/utils/types/DTO/chat-send-message.dto";
import axios from "axios";
import { io, Socket } from "socket.io-client";

type sendMessage = {
  userId?: string;
  sessionId: string;
  content: string;
  isAttendant: boolean;
  chatId?: string;
  customerName?: string;
  customerId?: string;
  type?: "text" | "audio" | "file" | "image";
  base64?: string;
  messageDirection?: "sent" | "received";
  replyTo?: string;
  file?: File; // novo campo para envio binário
};

interface newMessage {
  secureId: string;
  createdAt: Date;
  sessionId: string;
  receiveMessage?: string;
  sendMessage?: string;
  attendantName?: string;
  type?: "text" | "audio";
  urlFile?: string;
  messageDirection?: "sent" | "received";
  replyTo?: string;
}

export class WebSocketService {
  private socket: Socket;
  private reconnectAttempts: number = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;

  constructor() {
    this.socket = io(process.env.NEXT_PUBLIC_API_URL, {
      transports: ["websocket"],
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: this.MAX_RECONNECT_ATTEMPTS,
      reconnectionDelay: 1000,
    });

    this.setupErrorHandling();
  }

  private setupErrorHandling() {
    this.socket.on("connect_error", (error) => {
      console.error("Connection error:", error);
      this.reconnectAttempts++;

      if (this.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
        console.error("Max reconnection attempts reached");
        this.disconnect();
      }
    });

    this.socket.on("disconnect", (reason) => {});
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket.connect();

      const timeout = setTimeout(() => {
        this.socket.disconnect();
        reject(new Error("Connection timeout"));
      }, 5000);

      const connectHandler = () => {
        clearTimeout(timeout);
        this.reconnectAttempts = 0;
        resolve();
      };

      const errorHandler = (error: Error) => {
        clearTimeout(timeout);
        this.socket.off("connect", connectHandler);
        reject(error);
      };

      this.socket.once("connect", connectHandler);
      this.socket.once("connect_error", errorHandler);
    });
  }

  joinRoom(userId: string, sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        reject(new Error("Not connected to server"));
        return;
      }

      this.socket.emit("joinRoom", { userId, sessionId });

      this.socket.once("roomJoined", () => resolve());
      this.socket.once("roomError", (error) => reject(error));
    });
  }

  leaveRoom(userId: string, sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        reject(new Error("Not connected to server"));
        return;
      }

      this.socket.emit("leaveRoom", { userId, sessionId });

      this.socket.once("roomLeft", () => resolve());
      this.socket.once("roomError", (error) => reject(error));
    });
  }

  isConnected(): boolean {
    return this.socket.connected;
  }

  onRefreshMessages(callback: () => void): void {
    this.socket.on("refreshMessages", () => {
      callback();
    });
  }

  sendMessage(sendMessage: sendMessage): Promise<void> {
    return new Promise(async (resolve, reject) => {
      const { file, ...payload } = sendMessage;

      if (!this.isConnected()) {
        reject(new Error("Not connected to server"));
        return;
      }

      if (!payload?.isAttendant && !payload?.chatId) {
        reject(new Error("Missing accountId"));
        return;
      }

      if (file) {
        const newFile = new FormData();
        newFile.append("file", file);
        const response = await axios.post<{
          secureId: string;
          url: string;
        }>(`${process.env.NEXT_PUBLIC_API_URL}public/uploads`, newFile);

        const newPayload = {
          ...payload,
          fileSecureId: response.data.secureId,
          urlFile: response.data.url,
        };

        this.socket.emit("sendMessage", newPayload, (acknowledgement: any) => {
          if (acknowledgement?.error) {
            reject(new Error(acknowledgement.error));
          } else {
            resolve();
          }
        });
        return;
      }

      this.socket.emit("sendMessage", payload, (acknowledgement: any) => {
        if (acknowledgement?.error) {
          reject(new Error(acknowledgement.error));
        } else {
          resolve();
        }
      });
    });
  }

  onNewMessage(callback: (message: newMessage) => void): void {
    this.socket.on("newMessage", (data: any) => {
      try {
        // Validate incoming data
        if (!data) {
          console.error("Received empty message data");
          return;
        }

        const formattedMessage: newMessage = {
          secureId: data.secureId || "",
          createdAt: new Date(data.createdAt || Date.now()),
          sessionId: data.sessionId || "",
          receiveMessage: data.receiveMessage,
          sendMessage: data.sendMessage,
          messageDirection: data.messageDirection,
          attendantName: data.attendantName,
          type: data.messageType || "text",
          urlFile: data.urlFile || "",
          replyTo: data.replyTo || "",
        };

        // Check socket connection
        if (!this.isConnected()) {
          console.warn("Socket disconnected, attempting to reconnect...");
          this.socket.connect();
        }

        // Call the callback with formatted message
        callback(formattedMessage);
      } catch (error) {
        console.error("Error processing message:", error);
      }
    });

    // Handle errors
    this.socket.on("newMessage_error", (error: any) => {
      console.error("Message receive error:", error);
    });
  }

  attendantAssignedChat(callback: (isChanged: boolean) => void): void {
    this.socket.on("attendantAssignedChat", () => {
      try {
        callback(true);
      } catch (error) {
        console.error("Error processing attendantAssignedChat:", error);
      }
    });
  }

  attendantInterrupt(sessionId: string, attendantId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        reject(new Error("Not connected to server"));
        return;
      }

      const payload = {
        sessionSecureId: sessionId,
        attendantSecureId: attendantId,
      };

      this.socket.emit(
        "attendantInterrupt",
        payload,
        (acknowledgement: any) => {
          if (acknowledgement?.error) {
            reject(new Error(acknowledgement.error));
          } else {
            resolve();
          }
        }
      );
    });
  }

  removeAllListeners(): void {
    this.socket.removeAllListeners();
  }

  disconnect(): void {
    this.socket.disconnect();
    this.reconnectAttempts = 0;
  }
}
