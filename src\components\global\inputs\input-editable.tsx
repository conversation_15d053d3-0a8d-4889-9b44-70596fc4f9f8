import { Field } from "@/components/ui/field";
import { Editable, IconButton } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>heck, LuPencilLine, LuX } from "react-icons/lu";

type InputEditableProps = {
  placeholder: string;
  value: string;
  onValueChange: (value: string) => void;
  handleSave: () => void;
};

export const InputEditable = ({
  placeholder,
  value,
  onValueChange,
  handleSave,
}: InputEditableProps) => {
  return (
    <Editable.Root
      value={value}
      onValueChange={(e) => {
        onValueChange(e.value);
      }}
      onValueCommit={handleSave}
      placeholder={placeholder}
      bgColor={"white"}
      borderRadius={10}
      color={"#84767A"}
      _hover={{
        borderColor: "chatPrimary",
      }}
      _placeholder={{ color: "#B1A0A5" }}
      _focus={{
        borderColor: "chatPrimary",
      }}
      size={'lg'}
      p={{ "2xl": 0.5 }}
    >
      <Editable.Preview
        border={"none"}
        _focus={{
          border: "chatPrimary",
        }}
        _hover={{
          bgColor: "chatBackground",
        }}
      />
      <Editable.Input
        _focus={{
          borderColor: "chatPrimary",
          borderWidth: 2,
        }}
      />
      <Editable.Control>
        <Editable.EditTrigger
          asChild
          color={"#84767A"}
          borderWidth={2}
          _hover={{
            borderColor: "chatPrimary",
            bgColor: "transparent",
          }}
        >
          <IconButton variant="ghost" size="xs">
            <LuPencilLine />
          </IconButton>
        </Editable.EditTrigger>
        <Editable.CancelTrigger
          asChild
          color={"#84767A"}
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: "chatPrimary",
            bgColor: "transparent",
          }}
        >
          <IconButton variant="outline" size="xs">
            <LuX />
          </IconButton>
        </Editable.CancelTrigger>
        <Editable.SubmitTrigger
          asChild
          color={"#84767A"}
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: "chatPrimary",
            bgColor: "transparent",
          }}
        >
          <IconButton variant="outline" size="xs">
            <LuCheck />
          </IconButton>
        </Editable.SubmitTrigger>
      </Editable.Control>
    </Editable.Root>
  );
};
