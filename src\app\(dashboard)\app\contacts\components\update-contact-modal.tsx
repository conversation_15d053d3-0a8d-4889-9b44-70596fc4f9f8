import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { HStack, VStack } from "@chakra-ui/react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

type ContactData = {
  secureId: string;
  name: string;
  email?: string;
  phone?: string;
  document?: string;
};

type UpdateContactModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  contact: ContactData | null;
};

const UpdateContactSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  phone: yup.string().required("Celular é obrigatório"),
  document: yup.string().optional(),
});

type UpdateContactForm = yup.InferType<typeof UpdateContactSchema>;

export function UpdateContactModal({ open, setOpen, contact }: UpdateContactModalProps) {
	const phoneMask = "+99 (99) 99999-9999[9]";
	const cpfMask = "999.999.999-99";
	const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
		reset,
    setValue,
    formState: { errors },
  } = useForm<UpdateContactForm>({
    resolver: yupResolver(UpdateContactSchema),
  });

	const updateContactMutation = useMutation({
		mutationFn: async (data: UpdateContactForm) => {
      if (!contact) throw new Error("Contact data is missing");
			const response = await api.patch(`/contact/${contact.secureId}`, {
				name: data.name,
				email: data.email,
				phone: data.phone,
				document: data.document,
			});
			return response.data;
		},
		onSuccess: () => {
			toaster.success({ title: "Contato atualizado com sucesso!", type: "success" });
			queryClient.invalidateQueries({ queryKey: ['list-contacts'] });
			setOpen(false);
			reset();
		},
		onError: (error) => {
			toaster.error({ title: "Erro ao atualizar contato!", type: "error" });
			console.error("Erro ao atualizar contato:", error);
		},
	});

	useEffect(() => {
		if (open && contact) {
      setValue("name", contact.name);
      setValue("email", contact.email || "");
      setValue("phone", contact.phone || "");
      setValue("document", contact.document || "");
		} else if (!open) {
      reset();
    }
	}, [open, contact, setValue, reset]);

	const onSubmit = async (data: UpdateContactForm) => {
		await updateContactMutation.mutateAsync(data);
	}

  return (
    <BasicModal
      open={open}
      setOpen={setOpen}
      asForm={true}
      cancelText="Cancelar"
      children={
        <VStack gap={10} alignItems={"flex-start"} w={"100%"}>
          <HStack gap={5} w={"100%"}>
            <Input
              label="Nome"
              placeholder="Nome do contato"
              {...register("name")}
							rounded={'md'}
              error={errors.name}
            />
            <Input
              label="Email"
              placeholder="Email do contato"
              {...register("email")}
							rounded={'md'}
              error={errors.email}
            />
          </HStack>
          <HStack w={"100%"}>
            <InputMaskIcon
              label="Celular"
							mask={phoneMask}
              placeholder="Celular do contato"
              {...register("phone")}
							rounded={'md'}
              error={errors.phone}
            />
            <InputMaskIcon
              label="Documento"
							mask={cpfMask}
              placeholder="Documento do contato"
              {...register("document")}
							rounded={'md'}
              error={errors.document}
            />
          </HStack>
        </VStack>
      }
      handleSubmit={handleSubmit(onSubmit)}
      isSubmitting={updateContactMutation.isPending}
      confirmText="Atualizar"
      title="Atualizar Contato"
      placement="center"
      size="lg"
    />
  );
}
