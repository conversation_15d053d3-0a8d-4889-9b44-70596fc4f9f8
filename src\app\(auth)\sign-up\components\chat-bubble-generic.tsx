import { Avatar } from "@/components/ui/avatar";
import { Box, HStack, IconButton, VStack } from "@chakra-ui/react";
import { TbCornerDownRight } from "react-icons/tb";

type ChatBubbleGeneric = {
  name: string;
  message: string;
};

export default function ChatBubbleGeneric({ name, message }: ChatBubbleGeneric) {
  return (
    <HStack w={"100%"} justifyContent={"center"}>
      <HStack
        alignItems={"start"}
        gap={5}
        maxWidth={{base: "90%", md:"90%" , lg:"90%"}}
        ml={0}
      >
        <Avatar
          size={"lg"}
          name={"clientName"}
          bgColor={'#5e5e5e'}
          src={undefined}
          zIndex={1}
        />
        <VStack position={"relative"}>
          <Box
            position="absolute"
            bottom={0}
            left="-15px"
            top="30px"
            width={0}
            height={0}
            zIndex={0}
            borderLeft="20px solid transparent"
            borderRight="20px solid transparent"
            borderTop="20px solid #eadbde"
          />
          <Box 
            bgColor={"#eadbde"} 
            p={2.5} 
            rounded={10} 
            zIndex={1} 
            position="relative"
          >
            <VStack align={"start"} color={"text"} justify={"space-between"} fontSize={{base: "xs", sm: "sm", md: "md", xl: "lg"}} w="100%">
              {name}
              <Box color={"gray.900"} w="100%" fontSize={{base: "10px", sm: "xs", md: "sm", xl: "md"}}>
                {message}
              </Box>
            </VStack>
          </Box>
        </VStack>
      </HStack>
    </HStack>
    );
}
