import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { SubscriptionStatus } from "@/utils/types/subscription/subscription-status";
import { Badge, Box, HStack, Icon, Separator, Text, VStack } from "@chakra-ui/react";
import { useState } from "react";
import { FaPen } from "react-icons/fa6";
import { Button } from "../ui/button";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { toaster } from "../ui/toaster";
import { queryClient } from "@/services/queryClient";
import BasicModal from "../global/modal/basic-modal";

type CardAccountSubscriptionPros = {
  subscriptionSecureId: string;
  subscriptionPlan: string;
  subscriptionCycle: number;
  subscriptionStatus: SubscriptionStatus;
  subscriptionType: "paid" | "trial";
  subscriptionStartsAt: string;
  subscriptionEndsAt: string;
  gatewayId: number;
}

  function mapSubscriptionStatus(status: string): { label: string, color: string } {
    switch (status) {
      case "new":
        return { label: "Nova", color: "white" };
      case "active":
        return { label: "Ativa", color: "green.500" };
      case "new_charge":
        return { label: "Nova Cobrança", color: "dodgerblue" };
      case "canceled":
        return { label: "Cancelada", color: "red.500" };
      case "expired":
        return { label: "Expirada", color: "gray" };
      default:
        return { label: status, color: "gray" };
    }
  }

  function mapSubscriptionType(type: string): string {
    switch (type) {
      case "paid":
        return "Paga";
      case "trial":
        return "Dias de Teste";
      case "sponsored":
        return "Patrocinada";
      case "free":
        return "Gratuita";
      default:
        return type;
    }
  }

export default function CardAccountSubscription({
  subscriptionSecureId,
  subscriptionPlan,
  subscriptionCycle,
  subscriptionStatus,
  subscriptionType,
  subscriptionStartsAt,
  subscriptionEndsAt,
  gatewayId
}: CardAccountSubscriptionPros) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const cancelSubscription = useMutation({
    mutationFn: async () => {
      await api.delete(`/accounts-subscriptions/${subscriptionSecureId}`);
    },
    onSuccess: () => {
      toaster.create({
        description: "Assinatura cancelada com sucesso",
        title: "Assinatura cancelada",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["accounts-subscriptions"],
      });
    },
    onError: () => {},
  });
  const handleCancelSubscription = async () => {
    try {
      cancelSubscription.mutateAsync();
    } catch (e) {}
  };

  return (
    <Box
      p={4}
      borderRadius={20}
      bg="background"
      fontSize={"md"}
    >
      <VStack alignItems="flex-start" gap={2}>
        <HStack w="100%" alignItems={"center"} justifyContent={"space-between"}>
          <Text fontSize="lg">
            Plano: <strong>{subscriptionPlan}{" "}</strong>
          </Text>
            <Text>
              {new Date(subscriptionStartsAt).toLocaleDateString()} - {new Date(subscriptionEndsAt).toLocaleDateString()}
            </Text>
        </HStack>
        <Separator/>
        <Text>
          Ciclo: <strong>{subscriptionCycle}</strong>
        </Text>
        <Text>
          Status:{" "}
          <Badge color={mapSubscriptionStatus(subscriptionStatus).color} fontSize={"md"}>
            {mapSubscriptionStatus(subscriptionStatus).label}
          </Badge>
        </Text>
        <Text>
          Tipo: <strong>{mapSubscriptionType(subscriptionType)}</strong>
        </Text>
      </VStack>
      <Separator my={4} />
      <HStack
        w={"full"}
        justifyContent={"space-between"}
        alignItems={"center"}
      >
        <Text fontSize="sm" color="gray.600">
          ID da Assinatura no Gateway: <strong>{gatewayId ? gatewayId : "N/A"}</strong>
        </Text>
        {subscriptionStatus !== "canceled" && (
          <Button
            size="xs"
            color={"white"}
            bg={"backgroundCard"}
            borderRadius={"full"}
            _hover={{
              transition: "0.3s",
              bg: "red.500",
              color: "white",
            }}
          onClick={() => setIsModalOpen(true)}
        >
          Cancelar
        </Button>
        )}
      </HStack>
      <BasicModal
        open={isModalOpen}
        placement="center"
        setOpen={setIsModalOpen}
        title="Tem certeza que deseja cancelar a assinatura?"
        cancelText="Voltar"
        deleteText="Cancelar assinatura"
        handleDelete={handleCancelSubscription}
      >
        <Text color={"gray.600"}>
          Ao cancelar a assinatura, você perderá acesso a todos os recursos desse plano.
        </Text>
      </BasicModal>
    </Box>
  )
}