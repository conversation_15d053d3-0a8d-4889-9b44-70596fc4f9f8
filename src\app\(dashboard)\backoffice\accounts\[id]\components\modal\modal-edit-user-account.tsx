import { PermissionsCheckbox } from "@/components/global/checkbox/permissions-checkbox";
import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Box, Flex, HStack, Tabs, Text } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { useController, useForm } from "react-hook-form";
import { LuUser, LuUserCog } from "react-icons/lu";
import * as yup from "yup";
import BasicBackofficeModal from "@/components/global/modal/basic-backoffice-modal";
import useBackofficePermissions from "@/hook/permissions/useBackofficePermissions";
import useAttendantPermissions from "@/hook/permissions/useAttendantPermissions";
import { useGetUserBackoffice } from "@/hook/users/useGetUserBackoffice";
import AllPermissionCheckbox from "@/components/global/checkbox/all-permissions-checkbox";

type ModalEditAccountUserBackofficeProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
  accountSecureId: string;
  userSecureId: string;
  userName: string;
  userEmail: string;
  userCpf: string;
  userCellPhone: string | null;
  userIsOwner: boolean;
  userHasAllPermissions: boolean;
  userPermissions: string[];
};

const editAccountUserSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  cellPhone: yup.string(),
  password: yup.string().required("Senha é obrigatória"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem coincidir")
    .required("Confirmação de senha é obrigatória"),
  cpf: yup.string().required("CPF é obrigatório"),
  hasAllPermissions: yup.boolean().optional(),
  participatesInRotation: yup.boolean().optional(),
  permissions: yup.array().of(yup.string()).optional(),
});

type EditAccountUserFormData = yup.InferType<typeof editAccountUserSchema>;

export default function ModalAccountEditUserBackoffice({
  openModal,
  setOpenModal,
  accountSecureId,
  userSecureId,
  userName,
  userEmail,
  userCpf,
  userCellPhone,
  userIsOwner,
  userHasAllPermissions,
  userPermissions,
}: ModalEditAccountUserBackofficeProps) {
  const { data: permissionsData, isLoading } = useAttendantPermissions();

  const [phoneMask, setPhoneMask] = useState("+99 (99) 9999-9999[9]");
  const [permissionsItems, setPermissionsItems] = useState<
    { label: string; value: string }[]
  >([]);

  useEffect(() => {
    if (permissionsData?.permissions) {
      setPermissionsItems(
        permissionsData.permissions.map((permission) => ({
          label: permission.description,
          value: permission.secureId,
        }))
      );
    }
  }, [permissionsData]);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EditAccountUserFormData>({
    resolver: yupResolver(editAccountUserSchema),
    defaultValues: {
      name: "",
      email: "",
      cpf: "",
      cellPhone: "",
      hasAllPermissions: false,
      // participatesInRotation: false,
      permissions: [],
    },
  });

  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);

  const permissions = useController({
    control,
    name: "permissions",
    defaultValue: userPermissions || [],
  });

  useEffect(() => {
    if (userName) {
      setValue("name", userName);
      setValue("cpf", userCpf);
      setValue("email", userEmail);
      setValue("cellPhone", userCellPhone ? userCellPhone : "");
      setValue("hasAllPermissions", userHasAllPermissions);
      setValue("permissions", userPermissions || []);

      permissions.field.onChange(userPermissions || []);
    }
  }, [userSecureId, setValue]); 

  const editUser = useMutation({
    mutationFn: async (data: EditAccountUserFormData) => {
      const { permissions, hasAllPermissions, ...postData } = data;

      return await api.put(`register/backoffice-account/user/${userSecureId}`, {
        user: {
          permissionsSecureIds: hasAllPermissions ? undefined : permissions,
          hasAllPermissions: hasAllPermissions,
          ...postData,
        },
        accountSecureId: accountSecureId,
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Usuário atualizado com sucesso!",
        title: "Usuário atualizado!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["accounts-users"],
      });
      setOpenModal(false);
    },
    onError: () => {},
  });

  const onSubmit = async (data: EditAccountUserFormData) => {
    try {
      editUser.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <BasicBackofficeModal
      open={openModal}
      size="xl"
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Cadastrar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Cadastrar novo usuário"
      placement="top"
      children={
        <Tabs.Root defaultValue="user" colorPalette={"white"} size={"lg"}>
          <Tabs.List borderColor={"chatTextColor"}>
            <Tabs.Trigger
              value="user"
              border={"none"}
              _selected={{
                color: "white",
              }}
            >
              <LuUser />
              Usuário
            </Tabs.Trigger>
            <Tabs.Trigger
              value="permissions"
              _selected={{
                color: "white",
              }}
            >
              <LuUserCog />
              Permissões
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="user">
            <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
              <Input
                label="Nome"
                color="background"
                placeholder="Digite o nome do usuário"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("name")}
                error={errors.name}
              />
              <Input
                label="Email"
                color="background"
                placeholder="Digite o email do usuário"
                size={"md"}
                height="80px"
                borderRadius={20}
                {...register("email")}
                error={errors.email}
              />
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <InputMaskIcon
                  label="CPF"
                  mask={"999.999.999-99"}
                  color="background"
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o CPF do usuário"
                  size={"md"}
                  {...register("cpf")}
                  error={errors.cpf}
                />
                <InputMaskIcon
                  label="Celular"
                  mask={phoneMask}
                  color="background"
                  fieldHeight="80px"
                  borderRadius={20}
                  placeholder="Digite o número do atendente"
                  size={"md"}
                  {...register("cellPhone")}
                  error={errors.cellPhone}
                />
              </HStack>
              <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
                <Input
                  type="password"
                  label="Senha"
                  color="background"
                  placeholder="Digite a senha do usuário"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("password")}
                  error={errors.password}
                />
                <Input
                  type="password"
                  label="Confirmação da Senha"
                  color="background"
                  placeholder="Digite a senha novamente"
                  size={"md"}
                  height="80px"
                  borderRadius={20}
                  {...register("confirmPassword")}
                  error={errors.confirmPassword}
                />
              </HStack>
            </Flex>
          </Tabs.Content>
          <Tabs.Content value="permissions">
            <Box position={"relative"} w={"100%"} h={"100%"}>
              <Text color={"chatTextColor"} fontSize={"lg"} mb={4}>Selecione as permissões</Text>
              <AllPermissionCheckbox
                control={control}
                label="Conceder todas as permissões"
                name="hasAllPermissions"
                invalid={!!errors.hasAllPermissions}
              />
              <PermissionsCheckbox
                invalid={!!errors.permissions}
                onValueChange={permissions.field.onChange}
                value={permissions.field.value as string[] | undefined}
                name={permissions.field.name}
                label="Permissões"
                items={permissionsItems}
                hasLegend={false}
              />
            </Box>
          </Tabs.Content>
        </Tabs.Root>
      }
    />
  );
}
