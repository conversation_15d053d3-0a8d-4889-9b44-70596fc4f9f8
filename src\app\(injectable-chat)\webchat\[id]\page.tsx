"use client";
import { <PERSON>, VStack, <PERSON>, Spinner, <PERSON> } from "@chakra-ui/react";
import { v4 as uuidV4 } from "uuid";
import {
  ChatSessionDto,
} from "@/utils/types/DTO/chat-sessions.dto";
import { SocketProvider, useSocket } from "@/providers/SocketProvider";
import useChat from "@/hook/_public/chat/useChat";
import { use, useEffect, useState } from "react";
import { api } from "@/services/api";
import ChatOrchestrator from "./components/chat-orchestrator";

export default function InjectableChat({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: chatId } = use(params);
  try {
    const { data, isLoading } = useChat(chatId);
    const [sessionSecureId, setSessionSecureId] = useState<string | null>();
    const [userSecureId, setUserSecureId] = useState<string | null>();
    const [chatSecureId, setChatSecureId] = useState<string>(chatId);
    const [isAILoading, setIsAILoading] = useState<boolean>(true);
    const [isAI, setIsAI] = useState<boolean>(false);

    useEffect(() => {
      if (!data) return;

      const getChatHistory = async (secureId: string) => {
        const { data: chatHistory } = await api.get<ChatSessionDto>(
          `public/chat-sessions/${secureId}`
        );
        setIsAI(
          data?.chatbot?.isAI === true &&
            (!chatHistory ? true : chatHistory.isAIResponder)
        );
        setIsAILoading(false);
      };

      const storedSessionId = localStorage.getItem("@PlyrChat:SessionId");
      const storedUserId = localStorage.getItem("@PlyrChat:UserId");
      const storedChatId = localStorage.getItem("@PlyrChat:ChatId");

      let sessionId = storedSessionId;
      let userId = storedUserId;

      if (!storedSessionId || !storedUserId || storedChatId !== chatId) {
        sessionId = uuidV4();
        userId = uuidV4();

        localStorage.setItem("@PlyrChat:SessionId", sessionId);
        localStorage.setItem("@PlyrChat:UserId", userId);
        localStorage.setItem("@PlyrChat:ChatId", chatId);
      }

      setSessionSecureId(sessionId);
      setUserSecureId(userId);

      if (sessionId) {
        getChatHistory(sessionId);
      }
    }, [data, chatId]);

    return (
      <>
        {!data || isLoading || isAILoading ? (
          <VStack
            flex={1}
            h={{ base: "100vh", sm: "100vh" }}
            w={{ base: "100%", sm: "100%" }}
            bgColor={"chatBackground"}
            p={5}
            position="relative"
          >
            <Center flex={1} h={"100%"}>
              <Spinner color="chatPrimary" />
            </Center>
            <Text
              position="absolute"
              bottom="10px"
              fontSize="xs"
              color="gray.500"
              textAlign="center"
              width="100%"
              pointerEvents="auto"
              zIndex={1}
            >
              Powered By <Link href="https://plyrchat.com.br/" target="_blank" color="chatPrimary" textDecoration="none" _hover={{ textDecoration: "underline" }}>PlyrChat</Link>
            </Text>
          </VStack>
        ) : (
          <VStack
            flex={1}
            h={{ base: "100vh", sm: "100vh" }}
            w={{ base: "100%", sm: "100%" }}
            bgColor={data.chatBgColor || "chatBackground"}
            p={5}
            position="relative"
          >
            {!chatSecureId || !sessionSecureId || !userSecureId ? (
              <>
                <Center flex={1}>
                  <Text fontSize={"2xl"} color={"chatTextColor"}>
                    Chat não encontrado
                  </Text>
                </Center>
              </>
            ) : (
              <SocketProvider>
                <ChatOrchestrator
                  chatData={data}
                  chatSecureId={chatSecureId}
                  isAi={isAI}
                  sessionSecureId={sessionSecureId}
                  userSecureId={userSecureId}
                />
              </SocketProvider>
            )}
            <Text
              position="absolute"
              bottom="1px"
              fontSize="xs"
              color="gray.500"
              textAlign="center"
              width="100%"
              pointerEvents="auto"
              zIndex={1}
            >
              Powered By <Link href="https://plyrchat.com.br/" target="_blank" color="chatPrimary" textDecoration="none" _hover={{ textDecoration: "underline" }}>PlyrChat</Link>
            </Text>
          </VStack>
        )}
      </>
    );
  } catch (error) {
    return (
      <VStack
        flex={1}
        h={{ base: "100vh", sm: "calc(100vh - 180px)" }}
        w={{ base: "100%%", sm: "600px" }}
        bgColor={"chatBackground"}
        p={5}
        position="relative"
      >
        <Center flex={1}>
          <Text fontSize={"2xl"} color={"chatTextColor"}>
            Chat não encontrado
          </Text>
        </Center>
        <Text
          position="absolute"
          bottom="1px"
          fontSize="xs"
          color="gray.500"
          textAlign="center"
          width="100%"
          pointerEvents="auto"
          zIndex={1}
        >
          Powered By <Link href="https://plyrchat.com.br/" target="_blank" color="chatPrimary" textDecoration="none" _hover={{ textDecoration: "underline" }}>PlyrChat</Link>
        </Text>
      </VStack>
    );
  }
}
