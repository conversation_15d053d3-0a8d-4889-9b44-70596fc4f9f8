type WidgetProps = {
  iconUrl: string;
  websiteUrl: string;
  chatId: string;
  btnColor: string;
};

export default function getWidgetScript({
  websiteUrl,
  btnColor,
  chatId,
  iconUrl,
}: WidgetProps): string {
  const replacements: { [key: string]: string } = {
    "{{urlIcon}}": iconUrl,
    "{{websiteUrl}}": websiteUrl,
    "{{chatId}}": chatId,
    "{{btnColor}}": btnColor,
  };

  var widget = `<script>
	window.PlyrChatWidgetConfig = {
			websiteUrl: "{{websiteUrl}}",
			btnColor: "{{btnColor}}",
			chatId: "{{chatId}}",
			iconUrl: "{{urlIcon}}",
	};
</script>
<script src="{{websiteUrl}}/chat-widget.js"></script>
`;

  widget = widget.replace(
    /{{urlIcon}}|{{websiteUrl}}|{{chatId}}|{{btnColor}}/g,
    (matched) => replacements[matched]
  );

  return widget;
}
