import { useState, useEffect, useCallback } from "react";
import { format } from "date-fns";
import { toaster } from "@/components/ui/toaster";
import useInfiniteChatMessages from "./useInfiniteChatMessages";

export interface Message {
  secureId: string;
  message: string;
  isMine: boolean;
  messageTime: string;
  avatarUrl?: string;
  type?: "text" | "audio" | "file" | "image";
  urlFile?: string;
  attendantName?: string;
  messageCount: number;
  replyTo?: string;
  replyMessage?: string;
}

export function useChatMessages({
  secureId,
  wsServiceRef,
  user,
  sessionInfo,
  isConnected,
  refetchSessionInfo,
	scrollToBottom,
}: {
  secureId: string;
  wsServiceRef: any;
  user: any;
  sessionInfo: any;
  isConnected: boolean;
  refetchSessionInfo?: () => void;
	scrollToBottom?: () => void;
}) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isAI, setIsAI] = useState(false);
  const [replyTo, setReplyTo] = useState<{ secureId: string; message: string } | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [prevScrollHeight, setPrevScrollHeight] = useState<number | null>(null);

  const {
    data,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteChatMessages(secureId);

  // Função para carregar histórico de mensagens
  const loadMessageHistory = useCallback(() => {
    const allMessages = data?.pages.flatMap((page) => page.data).reverse() ?? [];
    if (!allMessages) return [];
    const loadedMessages: Message[] = [];
    allMessages.forEach((message, index) => {
      const isMine = message.messageDirection === "sent";
      const previousMessage = index > 0 ? allMessages[index - 1] : null;
      const isSameSender = previousMessage
        ? previousMessage.messageDirection === message.messageDirection
        : false;
      const messageCount = isSameSender
        ? (loadedMessages[index - 1]?.messageCount ?? 0) + 1
        : 1;

      let replyMessage: string | undefined = undefined;
      if (message.replyTo) {
        const repliedMessage = allMessages.find(
          (m) => m.secureId === message.replyTo
        );
        if (repliedMessage) {
          const messageText = repliedMessage.receiveMessage
            ? repliedMessage.receiveMessage
            : repliedMessage.sendMessage;
          replyMessage = messageText ?? undefined;
        }
      }

      loadedMessages.push({
        isMine,
        secureId: message.secureId,
        urlFile: message.urlFile,
        message: (isMine ? message.sendMessage : message.receiveMessage) ?? "",
        messageTime: format(new Date(message.createdAt), "HH:mm"),
        attendantName: message.attendantName,
        messageCount,
        type: message.type,
        replyTo: message.replyTo,
        replyMessage: replyMessage,
      });
    });
    return loadedMessages;
  }, [data]);

  // Atualiza mensagens ao carregar dados
  useEffect(() => {
    if (data && sessionInfo) {
      setIsAI(sessionInfo.isAIResponder);
      setMessages(loadMessageHistory());
      if (isInitialLoad) {
        setIsInitialLoad(false);
      }
    } else if (sessionInfo && isAI !== sessionInfo.isAIResponder) {
      setIsAI(sessionInfo.isAIResponder);
    }
  }, [data, sessionInfo, secureId, loadMessageHistory]);

  // WebSocket listeners
  useEffect(() => {
    if (!isConnected || !user || !sessionInfo || !secureId || !refetch) {
      return;
    }
    setIsConnecting(true);
    wsServiceRef.removeAllListeners();
    wsServiceRef.onNewMessage((newMessage: any) => {
      setMessages((currentMessages) => {
        const messageExists = currentMessages.some(
          (message) => message.secureId === newMessage.secureId
        );
        if (messageExists) {
          return currentMessages;
        }
        const isMine = newMessage.messageDirection === "sent";
        const previousMessage = currentMessages[currentMessages.length - 1];
        const isSameSender = previousMessage
          ? previousMessage.isMine === isMine
          : false;
        const messageCount = isSameSender
          ? (previousMessage?.messageCount ?? 0) + 1
          : 1;
        let replyMessage: string | undefined = undefined;
        if (newMessage.replyTo) {
          const repliedMessage = currentMessages.find(
            (msg) => msg.secureId === newMessage.replyTo
          );
          if (repliedMessage) {
            replyMessage = repliedMessage.message;
          }
        }
        return [
          ...currentMessages,
          {
            secureId: newMessage.secureId,
            message:
              (isMine ? newMessage.sendMessage : newMessage.receiveMessage) ??
              "",
            isMine,
            messageTime: format(new Date(newMessage.createdAt), "HH:mm"),
            attendantName: newMessage.attendantName,
            type: newMessage.type,
            urlFile: newMessage.urlFile,
            messageCount: messageCount,
            replyTo: newMessage.replyTo,
            replyMessage: replyMessage,
          },
        ];
      });
			if (scrollToBottom) {
				scrollToBottom();
			}
    });
    wsServiceRef.onRefreshMessages(() => {
      refetch();
      if (refetchSessionInfo) refetchSessionInfo();
    });
    wsServiceRef
      .joinRoom(user.subject, secureId)
      .catch(() => {
        toaster.create({
          title: "Erro de Chat",
          description: "Não foi possível configurar a comunicação em tempo real.",
          type: "error",
          duration: 5000,
        });
      })
      .finally(() => setIsConnecting(false));
    return () => {
      if (wsServiceRef.isConnected() && user?.subject && secureId) {
        wsServiceRef.leaveRoom(user.subject, secureId);
        wsServiceRef.removeAllListeners();
      }
    };
  }, [isConnected, user, sessionInfo, secureId, wsServiceRef, refetch, refetchSessionInfo]);

  // Funções utilitárias
  const handleReplyToMessage = (secureId: string, message: string) => {
    setReplyTo({ secureId, message });
  };
  const cancelReply = () => setReplyTo(null);

  return {
    messages,
    isConnecting,
    isAI,
    replyTo,
    setReplyTo: handleReplyToMessage,
    cancelReply,
    isInitialLoad,
    setPrevScrollHeight,
    prevScrollHeight,
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    refetch,
    setIsAI,
  };
}
