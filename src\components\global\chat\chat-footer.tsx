import { HStack, Icon<PERSON>utton } from "@chakra-ui/react";
import { InputMessage } from "./input-message";
import { TbSend } from "react-icons/tb";
import { ChatRequestOptions } from "ai";

type ChatFooterProps = {
  ref: any;
  input: string;
  handleInputChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>
  ) => void;
  isLoading: boolean;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions
  ) => void;
};

export default function ChatFooter({
  ref,
  handleInputChange,
  input,
  isLoading,
  handleSubmit,
}: ChatFooterProps) {
  return (
    <HStack w={"100%"} as="form" onSubmit={handleSubmit}>
      <InputMessage
        name="message"
        ref={ref}
        value={input}
        rounded={20}
        placeholder="Digite a mensagem"
        onChange={handleInputChange}
      />
      <IconButton
        type="submit"
        rounded="full"
        bgColor={"chatPrimary"}
        color={"white"}
        _icon={{}}
        size={"xl"}
        borderWidth={2}
        transition={"all 0.3s"}
        _active={{
          bgColor: "transparent",
          borderColor: "chatPrimary",
          color: "chatPrimary",
          borderWidth: 2,
        }}
        disabled={isLoading || input.trim() === ""}
      >
        <TbSend />
      </IconButton>
    </HStack>
  );
}
