"use client";
import { InputIcon } from "@/components/global/inputs/input-icon";
import { Flex, VStack, Text } from "@chakra-ui/react";
import Link from "next/link";
import * as yup from "yup";
import React from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { IoMailOutline } from "react-icons/io5";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { Button } from "@/components/ui/button";

const ForgotPasswordSchema = yup.object().shape({
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
});

type ForgotPasswordFormData = yup.InferType<typeof ForgotPasswordSchema>;

export default function ForgotPassword() {
  const {
    register,
    handleSubmit,
    formState,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: yupResolver(ForgotPasswordSchema),
  })

  const recoveryPassword = useMutation({
    mutationFn: async (data: any) => {
      return await api.post(`/forgot-password`, {
        email: data.email,
        redirectUrl: process.env.NEXT_PUBLIC_REDIRECT_URL
      });
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Verifique o email para continuar a recuperação da senha",
      })
    },
		onError: (error: any) => {
      // toaster.error({
      //   title: "Erro",
      //   description: "E-mail inválido!",
      // })
      console.error("Erro ao atualizar usuário:", error);
		},
  })
  const handleRecovery = async (data: any) => {
    try {
      await recoveryPassword.mutateAsync(data);
    } catch (error) { }
  }

  return (
    <Flex flex={1} justifyContent={"center"} alignSelf={"center"}>
      <VStack
        h={{base: "auto", sm: "sm", md:"md" }}
        w={{ base: "80%", md: "lg" }}
        m={{ base: 5, md: 0 }}
        bgColor={"chatLoginCardBackground"}
        borderRadius={20}
        justify={"center"}
        align={"center"}
        gap={{ base: 5, md:10 }}
      >
        <Flex
          mt={{ base: 5, md: 0 }}
          h={{ base: "55px", md: "70px" }}
          w={{ base: "206px", md: "306px" }}
          bgImage="url(/logo.png)"
          bgPos="initial"
          bgRepeat="no-repeat"
          bgSize="contain"
        />
        <VStack
          as="form"
          w={{ base: "80%", md: "100%" }}
          px={{ md: 20 }}
          onSubmit={handleSubmit(handleRecovery)}
          gap={{ base: 4, md:6 }}
        >
          <VStack gap={1} textAlign={"center"}>
            <Text color={"chatPrimary"} fontWeight={"bold"} fontSize={"2xl"}>Esqueceu sua senha?</Text>
            <Text color={"text"} justifySelf={"center"} fontSize={"md"}>Informe o e-mail para o qual deseja redefinir sua senha</Text>
          </VStack>

          <InputIcon
            startElement={<IoMailOutline />}
            borderRadius={20}
            placeholder="Digite seu Email"
            size={"md"}
            {...register("email")}
            error={errors.email}
          />
          <VStack gap={2} w={"100%"}>
          <Button
            children={"ENVIAR"}
            loading={formState.isSubmitting}
            bgColor={"chatPrimary"}
            w={"100%"}
            borderRadius={"full"}
            color={"#FFF"}
            fontWeight={"bold"}
            type="submit"
            transitionDuration={"0.2s"}
            _hover={{
              color: "chatPrimary",
              bgColor: "transparent",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
          />
          <Text
            fontSize="sm"
            color="text"
            _hover={{
              color: "chatPrimary",
            }}
          >
            <Link href="/">Voltar para login</Link>
          </Text>
          </VStack>
        </VStack>
      </VStack>
    </Flex>
  );
}
