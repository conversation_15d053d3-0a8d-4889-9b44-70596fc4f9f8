"use client";
import { Avatar } from "@/components/ui/avatar";
import { Box, Flex, HStack, Text } from "@chakra-ui/react";
import NavLinks from "../../../app/(dashboard)/backoffice/components/navlinks";
import Link from "next/link";
import Cookies from "js-cookie";
import { MenuContent, MenuItem, MenuRoot, MenuTrigger } from "@/components/ui/menu";
import { useRouter } from "next/navigation";
import HasPermission from "@/utils/funcs/has-permission";
import { APPPERMISSIONS } from "@/utils/types/permissions/all-attendant-permissions";

type SideBarProps = {
  children: React.ReactNode;
  name: string;
  url: string;
};

export default function SideBar({ children, name, url }: SideBarProps) {
  const router = useRouter();

  const handleLogout = () => {
    Cookies.remove("__PlyrChat_Token");
    router.push("/");
  };

  return (
    <Flex
      as="aside"
      position="fixed"
      top={0}
      left={0}
      w={{ "2xl": "20%", md: "25%" }}
      hideBelow={"md"}
      h="100vh"
      flexDirection="column"
      justifyContent="space-between"
      alignItems="center"
      zIndex={10}
      css={{
        "&::-webkit-scrollbar": {
          display: "none",
        },
      }}
    >
      {children}
      <Box
        position="fixed"
        bottom={5}
        left={5}
        zIndex={999}
      >
        <HStack justifyContent={"flex-start"} w="100%">
          <MenuRoot>
            <MenuTrigger
              _hover={{ cursor: "pointer" }}
              outline={"none"}
            >
              <HStack>
                <Avatar name={name} size={"lg"} bgColor={'#5e5e5e'} />
                <Text fontSize="sm" fontWeight="bold">
                  {name}
                </Text>
              </HStack>
            </MenuTrigger>
            <MenuContent
              outline={"none"}
              shadow={"md"}
            >
              <MenuItem
                value="my-account"
                _hover={{
                  cursor: "pointer",
                  color: "chatPrimary",
                }}
                onClick={() => router.push("/backoffice/profile")}
              >
                Minha Conta
              </MenuItem>
              {HasPermission(APPPERMISSIONS.VIEW) && (
                <MenuItem
                  value="app"
                  _hover={{
                    cursor: "pointer",
                    color: "chatPrimary",
                  }}
                  onClick={() => router.push("/app/dashboard")}
                >
                  App
                </MenuItem>
              )}
              <MenuItem
                value="logout"
                _hover={{
                  cursor: "pointer",
                  color: "chatPrimary",
                }}
                onClick={handleLogout}
              >
                Deslogar
              </MenuItem>
            </MenuContent>
          </MenuRoot>
        </HStack>
      </Box>
    </Flex>
  );
}
