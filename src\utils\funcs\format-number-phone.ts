export function formatCellphone(phoneNumber: string): string {
  // Remove all non-numeric characters
  const numericOnly = phoneNumber.replace(/\D/g, '');
  
  // Handle empty input
  if (!numericOnly) return phoneNumber;
  
  // Check for country code
  let countryCode = '';
  let remainingDigits = numericOnly;
  
  // Check for Brazilian country code (55)
  if (numericOnly.startsWith('55') && numericOnly.length > 2) {
    countryCode = '55';
    remainingDigits = numericOnly.substring(2);
  } else if (numericOnly.startsWith('1') && numericOnly.length > 1) {
    // US/Canada country code
    countryCode = '1';
    remainingDigits = numericOnly.substring(1);
  } else if (numericOnly.length > 11) {
    // Assume first 1-3 digits are country code for other international numbers
    countryCode = numericOnly.substring(0, numericOnly.length - 10 > 3 ? 3 : numericOnly.length - 10);
    remainingDigits = numericOnly.substring(countryCode.length);
  }
  
  // Format based on remaining digits (assumes DDD + local number)
  if (remainingDigits.length === 11) {
    // Brazilian format with 9-digit cellphone
    return countryCode ? 
      `+${countryCode} (${remainingDigits.substring(0, 2)}) ${remainingDigits.substring(2, 7)}-${remainingDigits.substring(7)}` :
      `(${remainingDigits.substring(0, 2)}) ${remainingDigits.substring(2, 7)}-${remainingDigits.substring(7)}`;
  } else if (remainingDigits.length === 10) {
    // Format with area code + 8-digit number
    return countryCode ?
      `+${countryCode} (${remainingDigits.substring(0, 2)}) ${remainingDigits.substring(2, 6)}-${remainingDigits.substring(6)}` :
      `(${remainingDigits.substring(0, 2)}) ${remainingDigits.substring(2, 6)}-${remainingDigits.substring(6)}`;
  } else if (remainingDigits.length === 9) {
    // Just 9-digit number (might be missing area code)
    return countryCode ?
      `+${countryCode} ${remainingDigits.substring(0, 5)}-${remainingDigits.substring(5)}` :
      `${remainingDigits.substring(0, 5)}-${remainingDigits.substring(5)}`;
  } else if (remainingDigits.length === 8) {
    // Just 8-digit number
    return countryCode ?
      `+${countryCode} ${remainingDigits.substring(0, 4)}-${remainingDigits.substring(4)}` :
      `${remainingDigits.substring(0, 4)}-${remainingDigits.substring(4)}`;
  }
  
  // If it doesn't match expected formats, return with country code if detected
  return countryCode ? `+${countryCode} ${remainingDigits}` : phoneNumber;
}
