import { api } from "@/services/api";
import { ChatMessagesDto, ChatSessionsMessagesOutputDto } from "@/utils/types/DTO/chat-sessions.dto";
import { useInfiniteQuery } from "@tanstack/react-query";

async function getChatMessages({
  sessionId,
  pageParam = 1,
  pageSize = 20,
}: {
  sessionId: string;
  pageParam?: number;
  pageSize?: number;
}) {
  const { data } = await api.get<ChatMessagesDto>(`/sessions/messages/${sessionId}`, {
    params: {
      page: pageParam,
      limit: pageSize,
    },
  });
  const messages: ChatSessionsMessagesOutputDto[] = data.data.map((message) => {
    return {
      secureId: message.secureId,
      sendMessage: message.sendMessage,
      receiveMessage: message.receiveMessage,
      type: message.messageType,
      messageDirection: message.messageDirection,
      urlFile: message.upload?.urlCdn,
      attendantName: message.userAccount?.user.name,
      replyTo: message.replyTo?.secureId,
      createdAt: message.createdAt,
    };
  });

  return {
    data: messages,
    meta: {
      totalItems: data.meta.totalItems,
      totalPages: data.meta.totalPages,
      currentPage: data.meta.currentPage,
      itemsPerPage: data.meta.itemsPerPage,
    },
  };
}

export default function useInfiniteChatMessages(sessionId: string) {
  return useInfiniteQuery({
    queryKey: ["chat-messages", sessionId],
    queryFn: ({ pageParam = 1 }) => getChatMessages({ sessionId, pageParam }),
    getNextPageParam: (lastPage) => {
      const { currentPage, totalPages } = lastPage.meta;
      if (currentPage < totalPages) {
        return currentPage + 1;
      }
      return undefined;
    },
    enabled: !!sessionId,
    initialPageParam: 1,
  });
}
