// filepath: /C:/dev/jobs/plyrchat/plyrchat-frontend/src/app/(dashboard)/app/chatbot/create/components/flows.tsx
import React, { useCallback } from "react";
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  addEdge,
  Controls,
  Background,
  MiniMap,
  Node,
  Edge,
  Connection,
  MarkerType,
} from "@xyflow/react";

import "@xyflow/react/dist/style.css";
import { Flex, Button } from "@chakra-ui/react";

const initialNodes: Node[] = [
  {
    id: "1",
    type: "question",
    position: { x: 250, y: 5 },
    data: { label: "Pergunta: Qual é o seu nome?" },
  },
];

const initialEdges: Edge[] = [];

export default function Flows() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Connection) =>
      setEdges((eds) =>
        addEdge({ ...params, markerEnd: { type: MarkerType.Arrow } }, eds)
      ),
    []
  );

  const addQuestionNode = () => {
    const newNode: Node = {
      id: `${nodes.length + 1}`,
      type: "question",
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: { label: "Nova Pergunta" },
    };
    setNodes((nds) => nds.concat(newNode));
  };

  const addAnswerNode = () => {
    const newNode: Node = {
      id: `${nodes.length + 1}`,
      type: "answer",
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: { label: "Nova Resposta" },
    };
    setNodes((nds) => nds.concat(newNode));
  };

  const nodeTypes = {
    question: ({ data }: any) => (
      <div
        style={{
          padding: 10,
          background: "#FFFFFF", // Fundo branco para melhor contraste
          borderRadius: 5,
          border: "1px solid #000000", // Borda preta
          color: "#000000", // Texto preto
        }}
      >
        <strong>{data.label}</strong>
      </div>
    ),
    answer: ({ data }: any) => (
      <div
        style={{
          padding: 10,
          background: "#FFFFFF", // Fundo branco para melhor contraste
          borderRadius: 5,
          border: "1px solid #000000", // Borda preta
          color: "#000000", // Texto preto
        }}
      >
        <span>{data.label}</span>
      </div>
    ),
  };

  return (
    <Flex
      flex={1}
      h="100%"
      bgColor="chatCardBackground"
      p="5"
      rounded="2xl"
      border="none"
      flexDirection="column"
    >
      <Flex mb={4}>
        <Button onClick={addQuestionNode} mr={2}>
          Adicionar Pergunta
        </Button>
        <Button onClick={addAnswerNode} mr={2}>
          Adicionar Resposta
        </Button>
      </Flex>
      <div style={{ height: "100%", width: "100%" }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          // snapGrid={snapGrid}
          fitView
        >
          <MiniMap />
          <Controls
            style={{
              color: "#000000",
            }}
          />
          <Background />
        </ReactFlow>
      </div>
    </Flex>
  );
}
