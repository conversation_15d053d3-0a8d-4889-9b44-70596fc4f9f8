import {
  Input as ChakraInput,
  InputProps as ChakraInputProps,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  height?: string;
  color?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  { name, label, error, startElement, endElement, height, labelColor, color, ...rest },
  ref
) => {
  color = color ? color : "chatPrimary";
  return (
    <Field
      invalid={!!error}
      h={height ? height : "60px"}
      errorText={error?.message}
      label={label}
      labelColor={labelColor}
    >
      <InputGroup w="100%" startElement={startElement} endElement={endElement}>
        <ChakraInput
          id={name}
          name={name}
          ref={ref}
          bg="white"
          color={color}
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: error ? "red.400" : color,
          }}
          _placeholder={{ color: "#B1A0A5" }}
          _focus={{
            borderColor: error ? "red.500" : color,
          }}
          size="md"
          {...rest}
        />
      </InputGroup>
    </Field>
  );
};

export const Input = forwardRef(InputBase);
