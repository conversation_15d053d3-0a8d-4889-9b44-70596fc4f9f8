"use client";
import { ChakraProvider, defaultSystem } from "@chakra-ui/react";
import createSystem from "@/styles/theme";
import ReactQueryProvider from "./ReactQueryProvider";
import {
  ColorModeProvider,
  type ColorModeProviderProps,
} from "../components/ui/color-mode";
import { AuthProvider } from "./AuthProvider";
import { Toaster } from "@/components/ui/toaster";

export function Provider(props: ColorModeProviderProps) {
  return (
    <ReactQueryProvider>
      <AuthProvider>
        <ChakraProvider value={createSystem}>
          <ColorModeProvider {...props} />
          <Toaster />
        </ChakraProvider>
      </AuthProvider>
    </ReactQueryProvider>
  );
}
