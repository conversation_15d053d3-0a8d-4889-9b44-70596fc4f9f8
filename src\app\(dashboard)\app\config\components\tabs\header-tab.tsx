import { Button } from "@/components/ui/button";
import { Box, HStack, Text } from "@chakra-ui/react";

type HeaderTabProps = {
  title: string;
  buttonTitle?: string;
  isSubmit?: boolean;
  onClick?: () => void;
  submit?: boolean;
};

export default function HeaderTab({
  buttonTitle,
  title,
  isSubmit,
  onClick,
  submit,
}: HeaderTabProps) {
  return (
    <HStack w={"100%"} justifyContent={"space-between"}>
      <Text color="chatTextColor" fontSize={"xl"} fontWeight={"medium"}>
        {title}
      </Text>
      <Box>
        {buttonTitle && (
          <Button
            type={submit ? "submit" : "button"}
            w={"100%"}
            borderRadius={20}
            size={"md"}
            fontWeight="700"
            bgColor="chatPrimary"
            color="white"
            loading={isSubmit}
            onClick={onClick}
            transitionDuration={"0.2s"}
            _hover={{
              color: "chatPrimary",
              bgColor: "transparent",
              borderColor: "chatPrimary",
            }}
            _active={{
              transform: "scale(0.95)",
            }}
          >
            {buttonTitle}
          </Button>
        )}
      </Box>
    </HStack>
  );
}
