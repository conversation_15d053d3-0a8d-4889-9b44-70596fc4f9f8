import { MetaDTO } from "./meta.dto";

export type GetAllPlansDto = {
  meta: MetaDTO;
  data: GetPlanDto[];
}

type GetPlanDto = {
  secureId: string;
  attendantsLimit: number;
  whatsappNumberLimit: number;
  chatbotsLimit: number;
  knowledgeBaseLimit: number;
  iaMessagesLimit: number;
  interval: number;
  trialDays: number;
  name: string;
  slug: string;
  description: string;
  price: string;
  details: string;
  efiPlanId: number;
  efiIsDeleted: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}