"use client";
import { Ava<PERSON> } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  DrawerActionTrigger,
  DrawerBackdrop,
  DrawerBody,
  DrawerCloseTrigger,
  Drawer<PERSON>ontent,
  Drawer<PERSON>ooter,
  Drawer<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Box, HStack, IconButton, Stack, Text, VStack } from "@chakra-ui/react";
import { IoArrowBackOutline } from "react-icons/io5";
import { RxHamburgerMenu } from "react-icons/rx";
import { NavLink } from "../navlink/navlink";
import { BiHomeAlt } from "react-icons/bi";
import NavButtons from "@/app/(dashboard)/app/components/navbuttons";
import { FiMessageCircle, FiPieChart, FiUser, FiSettings } from "react-icons/fi";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LuBrainCircuit, LuChartNoAxesCombined } from "react-icons/lu";
import { FaWhatsapp } from "react-icons/fa";
import { useAuthContext } from "@/providers/AuthProvider";
import {
  ATTENDANTPERMISSIONS,
  CHATBOTPERMISSIONS,
  CHATPERMISSIONS,
  CONFIGPERMISSIONS,
  CONTACTPERMISSIONS,
  DASHBOARDPERMISSIONS,
  REPORTPERMISSIONS,
  WEBCHATPERMISSIONS,
  WHATSAPPERMISSIONS,
} from "@/utils/types/permissions/all-attendant-permissions";
import HasPermission from "@/utils/funcs/has-permission";
import Cookies from "js-cookie";
import { MenuContent, MenuItem, MenuRoot, MenuTrigger } from "@/components/ui/menu";
import { usePathname, useRouter } from "next/navigation";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { useState, useEffect } from "react";

type AppDrawerMenuProps = {
  name: string;
  url?: string;
};

export default function AppDrawerMenu({ name, url }: AppDrawerMenuProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [currentHash, setCurrentHash] = useState<string>("");
  const [currentUrl, setCurrentUrl] = useState<string>("");

  // Track hash changes for active state - same logic as in tabs
  useEffect(() => {
    const updateHashAndUrl = () => {
      if (typeof window !== "undefined") {
        const newUrl = window.location.href;
        const newHash = window.location.hash;

        if (newUrl !== currentUrl) {
          setCurrentUrl(newUrl);
          setCurrentHash(newHash);
        }
      }
    };

    // Initial setup
    updateHashAndUrl();

    // Listen for hash changes
    const handleHashChange = () => {
      updateHashAndUrl();
    };

    // Listen for navigation events
    const handlePopState = () => {
      updateHashAndUrl();
    };

    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("popstate", handlePopState);

    // Poll for URL changes as a fallback (Next.js Link might not trigger events)
    const checkUrlInterval = setInterval(updateHashAndUrl, 100);

    return () => {
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("popstate", handlePopState);
      clearInterval(checkUrlInterval);
    };
  }, [currentUrl]);

  const handleLogout = () => {
    Cookies.remove("__PlyrChat_Token");
    router.push("/");
  };

  return (
    <Box
      hideFrom={"md"}
      w={"100%"}
      position={"absolute"}
      top={0}
      pt={5}
      zIndex={100}
      bgColor={"chatCardBackground"}
    >
      <HStack w={"100%"} justifyContent={"space-around"}>
        <DrawerRoot placement={"start"}>
          <DrawerBackdrop />
          <DrawerTrigger asChild>
            <IconButton
              aria-label="Go Back"
              rounded="full"
              bgColor={"chatPrimary"}
              color={"white"}
              size={"lg"}
              borderWidth={2}
              _active={{
                bgColor: "transparent",
                borderColor: "chatPrimary",
                color: "chatPrimary",
                borderWidth: 2,
              }}
            >
              <RxHamburgerMenu />
            </IconButton>
          </DrawerTrigger>
          <DrawerContent bgColor={"white"}>
            <DrawerBody>
              <Stack flex={1} alignContent="center" w="100%" p={3}>
                <Box
                  w="100%"
                  my={7}
                  h={"9"}
                  bgImage="url(/logo.png)"
                  bgPos="center"
                  bgRepeat="no-repeat"
                  bgSize="contain"
                />
                <VStack gap={8} align={"center"} justify={"center"} w="100%">
                  {HasPermission(DASHBOARDPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<LuChartNoAxesCombined size={"30"} />}
                      label={"Dashboard"}
                      redirect={"/app/dashboard"}
                      active={pathname === "/app/contacts"}
                    />
                  )}
                  {HasPermission(CHATPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<FiMessageCircle size={"30"} />}
                      label="Chats"
                      redirect={"/app/chat"}
                      active={pathname === "/app/chat"}
                    />
                  )}

                  {HasPermission(CONTACTPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<LuBookUser size={"30"} />}
                      label="Contatos"
                      redirect={"/app/contacts"}
                      active={pathname === "/app/contacts"}
                    />
                  )}

                  {/* Config Section */}
                  {HasPermission(CONFIGPERMISSIONS.VIEW) && (
                    <>
                      {HasPermission(WHATSAPPERMISSIONS.VIEW) && (
                        <NavButtons
                          icon={<FaWhatsapp size={"30"} />}
                          label="WhatsApp"
                          redirect={"/app/config#whatsapp"}
                          active={pathname === "/app/config" && currentHash === "#whatsapp"}
                        />
                      )}
                      {HasPermission(WEBCHATPERMISSIONS.VIEW) && (
                        <NavButtons
                          icon={<FiMessageCircle size={"30"} />}
                          label="WebChats"
                          redirect={"/app/config#webchat"}
                          active={pathname === "/app/config" && currentHash === "#webchat"}
                        />
                      )}
                      {HasPermission(CHATBOTPERMISSIONS.VIEW) && (
                        <NavButtons
                          icon={<LuBrainCircuit size={"30"} />}
                          label="ChatBots"
                          redirect={"/app/config#chatbot"}
                          active={pathname === "/app/config" && currentHash === "#chatbot"}
                        />
                      )}
                      {HasPermission(ATTENDANTPERMISSIONS.VIEW) && (
                        <NavButtons
                          icon={<FiUser size={"30"} />}
                          label="Atendentes"
                          redirect={"/app/config#attendant"}
                          active={pathname === "/app/config" && currentHash === "#attendant"}
                        />
                      )}
                    </>
                  )}
                  {/* {HasPermission(CHATBOTPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<LuBrainCircuit size={"30"} />}
                      label={"ChatBots"}
                      redirect={"/app/chatbot"}
                    />
                  )} */}
                  {/* {HasPermission(REPORTPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<FiPieChart size={"30"} />}
                      label={"Relatórios"}
                      redirect={"/app/reports"}
                    />
                  )} */}
                  {/* {HasPermission(ATTENDANTPERMISSIONS.VIEW) && (
                    <NavButtons
                      icon={<FiUser size={"30"} />}
                      label={"Usuários"}
                      redirect={"/app/users"}
                    />
                  )} */}
                </VStack>
              </Stack>
            </DrawerBody>
            <DrawerCloseTrigger color={"chatPrimary"} />
          </DrawerContent>
        </DrawerRoot>

        <Box
          w="40%"
          my={2}
          h={"9"}
          bgImage="url(/logo.png)"
          bgPos="center"
          bgRepeat="no-repeat"
          bgSize="contain"
        />

        <MenuRoot>
          <MenuTrigger
            _hover={{ cursor: "pointer" }}
            outline={"none"}
          >
            <Avatar
              bgColor={'#5e5e5e'}
              name={name}
              size={"lg"}
              borderWidth={2}
              borderColor={"transparent"}
              _active={{
                bgColor: "transparent",
                borderColor: "chatPrimary",
                color: "chatPrimary",
                borderWidth: 2,
              }}
            />
          </MenuTrigger>
          <MenuContent
            bgColor={"chatCardBackground"}
            outline={"none"}
            shadow={"md"}
          >
            <MenuItem
              value="my-account"
              bgColor={"chatCardBackground"}
              color={"chatTextColor"}
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={() => router.push("/app/profile")}
            >
              Minha Conta
            </MenuItem>
            {HasBackofficePermission(BACKOFFICEPERMISSIONS.VIEW) && (
              <MenuItem
                value="backoffice"
                bgColor={"chatCardBackground"}
                color={"chatTextColor"}
                _hover={{
                  cursor: "pointer",
                  color: "chatPrimary",
                }}
                onClick={() => router.push("/backoffice/dashboard")}
              >
                Backoffice
              </MenuItem>
            )}
            <MenuItem
              value="logout"
              bgColor={"chatCardBackground"}
              color={"chatTextColor"}
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={handleLogout}
            >
              Deslogar
            </MenuItem>
          </MenuContent>
        </MenuRoot>
      </HStack>
    </Box>
  );
}
