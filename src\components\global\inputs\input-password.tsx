import {
  Input as ChakraInput,
  InputProps as ChakraInputProps,
  IconButton,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction, useState } from "react";
import { FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";
import { LuEye, LuEyeOff } from "react-icons/lu";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  error?: FieldError;
  comments?: string;
  type?: "text" | "email" | "password";
  startElement?: React.ReactNode;
  height?: string;
  color?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  { name, label, error, type = "password", startElement, height, color= "chatPrimary", ...rest },
  ref
) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Field
      invalid={!!error}
      h={height ? height : "60px"}
      errorText={error?.message}
      label={label}
    >
      <InputGroup
        w={"100%"}
        startElement={startElement}
        endElement={
          type === "password" && (
            <IconButton
              aria-label={showPassword ? "Hide password" : "Show password"}
              variant="ghost"
              onClick={() => setShowPassword(!showPassword)}
              size="sm"
              color={"gray.400"}
              _hover={{ color: color, bgColor: "transparent" }}
            >
              {showPassword ? <LuEyeOff /> : <LuEye />}
            </IconButton>
          )
        }
      >
        <ChakraInput
          id={name}
          name={name}
          ref={ref}
          type={
            type === "password" ? (showPassword ? "text" : "password") : type
          }
          bg="white"
          color={color}
          borderRadius={10}
          borderColor="#D6D6D6"
          borderWidth={2}
          _hover={{
            borderColor: error ? "red.400" : color,
          }}
          _placeholder={{ color: "#B1A0A5" }}
          _focus={{
            borderColor: error ? "red.500" : color,
          }}
          size="md"
          {...rest}
        />
      </InputGroup>
    </Field>
  );
};

export const InputPassword = forwardRef(InputBase);
