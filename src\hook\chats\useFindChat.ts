import { api } from "@/services/api";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import { useQuery } from "@tanstack/react-query";

async function findChat(secureId: string) {
  const { data } = await api.get<GetOneChatDtoInput>(`/chats/${secureId}`);
  return data;
}

export default function useFindChat(secureId: string) {
  return useQuery({
    queryFn: async () => await findChat(secureId),
    queryKey: ["findChat", secureId],
  });
}
