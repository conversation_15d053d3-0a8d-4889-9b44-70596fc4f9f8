import DefaultButton from "@/components/global/buttons/button";
import { SwitchLabel } from "@/components/global/inputs/switch";
import { Switch } from "@/components/ui/switch";
import { toaster } from "@/components/ui/toaster";
import { Tooltip } from "@/components/ui/tooltip";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import truncateText from "@/utils/funcs/truncate-text";
import {
  Box,
  Button,
  Grid,
  GridItem,
  HStack,
  VStack,
  Icon,
  Text,
} from "@chakra-ui/react";

import { format } from "date-fns";
import { useState } from "react";
import { LuMessageCirclePlus, LuPencil } from "react-icons/lu";
import { CreateNewSession } from "./create-new-session";
import { UpdateContactModal } from "./update-contact-modal";

type ContactCardProps = {
  secureId: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  customerDocument?: string;
  lastUpdate?: Date;
  isActive?: boolean;
};

export default function ContactCard({
  secureId,
  lastUpdate,
  customerName,
  isActive,
  customerEmail,
  customerPhone,
  customerDocument,
}: ContactCardProps) {
  const [active, setActive] = useState(isActive || false);
  const [modalStartSession, setModalStartSession] = useState(false);
  const [modalUpdateContact, setModalUpdateContact] = useState(false);

  const handleUpdateStatus = async () => {
    try {
      await api.patch(`/contact/${secureId}`, { isActive: !active });
      toaster.success({
        title: "Sucesso",
        description: !active
          ? "Contato ativado com sucesso!"
          : "Contato desativado com sucesso!",
      });
      // queryClient.invalidateQueries({queryKey:["list-contacts"]});
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <Box
        key={secureId}
        p={5}
        color={"chatTextColor"}
        bgColor={"chatBackground"}
        rounded={"2xl"}
        width="100%"
      >
        {/* Desktop Layout */}
        <Grid
          fontSize={{ base: "xs", md: "sm" }}
          templateColumns={`repeat(6, 1fr)`}
          color={"chatTextColor"}
          gap={4}
          display={{ base: "none", md: "grid" }}
        >
          <GridItem display="flex" alignItems="center">
            <Text textAlign={"left"}>
              {customerName ? truncateText(customerName, 22) : "-"}
            </Text>
          </GridItem>
          <GridItem display="flex" alignItems="center">
            <Tooltip content={customerEmail}>
              <Text textAlign={"left"}>
                {customerEmail ? truncateText(customerEmail, 21) : "-"}
              </Text>
            </Tooltip>
          </GridItem>
          <GridItem display="flex" alignItems="center">
            <Text textAlign={"left"}>
              {customerPhone ? customerPhone : "-"}
            </Text>
          </GridItem>
          <GridItem display="flex" alignItems="center" justifyContent="center">
            <Text textAlign={"center"}>
              {customerDocument ? customerDocument : "-"}
            </Text>
          </GridItem>
          <GridItem display="flex" alignItems="center" justifyContent="center">
            <Text textAlign={"center"}>
              {lastUpdate ? format(lastUpdate, "dd/MM/yy HH:mm") : "-"}
            </Text>
          </GridItem>
          <GridItem display="flex" alignItems="center">
            <HStack justify={"space-around"} alignItems={"center"} width="100%">
              <DefaultButton
                disabled={!customerPhone}
                size={"sm"}
                onClick={() => setModalStartSession(true)}
                buttonColor="chatPrimary"
                isRounded={true}
                tooltipContent={
                  customerPhone
                    ? "Iniciar conversa"
                    : "Não é possível iniciar conversa sem um número de telefone"
                }
              >
                <LuMessageCirclePlus color="fff" />
              </DefaultButton>
              <DefaultButton
                size={"sm"}
                onClick={() => setModalUpdateContact(true)}
                buttonColor="chatPrimary"
                isRounded={true}
                tooltipContent={"Editar contato"}
              >
                <LuPencil color="fff" />
              </DefaultButton>
              <Switch
                name="isActive"
                checked={active}
                onCheckedChange={({ checked }) => {
                  setActive(checked);
                  handleUpdateStatus();
                }}
              />
            </HStack>
          </GridItem>
        </Grid>

        {/* Mobile Layout */}
        <Box display={{ base: "block", md: "none" }}>
          <VStack align="stretch" gap={3}>
            {/* Nome e Switch */}
            <HStack justify="space-between" align="center">
              <Text fontSize="md" fontWeight="bold" color="chatPrimary">
                {customerName ? truncateText(customerName, 25) : "-"}
              </Text>
              <Switch
                name="isActive"
                checked={active}
                onCheckedChange={({ checked }) => {
                  setActive(checked);
                  handleUpdateStatus();
                }}
              />
            </HStack>

            {/* Informações de contato */}
            <VStack align="stretch" gap={2}>
              {customerEmail && (
                <HStack>
                  <Text fontSize="xs" color="chatPrimary" fontWeight="medium" minW="60px">
                    Email:
                  </Text>
                  <Text fontSize="xs" color="chatTextColor">
                    {truncateText(customerEmail, 30)}
                  </Text>
                </HStack>
              )}

              {customerPhone && (
                <HStack>
                  <Text fontSize="xs" color="chatPrimary" fontWeight="medium" minW="60px">
                    Celular:
                  </Text>
                  <Text fontSize="xs" color="chatTextColor">
                    {customerPhone}
                  </Text>
                </HStack>
              )}

              {customerDocument && (
                <HStack>
                  <Text fontSize="xs" color="chatPrimary" fontWeight="medium" minW="60px">
                    Doc:
                  </Text>
                  <Text fontSize="xs" color="chatTextColor">
                    {customerDocument}
                  </Text>
                </HStack>
              )}

              {lastUpdate && (
                <HStack>
                  <Text fontSize="xs" color="chatPrimary" fontWeight="medium" minW="60px">
                    Atualizado:
                  </Text>
                  <Text fontSize="xs" color="chatTextColor">
                    {format(lastUpdate, "dd/MM/yy HH:mm")}
                  </Text>
                </HStack>
              )}
            </VStack>

            {/* Botões de ação */}
            {/* No momento, mobile só ira ser para visualização */}
            {/* <HStack justify="center" gap={4} pt={2}>
              <DefaultButton
                disabled={!customerPhone}
                size={"sm"}
                onClick={() => setModalStartSession(true)}
                buttonColor="chatPrimary"
                isRounded={true}
                tooltipContent={
                  customerPhone
                    ? "Iniciar conversa"
                    : "Não é possível iniciar conversa sem um número de telefone"
                }
              >
                <LuMessageCirclePlus color="fff" />
              </DefaultButton>
              <DefaultButton
                size={"sm"}
                onClick={() => setModalUpdateContact(true)}
                buttonColor="chatPrimary"
                isRounded={true}
                tooltipContent={"Editar contato"}
              >
                <LuPencil color="fff" />
              </DefaultButton>
            </HStack> */}
          </VStack>
        </Box>
      </Box>
      <CreateNewSession
        open={modalStartSession}
        setOpen={setModalStartSession}
        contactSecureId={secureId}
      />
      <UpdateContactModal
        open={modalUpdateContact}
        setOpen={setModalUpdateContact}
        contact={{
          secureId,
          name: customerName,
          email: customerEmail,
          phone: customerPhone,
          document: customerDocument,
        }}
      />
    </>
  );
}
