import { Avatar } from "@/components/ui/avatar";
import { <PERSON>u<PERSON>ontent, <PERSON>uI<PERSON>, MenuRoot, MenuTrigger } from "@/components/ui/menu";
import HasBackofficePermission from "@/utils/funcs/has-permission-backoffice";
import { BACKOFFICEPERMISSIONS } from "@/utils/types/permissions/all-backoffice-permissions";
import { Flex, HStack, Icon, Text, VStack } from "@chakra-ui/react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { FaBell } from "react-icons/fa";
import Notifications from "./notifications";
import TrialBanner from "./trial-banner";

type HeaderProps = {
  name: string;
  url?: string;
  trialEndAt: Date | null;
};

export default function Header({ name, url, trialEndAt }: HeaderProps) {
  const router = useRouter();

  const handleLogout = () => {
    Cookies.remove("__PlyrChat_Token");
    router.push("/");
  };

  // Calculate days left in trial if trialEndAt exists
  const calculateDaysLeft = () => {
    if (!trialEndAt) return 7; // Default to 7 days if no trial end date

    const today = new Date();
    const endDate = new Date(trialEndAt);
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  };

  return (
    <Flex direction="column" mb={5} gap={4} hideBelow={"md"}>

      <HStack justifyContent={"space-between"}>
        <Flex
          h={"9"}
          w={"40"}
          bgImage="url(/logo.png)"
          bgPos="initial"
          bgRepeat="no-repeat"
          bgSize="contain"
        />
         {trialEndAt && <TrialBanner daysLeft={calculateDaysLeft()} />}
        <HStack justifyContent={"flex-start"}>
        <Notifications />

        <MenuRoot>
          <MenuTrigger
            _hover={{ cursor: "pointer" }}
            outline={"none"}
          >
            <HStack>
              <Text fontSize="sm" color={"chatTextColor"}>
                {name}
              </Text>
              <Avatar name={name} size={"lg"} bgColor={'#5e5e5e'} />
            </HStack>
          </MenuTrigger>
          <MenuContent
            bgColor={"chatCardBackground"}
            outline={"none"}
            shadow={"md"}
          >
            <MenuItem
              value="my-account"
              bgColor={"chatCardBackground"}
              color={"chatTextColor"}
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={() => router.push("/app/profile")}
            >
              Minha Conta
            </MenuItem>
            {HasBackofficePermission(BACKOFFICEPERMISSIONS.VIEW) && (
              <MenuItem
                value="backoffice"
                bgColor={"chatCardBackground"}
                color={"chatTextColor"}
                _hover={{
                  cursor: "pointer",
                  color: "chatPrimary",
                }}
                onClick={() => router.push("/backoffice/dashboard")}
              >
                Backoffice
              </MenuItem>
            )}
            <MenuItem
            value="plans"
            bgColor={"chatCardBackground"}
            color={"chatTextColor"}
            _hover={{
              cursor: "pointer",
              color: "chatPrimary",
            }}
            onClick={() => router.push("/app/choose-plans")}
            >
              Planos
            </MenuItem>
            <MenuItem
              value="logout"
              bgColor={"chatCardBackground"}
              color={"chatTextColor"}
              _hover={{
                cursor: "pointer",
                color: "chatPrimary",
              }}
              onClick={handleLogout}
            >
              Deslogar
            </MenuItem>
          </MenuContent>
        </MenuRoot>
      </HStack>
    </HStack>
    </Flex>
  );
}
