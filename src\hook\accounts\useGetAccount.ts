import { api } from "@/services/api";
import { GetAccountDto, GetAllAccountsDto } from "@/utils/types/DTO/accounts.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllAccounts(page: number) {
  const limit = 7
  const { data } = await api.get<GetAllAccountsDto>(`/accounts?page=${page}&limit=${limit}`); // TODO: change to backoffice/accounts
  return data;
}

export function useGetAllAccounts(page: number) {
  return useQuery({
    queryKey: ["accounts-backoffice", page],
    queryFn: async () => await getAllAccounts(page),
  });
}

async function getAccount(secureId: string) {
  const { data } = await api.get<GetAccountDto>(`/accounts/${secureId}`); // TODO: change to backoffice/accounts

  return data;
}

export function useGetAccount(secureId: string) {
  return useQuery({
    queryKey: ["accounts-backoffice", secureId],
    queryFn: async () => await getAccount(secureId),
  });
}
