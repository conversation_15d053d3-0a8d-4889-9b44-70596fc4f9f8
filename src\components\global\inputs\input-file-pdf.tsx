import { Field } from "@/components/ui/field";
import {
  Box,
  // FormControl,
  // FormErrorMessage,
  // FormLabel,
  HStack,
  Image,
  InputProps as ChakraInputProps,
  Progress,
  Text,
  VStack,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction } from "react";
import Dropzone from "react-dropzone";
import {
  Control,
  Controller,
  FieldError,
  FieldErrorsImpl,
  Merge,
  UseFormWatch,
} from "react-hook-form";
import { LuCamera, LuCloudUpload, LuFile } from "react-icons/lu";

interface InputProps extends Omit<ChakraInputProps, "size"> {
  name: string;
  control: Control<any>;
  watch: UseFormWatch<any>;
  defaultImage?: string;
  label?: string;
  uploadText?: string;
  error?: Merge<FieldError, FieldErrorsImpl<{}>>;
  progress?: number;
  nameFileActive?: string;
  labelColor?: string;
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = (
  {
    name,
    defaultImage,
    label,
    error = null,
    progress,
    nameFileActive,
    control,
    labelColor,
    uploadText,
    watch,
    ...rest
  },
  ref
) => {
  const file = watch(name) as File;

  return (
    <Field
      invalid={!!error}
      errorText={error?.message}
      label={label}
      //
      labelColor={labelColor}
      alignItems={"center"}
      justifyContent={"flex-start"}
    >
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Dropzone
            multiple={false}
            accept={{
              "file/pdf": [".pdf"],
              "file/md": [".md"],
              "file/txt": [".txt"],
              "file/doc": [".doc"],
            }}
            onDropAccepted={(e) => field.onChange(e[0])}
          >
            {({ getRootProps, getInputProps, isDragActive, isDragReject }) => (
              <Box
                border="1px dashed"
                borderColor={error ? "red.500" : "#D6D6D6"}
                borderRadius="2xl"
                cursor="pointer"
                width={"50%"}
                height={40}
                backgroundColor="#e9e9e9"
                transition="all 200ms ease"
                {...getRootProps()}
              >
                <input {...getInputProps()} />
                <HStack
                  justify="center"
                  align="center"
                  height="100%"
                  padding="3"
                >
                  {!file && defaultImage && (
                    <Image
                      src={defaultImage}
                      maxHeight="60px"
                      maxWidth="60px"
                      rounded={"full"}
                    />
                  )}
                  {file && (
                    <VStack>
                      <LuFile />
                      <Text>{file.name}</Text>
                    </VStack>
                  )}
                  {!isDragActive && !file && !defaultImage ? (
                    <VStack justifyContent={"center"}>
                      <LuCloudUpload
                        size={"30"}
                        // opacity={1
                        color="#FD2264"
                      />
                      <Text
                        fontSize={"md"}
                        color="chatTextColor"
                        textAlign={"center"}
                      >
                        {uploadText
                          ? uploadText
                          : "Solte a imagem aqui ou clique para o upload"}
                      </Text>
                    </VStack>
                  ) : isDragReject && !file ? (
                    <Text color="red.500">Arquivo não suportado</Text>
                  ) : (
                    !file &&
                    !defaultImage && (
                      <Box color={"red.200"}>
                        <LuCloudUpload color="#D6D6D6" />
                        <Text fontSize={"md"}>Solte a imagem aqui</Text>
                      </Box>
                    )
                  )}
                </HStack>
              </Box>
            )}
          </Dropzone>
        )}
      />
    </Field>
  );
};

export const InputUpload = forwardRef(InputBase);
