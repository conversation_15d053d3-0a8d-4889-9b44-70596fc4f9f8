export type ChatSessionsDto = {
  secureId: string;
  customerName: string;
  customerId: string;
  isActive: boolean;
  isDeleted: boolean;
  isRead: boolean;
  source: "whatsapp" | "webchat";
  chatMessages: MessagesDto[];
  account: {
    secureId: string;
  };
  whatsapp: {
    secureId: string;
    phoneNumber: string;
  }
  createdAt: string;
  updatedAt: string;
};

export type ChatMessagesDto = {
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    itemsPerPage: number;
  },
  data: MessagesDto[]
}

export type MessagesDto = {
  secureId: string;
  sendMessage?: string;
  receiveMessage?: string;
  messageType?: "text" | "audio" | "file" | "image";
  messageDirection?: "sent" | "received";
  upload: {
    secureId: string;
    urlCdn: string;
  };
  createdAt: string;
  userAccount?: {
    user: {
      name: string;
    };
  };
  replyTo?: {
    secureId: string;
    sendMessage: string;
    receiveMessage: string;
    messageType: string;
    messageDirection: string;
  };
};

export type ChatSessionsMessagesOutputDto = {
  secureId: string;
  receiveMessage?: string;
  sendMessage?: string;
  type?: "text" | "audio" | "file" | "image";
  messageDirection?: "sent" | "received";
  urlFile?: string;
  attendantName?: string;
  replyTo?: string;
  createdAt: string;
};

export type ChatSessionDto = {
  secureId: string;
  customerId: string;
  customerName: string;
  customerEmail?: string;
  customerDocument?: string;
  customerPhone?: string;
  sessionDescription?: string;
  isAIResponder: boolean;
  isArchived: boolean;
  isFinalized: boolean;
  isActive: boolean;
  isDeleted: boolean;
  chatMessages: MessagesDto[];
  account: {
    secureId: string;
  };
  chat?: {
    secureId: string;
  };
  createdAt: string;
  updatedAt: string;
  attendant: {
    secureId: string;
    user: {
      name: string;
      secureId: string;
    };
  } | null;
};

export type ChatSessionSessionsInfosDto = {
  chatId?: string;
  accountId: string;
  customerId: string;
  customerName: string;
  customerEmail?: string;
  customerDocument?: string;
  isAIResponder: boolean;
  isArchived: boolean;
  isFinalized: boolean;
  customerPhone?: string;
  sessionDescription?: string;
  attendantSecureId?: string;
  attendantName?: string;
};
