import DefaultButton from "@/components/global/buttons/button";
import { Input } from "@/components/global/inputs/input";
import { InputSearch } from "@/components/global/inputs/input-search";
import { Box, HStack, VStack, Text } from "@chakra-ui/react";

type HeaderTabProps = {
  title: string;
  onSearchChange: (value: string) => void;
  openCreateModal?: () => void;
};

export default function HeaderTab({
  title,
  onSearchChange,
  openCreateModal
}: HeaderTabProps) {
  return (
    <>
      {/* Desktop Layout */}
      <HStack
        w={"100%"}
        alignItems={"center"}
        justifyContent={"space-between"}
        display={{ base: "none", md: "flex" }}
      // pt={{ base: 10, md: 0 }}
      >
        <Text color="chatTextColor" fontSize={"xl"} fontWeight={"medium"}>
          {title}
        </Text>
        <HStack>
          <DefaultButton onClick={openCreateModal}>
            <Text fontSize={"sm"} color={'#fff'}>Novo Contato</Text>
          </DefaultButton>
          <Box height={"40px"}>
            <InputSearch
              placeholder="Pesquisar"
              borderRadius={20}
              name="search"
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </Box>
        </HStack>
      </HStack>

      {/* Mobile Layout */}
      <VStack
        w={"100%"}
        alignItems={"stretch"}
        gap={3}
        display={{ base: "flex", md: "none" }}
      >
        <Text color="chatTextColor" fontSize={"lg"} fontWeight={"medium"} textAlign="center">
          {title}
        </Text>
        <HStack gap={2}>
          <Box flex={1} alignItems={"center"} display="flex">
            <InputSearch
              height="40px"
              placeholder="Pesquisar"
              borderRadius={20}
              name="search"
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </Box>
          <DefaultButton onClick={openCreateModal} size="sm">
            <Text fontSize={"xs"} color={'#fff'}>Novo</Text>
          </DefaultButton>
        </HStack>
      </VStack>
    </>
  );
}
