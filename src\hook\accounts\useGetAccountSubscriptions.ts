import { api } from "@/services/api";
import { GetAccountSubscriptionDto, GetAllAccountsSubscriptionsDto } from "@/utils/types/DTO/accounts-subscriptions.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllAccountsSubscriptions(page: number, accountSecureId?: string) {
  const limit = 3;
  const { data } = await api.get<GetAllAccountsSubscriptionsDto>(`/accounts-subscriptions?page=${page}&limit=${limit}`, {
    params: {
      accountSecureId
    }
  });
  return data;
}

export function useGetAllAccountsSubscriptions(page: number, accountSecureId?: string) {
  return useQuery({
    queryKey: ["accounts-subscriptions", page, accountSecureId],
    queryFn: async () => await getAllAccountsSubscriptions(page, accountSecureId),
  });
}

async function getAccountSubscription(secureId: string) {
  const { data } = await api.get<GetAccountSubscriptionDto>(`/accounts-subscriptions/${secureId}`);
  return data;
}

export function useGetAccountSubscriptions(secureId: string) {
  return useQuery({
    queryKey: ["accounts-subscriptions", secureId],
    queryFn: async () => await getAccountSubscription(secureId),
  });
}
