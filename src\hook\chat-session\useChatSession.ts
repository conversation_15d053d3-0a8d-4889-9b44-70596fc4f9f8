import { api } from "@/services/api";
import {
  ChatSessionDto,
  ChatSessionsDto,
} from "@/utils/types/DTO/chat-sessions.dto";
import { MetaDTO } from "@/utils/types/DTO/meta.dto";
// Import useInfiniteQuery instead of useQuery
import { useInfiniteQuery } from "@tanstack/react-query";

type ApiResponseDTO = {
  meta: MetaDTO;
  data: ChatSessionsDto[];
  archivedCount: number;
};

// Update getSessions to accept pageParam
async function getSessions({
  search = "",
  pageParam = 1, // Default pageParam to 1
  isAIResponder = false,
  showAll = false,
  showOnlyArchived = false,
  showOnlyFinalized = false,
  sortOrder = "desc",
}: {
  search?: string;
  pageParam?: number;
  isAIResponder?: boolean;
  showAll?: boolean;
  showOnlyArchived?: boolean;
  showOnlyFinalized?: boolean;
  sortOrder?: "asc" | "desc";
}) {
  const { data } = await api.get<ApiResponseDTO>(
    `/chat-sessions?search=${search}&page=${pageParam}&limit=20&isAIResponder=${isAIResponder}&showAll=${showAll}&showOnlyArchived=${showOnlyArchived}&showOnlyFinalized=${showOnlyFinalized}&sortOrder=${sortOrder}`
  );

  return data;
}

export default function useChatSession({
  search,
  isAIResponder,
  showAll,
  showOnlyArchived,
  showOnlyFinalized,
  sortOrder,
}: {
  search?: string;
  isAIResponder?: boolean;
  showAll?: boolean;
  showOnlyArchived?: boolean;
  showOnlyFinalized?: boolean;
  sortOrder?: "asc" | "desc";
}) {

  return useInfiniteQuery({
    queryFn: ({ pageParam }) =>
      getSessions({ search, pageParam, isAIResponder, showAll, showOnlyArchived, showOnlyFinalized, sortOrder }),
    queryKey: ["get-all-chat-sessions", search, isAIResponder, showAll, showOnlyArchived, showOnlyFinalized, sortOrder],
    getNextPageParam: (lastPage) => {
      const currentPage = lastPage.meta.currentPage;
      const totalPages = lastPage.meta.totalPages;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
    refetchInterval: 1000 * 30,
  });
}
