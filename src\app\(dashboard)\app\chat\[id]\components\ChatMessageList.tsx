import MessageBubble from "@/components/global/chat/message-bubble";
import { VStack, Flex } from "@chakra-ui/react";

interface Message {
  secureId: string;
  message: string;
  isMine: boolean;
  messageTime: string;
  avatarUrl?: string;
  type?: "text" | "audio" | "file" | "image";
  urlFile?: string;
  attendantName?: string;
  messageCount: number;
  replyTo?: string;
  replyMessage?: string;
}

interface ChatMessageListProps {
  messages: Message[];
  clientName: string;
  onReply: (secureId: string, message: string) => void;
  sessionInfo?: any;
}

export default function ChatMessageList({ messages, clientName, onReply, sessionInfo }: ChatMessageListProps) {
  // Remove duplicatas pelo secureId
  const uniqueMessages = Array.from(
    new Map(messages.map(msg => [msg.secureId, msg])).values()
  );
  return (
    <VStack flex={1} w="100%" justifyContent="flex-end">
      {uniqueMessages.length === 0 ? (
        <Flex justify="center" align="center" h="100%" color={"chatTextColor"}>
          Inicie uma conversa
        </Flex>
      ) : (
        uniqueMessages.map((message) => (
          <MessageBubble
            key={message.secureId}
            secureId={message.secureId}
            message={message.message}
            isMine={message.isMine}
            type={message.type}
            urlFile={message.urlFile}
            clientName={clientName}
            messageTime={message.messageTime}
            avatarUrl={message.avatarUrl}
            name={message.attendantName}
            messageCount={message.messageCount}
            onReply={onReply}
            replyTo={message.replyTo}
            replyMessage={message.replyMessage}
          />
        ))
      )}
    </VStack>
  );
}
