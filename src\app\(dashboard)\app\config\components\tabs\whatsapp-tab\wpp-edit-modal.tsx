import { InputSelect } from "@/components/global/inputs/input-select";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import useListChatBots from "@/hook/chatbots/useListChatbots";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Center, Spinner, VStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

type WppEditModalProps = {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  wppIntegrationSecureId: string;
  selectedChatBotSecureId: string | null;
};

const WppEditModalSchema = yup.object().shape({
  chatBotSecureId: yup.array(yup.string()).optional(),
});

type WppEditModalFormData = yup.InferType<typeof WppEditModalSchema>;

export default function WppEditModal({
  isOpen,
  setIsOpen,
  selectedChatBotSecureId,
  wppIntegrationSecureId,
}: WppEditModalProps) {
  const { data, isLoading } = useListChatBots();
  const [itemList, setItemList] = useState<{ label: string; value: string }[]>(
    []
  );

  useEffect(() => {
    if (data) {
      const list = data.data.map((item) => ({
        label: item.name,
        value: item.secureId,
      }));
      setItemList(list);
    }
  }, [data]);

  const handleEdit = async (data: WppEditModalFormData) => {
    try {
      await api.put(`/whatsapp-integration/${wppIntegrationSecureId}`, {
        chatBotSecureId: data.chatBotSecureId?.[0] || null,
      });

      queryClient.invalidateQueries({
        queryKey: ["whatsapp-integration"],
      });
      setIsOpen(false);
      toaster.success({
        title: "Alterado!",
        description: "Integração com whatsapp foi alterado com sucesso",
      });
    } catch (e) {}
  };

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<WppEditModalFormData>({
    resolver: yupResolver(WppEditModalSchema),
    defaultValues: {
      chatBotSecureId: selectedChatBotSecureId ? [selectedChatBotSecureId] : [],
    },
  });

  return (
    <>
      <BasicModal
        open={isOpen}
        size="lg"
        setOpen={setIsOpen}
        cancelText="Cancelar"
        title="Adicionar ChatBot"
        isSubmitting={false}
        confirmText="Confirmar"
        asForm
        handleConfirm={handleSubmit(handleEdit)}
        placement="center"
        children={
          <>
            <VStack flex={1} gap={5}>
              {isLoading ? (
                <Center width={"100%"} height={"100%"}>
                  <Spinner size="xl" color={"chatPrimaryColor"} />
                </Center>
              ) : (
                <InputSelect
                  {...register("chatBotSecureId")}
                  control={control}
                  label="Selecione o ChatBot"
                  placeholder="Selecione o ChatBot"
                  labelColor={"chatTextColor"}
                  rounded={"2xl"}
                  error={errors.chatBotSecureId as any}
                  itensList={itemList}
                />
              )}
            </VStack>
          </>
        }
      />
    </>
  );
}
