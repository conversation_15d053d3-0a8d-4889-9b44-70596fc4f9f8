"use client";
import { InputIcon } from "@/components/global/inputs/input-icon";
import { <PERSON>, Spinner, VStack, Text, Icon, Flex } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { IoIosSearch } from "react-icons/io";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import ConversationCard from "./conversation-card";
import useChatSession from "@/hook/chat-session/useChatSession";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { LuArchive, LuEllipsisVertical, LuFilter, LuPlus, LuRedo2 } from "react-icons/lu";
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from "@/components/ui/menu";
import { Checkbox } from "@/components/ui/checkbox";
import { queryClient } from "@/services/queryClient";
import { Button } from "@/components/ui/button";
import { useInView } from "react-intersection-observer";
import { Tooltip } from "@/components/ui/tooltip";
import ActionButton from "@/components/global/buttons/action-button";
import StartNewConversationModal from "./new-conversation-modal";

const SearchSchema = yup.object().shape({
  search: yup.string(),
});

type SearchFormData = yup.InferType<typeof SearchSchema>;

type ConversationsProps = {
  activeConversationId?: string;
};

export default function Conversations({
  activeConversationId,
}: ConversationsProps) {
  const [search, setSearch] = useState("");
  const [modalStartSession, setModalStartSession] = useState(false);
  const [checkedAIChat, setCheckedAIChat] = useState(false);
  const [checkedALLChat, setCheckedALLChat] = useState(false);
  const [showArchived, setShowArchived] = useState(false);
  const [showFinalized, setShowFinalized] = useState(false);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const scrollableListRef = useRef<HTMLDivElement>(null);

  const {
    data,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useChatSession({
    search,
    isAIResponder: checkedAIChat,
    showAll: checkedALLChat,
    showOnlyArchived: showArchived,
    showOnlyFinalized: showFinalized,
    sortOrder,
  });

  const { ref: loadMoreRef, inView } = useInView();

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: [
        "get-all-chat-sessions",
        search,
        checkedAIChat,
        checkedALLChat,
        showArchived,
        showFinalized,
        sortOrder,
      ],
    });
  }, [search, checkedAIChat, checkedALLChat, showArchived, showFinalized, sortOrder]);

  const {
    register,
    handleSubmit,
    formState,
    formState: { errors },
  } = useForm<SearchFormData>({
    resolver: yupResolver(SearchSchema),
  });

  const handleSearch = useCallback(async (formData: SearchFormData) => {
    setSearch(formData.search || "");
  }, []);

  const allConversations = data?.pages.flatMap((page) => page.data) ?? [];
  const archivedCount = data?.pages[0]?.archivedCount ?? 0;

  const handleToggleFilters = () => {
    setIsFiltersOpen((prev) => !prev);
  };

  const handleToggleSortOrder = () => {
    setSortOrder((prevOrder) => (prevOrder === "desc" ? "asc" : "desc"));
    if (scrollableListRef.current) {
      scrollableListRef.current.scrollTop = 0; // Rola para o topo
    }
  };

  return (
    <VStack flex={1} gap={2} maxH={"calc(100vh - 140px)"} alignItems={"stretch"}>
      <Flex flexDirection={"column"} w={"100%"} h={"fit-content"}>
        <Flex flexDirection={"row"} justifyContent={"space-between"} mb={4}>
          <Flex flexDirection={"row"} gap={4}>
            {/* <MenuRoot
              closeOnSelect={false}
              positioning={{ placement: "right-start" }}
            >
              <MenuTrigger outline={"none"}>
                <Box
                  display={"flex"}
                  ml={2}
                  p={1}
                  justifyContent={"center"}
                  alignItems={"center"}
                  _hover={{
                    cursor: "pointer",
                    bgColor: "#E0DFE0",
                    rounded: "full",
                  }}
                >
                  <LuEllipsisVertical color="black" />
                </Box>
              </MenuTrigger>
              <MenuContent
                bgColor={"chatCardBackground"}
                outline={"none"}
                shadow={"md"}
              >
                <MenuItem
                  value="viewAISessions"
                  closeOnSelect={false}
                  _hover={{
                    bgColor: "#f2f2f2",
                  }}
                >
                  <VStack gap={2} w="100%" alignItems={"flex-start"}>
                    <Box>
                      <Checkbox
                        name="viewAISessions"
                        checked={checkedAIChat}
                        onCheckedChange={(e) => setCheckedAIChat(!!e.checked)}
                        size={"sm"}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Text color={"chatTextColor"}>
                          Mostrar Conversas com IA
                        </Text>
                      </Checkbox>
                    </Box>
                    <Box>
                      <Checkbox
                        name="viewALLSessions"
                        checked={checkedALLChat}
                        onCheckedChange={(e) => setCheckedALLChat(!!e.checked)}
                        size={"sm"}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Text color={"chatTextColor"}>Ver Todas as Conversas</Text>
                      </Checkbox>
                    </Box>
                    <Box>
                      <Checkbox
                        name="viewFinalizedSessions"
                        checked={showFinalized}
                        onCheckedChange={(e) => setShowFinalized(!!e.checked)}
                        size={"sm"}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Text color={"chatTextColor"}>Ver Conversas Finalizadas</Text>
                      </Checkbox>
                    </Box>
                  </VStack>
                </MenuItem>
              </MenuContent>
            </MenuRoot> */}
            <ActionButton
              icon={<LuFilter />}
              tooltipLabel={isFiltersOpen ? "Fechar Filtros" : "Abrir Filtros"}
              onClick={handleToggleFilters}
              isActive={isFiltersOpen}
            />
            <ActionButton
              icon={<LuRedo2 style={{ transform: "rotate(90deg)" }} />}
              tooltipLabel={
                sortOrder === "desc"
                  ? "Recentes primeiro"
                  : "Antigos primeiro"
              }
              onClick={handleToggleSortOrder}
              transform={sortOrder === "asc" && "rotate(-180deg)"}
            />
          </Flex>
          <ActionButton
            icon={<LuPlus />}
            tooltipLabel="Iniciar chat"
            onClick={() => setModalStartSession(true)}
            bgColor={"chatPrimary"}
            color={"white"}
            hoverBgColor={"transparent"}
            hoverColor={"chatPrimary"}

          />
        </Flex>
        <Box
          overflow="hidden"
          transitionProperty="max-height, padding-top, padding-bottom, opacity, margin-bottom"
          transitionDuration="300ms" // Duração da animação
          transitionTimingFunction="ease-in-out" // Efeito de aceleração
          maxHeight={isFiltersOpen ? "300px" : "0px"} // Altura máxima quando aberto (ajuste conforme necessário)
          opacity={isFiltersOpen ? 1 : 0}
          pb={isFiltersOpen ? 4 : 0} // Padding inferior animado
          bgColor={"chatCardBackground"}
        >
          <VStack gap={2} w="100%" alignItems={"flex-start"}>
            <Box>
              <Checkbox
                name="viewAISessions"
                checked={checkedAIChat}
                onCheckedChange={(e) => setCheckedAIChat(!!e.checked)}
                size={"sm"}
                onClick={(e) => e.stopPropagation()}
              >
                <Text color={"chatTextColor"}>Mostrar Conversas com IA</Text>
              </Checkbox>
            </Box>
            <Box>
              <Checkbox
                name="viewALLSessions"
                checked={checkedALLChat}
                onCheckedChange={(e) => setCheckedALLChat(!!e.checked)}
                size={"sm"}
                onClick={(e) => e.stopPropagation()}
              >
                <Text color={"chatTextColor"}>Ver Todas as Conversas</Text>
              </Checkbox>
            </Box>
            <Box>
              <Checkbox
                name="viewFinalizedSessions"
                checked={showFinalized}
                onCheckedChange={(e) => setShowFinalized(!!e.checked)}
                size={"sm"}
                onClick={(e) => e.stopPropagation()}
              >
                <Text color={"chatTextColor"}>Ver Conversas Finalizadas</Text>
              </Checkbox>
            </Box>
            <Box>
              <Checkbox
                name="viewArchivedSessions"
                checked={showArchived}
                onCheckedChange={(e) => setShowArchived(!!e.checked)}
                size={"sm"}
                onClick={(e) => e.stopPropagation()}
              >
                <Text color={"chatTextColor"}>Ver Conversas Arquivadas</Text>
              </Checkbox>
            </Box>
          </VStack>
        </Box>
        <InputIcon
          placeholder="Buscar"
          height="40px"
          startElement={<IoIosSearch size={20} />}
          rounded={20}
          {...register("search")}
          error={errors.search}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSubmit(handleSearch)();
            }
          }}
        />
      </Flex>
      <VStack
        w="100%"
        gap={4}
        flex={1}
        overflowY="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "4px",
          },
          "&::-webkit-scrollbar-track": {
            width: "6px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#D6D6D6",
            borderRadius: "24px",
          },
        }}
        pb={4}
      >
        {isLoading && !data ? (
          <Spinner />
        ) : (
          <>
            {allConversations.length === 0 ? (
              <Box color={"chatTextColor"}>Nenhuma conversa encontrada</Box>
            ) : (
              <>
                {allConversations.map((conversation) => (
                  <ConversationCard
                    key={conversation.secureId}
                    isArchived={showArchived}
                    messageType={conversation?.chatMessages[0]?.messageType}
                    lastMessageDate={conversation?.chatMessages[0]?.createdAt}
                    whatsappNumber={conversation.whatsapp?.phoneNumber}

                    secureId={conversation.secureId}
                    isSelected={activeConversationId === conversation.secureId}
                    name={conversation.customerName}
                    lastMessage={
                      conversation?.chatMessages[0]?.sendMessage
                        ? conversation.chatMessages[0].sendMessage
                        : conversation?.chatMessages[0]?.receiveMessage ?? ""
                    }
                    isRead={conversation.isRead}
                    messageSource={conversation.source}
                  />
                ))}
                <Box ref={loadMoreRef} h="1px" />
              </>
            )}
          </>
        )}
        {isFetchingNextPage && <Spinner size="sm" mt={2} />}
      </VStack>
      {/* <Button
        mt={1}
        w={"100%"}
        h={8}
        size={"xs"}
        bgColor={"chatPrimary"}
        flexDir={"row"}
        justifyContent={"space-between"}
        alignItems={"center"}
        borderRadius={20}
        _hover={{
          bgColor: "chatCardBackground",
          color: "chatPrimary",
          border: "1px solid",
          borderColor: "chatPrimary",
        }}
        _active={{ transform: "scale(0.95)" }}
        color={"#fff"}
        onClick={() => {
          setShowArchived(!showArchived);
        }}
      >
        <LuArchive />
        <Text fontSize={"xs"} fontWeight={"bold"}>
          Arquivados
        </Text>
        <Text fontSize={"xs"}>{archivedCount}</Text>
      </Button> */}
      <StartNewConversationModal
        open={modalStartSession}
        setOpen={setModalStartSession}
      />
    </VStack>
  );
}
