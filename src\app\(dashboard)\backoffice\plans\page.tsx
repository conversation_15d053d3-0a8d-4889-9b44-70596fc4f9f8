"use client";
import CardPlan from "@/components/cards/card-plan";
import { Button } from "@/components/ui/button";
import useGetPlans from "@/hook/plans/useGetPlans";
import { Flex, Grid, HStack, Text, Spinner } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";
import { useState } from "react";
import ModalCreatePlanBackoffice from "./components/modal-create-plan-backoffice";
import { Checkbox } from "@/components/ui/checkbox";
export default function BackofficePlans() {
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [showOnlyActives, setShowOnlyActives] = useState(true);
  const { data: plans } = useGetPlans(showOnlyActives)

  return (
    <>
      <BackofficePageContainer>
        <Flex direction="column" gap={4} w="100%">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Planos
          </Text>
          <HStack justifyContent="space-between" w="100%" alignItems={"center"}>
            <Text fontSize={"md"} color={"gray.400"}>
              Total de planos: {plans?.meta.totalItems}
            </Text>
            <Button
              size={{base: "xs", lg:"sm"}}
              color={"white"}
              bg={"chatPrimary"}
              borderRadius={"full"}
              colorScheme={"green"}
              _hover={{
                transition: "0.3s",
                bg: "gray.100",
                color: "chatPrimary",
              }}
              onClick={() => setOpenCreateModal(true)}
            >
              Cadastrar Plano
            </Button>
          </HStack>
          <Checkbox
              name="viewAISessions"
              checked={showOnlyActives}
              onCheckedChange={(e) => setShowOnlyActives(!!e.checked)}
              size={"sm"}
              onClick={(e) => e.stopPropagation()}
            >
              <Text color={"chatTextColor"}>
                Somente Ativos
              </Text>
            </Checkbox>
          <Grid
            flex={1}
            templateColumns={{ base: "1fr", sm: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }}
            gap={4}
            overflow="auto"
            maxH="calc(100vh - 200px)"
            css={{
              boxSizing: "border-box",
              paddingRight: "8px", 
              "&::-webkit-scrollbar": { width: "6px" },
              "&::-webkit-scrollbar-track": { background: "transparent" },
              "&::-webkit-scrollbar-thumb": {
                background: "rgba(85, 85, 85, 0.4)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                background: "rgba(102, 102, 102, 0.6)",
              },
            }}
          >
            {plans && plans.data.length > 0 ? (
              plans.data.map((plan) =>
                <CardPlan
                  key={plan.secureId}
                  planSecureId={plan.secureId}
                  planName={plan.name}
                  planSlug={plan.slug}
                  planPrice={plan.price}
                  planDescription={plan.description}
                  planDetails={plan.details}
                  planTrialDays={plan.trialDays}
                  planAttendantsLimit={plan.attendantsLimit}
                  planNumberLimit={plan.whatsappNumberLimit}
                  planChatbotsLimit={plan.chatbotsLimit}
                  planKnowledgeBaseLimit={plan.knowledgeBaseLimit}
                  planIaMessagesLimit={plan.iaMessagesLimit}
                  isActive={plan.isActive}
                />
              )
            ) : (
              <Spinner color="white" />
            )}
          </Grid>
        </Flex>
      </BackofficePageContainer>
      <ModalCreatePlanBackoffice
        openModal={openCreateModal}
        setOpenModal={setOpenCreateModal}
      />
    </>
  );
}