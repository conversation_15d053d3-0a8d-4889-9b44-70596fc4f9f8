import CardAccountTransaction from "@/components/cards/card-account-transaction";
import CardAccountUser from "@/components/cards/card-account-user";
import Pagination from "@/components/global/pagination/pagination";
import { Button } from "@/components/ui/button";
import { useGetAllAccountsUsers } from "@/hook/accounts/useGetAccountUsers";
import { Flex, Heading, HStack, Text } from "@chakra-ui/react";
import { useState } from "react";
import ModalAccountCreateUserBackoffice from "./modal/modal-create-user-account";

type AccountUserTabProps = {
  accountSecureId: string
}

export default function AccountUserTab({accountSecureId}: AccountUserTabProps) {
  const [openCreateUserModal, setOpenCreateUserModal] = useState(false);
  const [page, setPage] = useState(1);
  const { data: accountUsers } = useGetAllAccountsUsers(page, accountSecureId as string);

  return (
    <Flex direction="column" gap={4} w="100%">
      <Heading size="md"><PERSON><PERSON><PERSON><PERSON><PERSON> da <PERSON>ta</Heading>
      <HStack justifyContent={"space-between"}>
        <Text fontSize="sm" color="gray.400">
          Total de usuários: {accountUsers?.meta.totalItems}
        </Text>
        <Button
          size="sm"
          color={"white"}
          bg={"chatPrimary"}
          borderRadius={"full"}
          colorScheme={"green"}
          _hover={{
            transition: "0.3s",
            bg: "gray.100",
            color: "chatPrimary",
          }}
          onClick={() => setOpenCreateUserModal(true)}
        >
          Adicionar Usuário
        </Button>
      </HStack>

      {accountUsers?.data.map((user) => (
        <CardAccountUser
          key={user.secureId}
          accountSecureId={accountSecureId}
          userSecureId={user.secureId}
          name={user.name}
          email={user.email}
          cpf={user.cpf}
          cellPhone={user.cellphone}
          isOwner={user.isOwner}
          hasAllPermissions={user.hasAllPermissions}
          permissions={user.permissions}
        />
      ))}
      {accountUsers &&
        <Pagination
          totalItems={accountUsers.meta.totalItems}
          itemsPerPage={accountUsers.meta.itemsPerPage}
          page={page}
          setPage={setPage}
          alignSelf={"flex-end"}
          color="white"
          hoverColor="backgroundCard"
        />
      }
      <ModalAccountCreateUserBackoffice
        openModal={openCreateUserModal}
        setOpenModal={setOpenCreateUserModal}
        accountSecureId={accountSecureId}
      />
    </Flex>
  )
}