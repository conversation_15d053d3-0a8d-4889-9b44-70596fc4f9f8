import { Box, Flex, Separator, Text } from "@chakra-ui/react";

type CardPlanProps = {
  planSecureId: string;
  planName: string;
  planPrice: string;
  planDescription: string;
  planDetails: string;
  planAttendantsLimit: number;
  planNumberLimit: number;
  planChatbotsLimit: number;
  planKnowledgeBaseLimit: number;
  planIaMessagesLimit: number;
};

export default function CardPlanApp({
  planSecureId,
  planName,
  planPrice,
  planDescription,
  planAttendantsLimit,
  planNumberLimit,
  planChatbotsLimit,
  planKnowledgeBaseLimit,
  planIaMessagesLimit,
}: CardPlanProps) {;
  return (
    <Box
      // w={{base:"full", sm:"49%"}}
      color={"chatTextColor"}
      h={"fit-content"}
      bg={"chatBackground"}
      borderRadius={"2xl"}
      p={4}
    >
      <Flex
        flexDirection={"row"}
        justifyContent={"space-between"}
        alignItems={"flex-start"}
        mb={2}
        gap={6}
      >
        <Flex
          justifyContent={"space-between"}
          alignItems={"center"}
          flexWrap={"wrap"}
        >
            <Text
              mr={4}
              fontSize={{ base: "xl", lg: "2xl" }}
              fontWeight="bold"
              color="chatPrimary"
            >
              {planName}
            </Text>
          <Text
            fontSize={{ base: "xl", lg: "2xl" }}
            fontWeight="bold"
            color="chatPrimary"
          >
            {planPrice}
          </Text>
        </Flex>
      </Flex>

      <Text fontSize={{ base: "sm", lg: "md" }} fontWeight={"600"} mb={2}>
        {planDescription}
      </Text>
      <Separator mb={2} />
      <Text fontSize={{ base: "sm", lg: "md" }} mb={2}>
        Atendentes: <strong>{planAttendantsLimit}</strong>
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} mb={2}>
        Números de WhatsApp: <strong>{planNumberLimit}</strong>
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} mb={2}>
        Chatbots: <strong>{planChatbotsLimit}</strong>
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} mb={2}>
        Base de Conhecimento: <strong>{planKnowledgeBaseLimit}</strong>
      </Text>
      <Text fontSize={{ base: "sm", lg: "md" }} mb={2}>
        Mensagens IA: <strong>{planIaMessagesLimit}</strong>
      </Text>
    </Box>
  );
}
