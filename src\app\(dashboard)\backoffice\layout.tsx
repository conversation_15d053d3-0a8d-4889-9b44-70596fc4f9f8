import { Metadata } from "next";
import BackOfficeLayout from "./components/backoffice-layout";
import { WithSSRAuth } from "@/utils/validator/WithSSRAuth";

type DashLayoutProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "PlyrChat - Backoffice",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default async function AuthLayout({ children }: DashLayoutProps) {
  return <BackOfficeLayout>{children}</BackOfficeLayout>;
}
