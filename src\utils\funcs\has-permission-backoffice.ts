import { useAuthContext } from "@/providers/AuthProvider";
import { UserAuthProps } from "../types/global/UserAuth";

export default function HasBackofficePermission(
  permission: string,
  thisUser?: UserAuthProps
) {
  if (thisUser) {
    const viewPermissions = thisUser?.activeAccount.viewPermissionsSlugs || [];

    if (thisUser?.activeAccount.roleSlug === "MASTER") {
      return true;
    }
    //Praticamente o user master da conta
    if (viewPermissions.includes("app_view")) {
      return true;
    }

    if (viewPermissions.includes(permission)) {
      return true;
    }

    return false;
  }

  const { viewPermissions, user } = useAuthContext();

  if (user?.activeAccount.roleSlug === "MASTER") {
    return true;
  }
  //Praticamente o user master da conta
  if (viewPermissions.includes("backoffice_view")) {
    return true;
  }

  return viewPermissions.includes(permission);
}
