import { Avatar } from "@/components/ui/avatar";
import {
  Box,
  HStack,
  VStack,
  Text,
  Icon,
  Link,
  IconButton,
  Image,
} from "@chakra-ui/react";
import React from "react";
import { IoDocumentOutline, IoDownloadOutline } from "react-icons/io5";
import AudioPlayer from "./audio-player";
import ReactMarkdown from "react-markdown";
import { TbCornerDownRight } from "react-icons/tb";

type MessageBubbleProps = {
  message: string;
  attendantName: string;
  isMine: boolean;
  avatarUrl?: string;
  messageTime: string;
  messageStatus: string;
  messageType?: "text" | "file" | "image" | "audio";
  urlFile?: string | null;
  isSameSender: boolean;
  customerMessageColor: string | null;
  customerMessageBgColor: string | null;
  attendantMessageColor: string | null;
  attendantMessageBgColor: string | null;
  attendantAvatarColor: string | null;
  primaryColor: string | null;
  secureId: string;
  onReply?: (secureId: string, message: string) => void;
  replyTo?: string;
  replyMessage?: string;
};

export default function MessageBubbleInjectChat({
  message,
  isMine,
  avatarUrl,
  messageTime,
  attendantName,
  messageType,
  urlFile,
  isSameSender,
  attendantMessageBgColor,
  attendantMessageColor,
  customerMessageBgColor,
  customerMessageColor,
  attendantAvatarColor,
  primaryColor,
  secureId,
  onReply,
  replyMessage,
  replyTo,
}: MessageBubbleProps) {
  const renderMessageContent = () => {
    switch (messageType) {
      case "audio":
        return urlFile ? <AudioPlayer url={urlFile} /> : null;

      case "image":
        return (
          <VStack align="start" gap={2} w="100%">
            {urlFile && (
              <Box
                maxW="300px"
                maxH="300px"
                overflow="hidden"
                borderRadius="md"
              >
                <Image
                  src={urlFile}
                  onClick={() => window.open(urlFile, "_blank")}
                  cursor="pointer"
                  alt="Image content"
                  objectFit="contain"
                  maxW="100%"
                  maxH="300px"
                  loading="lazy"
                />
              </Box>
            )}
            {message && (
              <ReactMarkdown
                components={{
                  ol: ({ children }) => (
                    <Box as="ol" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  ul: ({ children }) => (
                    <Box as="ul" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  li: ({ children }) => (
                    <Box
                      as="li"
                      display="list-item"
                      listStylePosition={"initial"}
                      my={1}
                      alignItems="center"
                    >
                      {children}
                    </Box>
                  ),
                  p: ({ children }) => (
                    <Text
                      display="inline-flex"
                      flexWrap="wrap"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                  a: ({ href, children }) => (
                    <Link
                      href={href}
                      color="#FD2264"
                      textDecoration="underline"
                      target="_blank"
                      maxWidth="100%"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Link>
                  ),
                  strong: ({ children }) => (
                    <Text
                      as="strong"
                      fontWeight="bold"
                      display="inline"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                  code: ({ children }) => (
                    <Text
                      as="code"
                      bg="gray.100"
                      p="1"
                      rounded="sm"
                      fontSize="sm"
                      fontFamily="monospace"
                      display="inline"
                      wordBreak="break-all"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                }}
                className="markdown-content"
              >
                {message}
              </ReactMarkdown>
            )}
          </VStack>
        );

      case "file":
        return (
          <VStack align="start" gap={2} w="100%">
            {urlFile && (
              <HStack
                p={3}
                bg="gray.50"
                borderRadius="md"
                w="100%"
                gap={3}
                border="1px solid"
                justifyContent={"space-between"}
                alignItems="center"
                borderColor="gray.200"
              >
                <Box color={primaryColor ?? "#FD2264"}>
                  <Icon size="lg">
                    <IoDocumentOutline />
                  </Icon>
                </Box>
                <VStack align="start" gap={0} flex={1}>
                  <Text
                    fontWeight="medium"
                    fontSize="sm"
                    color={attendantMessageColor || "chatTextColor"}
                  >
                    {"Documento"}
                  </Text>
                </VStack>
                <Link href={urlFile} target="_blank" download>
                  <IconButton
                    size="xs"
                    rounded="full"
                    fontWeight="700"
                    bgColor="chatPrimary"
                    color="white"
                    transitionDuration={"0.2s"}
                    _hover={{
                      color: primaryColor ?? "chatPrimary",
                      bgColor: "transparent",
                      borderColor: "chatPrimary",
                    }}
                    _active={{
                      transform: "scale(0.95)",
                    }}
                  >
                    <Icon size="md">
                      <IoDownloadOutline />
                    </Icon>
                  </IconButton>
                </Link>
              </HStack>
            )}
            {message && (
              <ReactMarkdown
                components={{
                  ol: ({ children }) => (
                    <Box as="ol" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  ul: ({ children }) => (
                    <Box as="ul" pl={4} my={2}>
                      {children}
                    </Box>
                  ),
                  li: ({ children }) => (
                    <Box
                      as="li"
                      display="list-item"
                      listStylePosition={"initial"}
                      my={1}
                      alignItems="center"
                    >
                      {children}
                    </Box>
                  ),
                  p: ({ children }) => (
                    <Text
                      display="inline-flex"
                      flexWrap="wrap"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                  a: ({ href, children }) => (
                    <Link
                      href={href}
                      color={primaryColor || "#FD2264"}
                      textDecoration="underline"
                      target="_blank"
                      maxWidth="100%"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Link>
                  ),
                  strong: ({ children }) => (
                    <Text
                      as="strong"
                      fontWeight="bold"
                      display="inline"
                      wordBreak="break-word"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                  code: ({ children }) => (
                    <Text
                      as="code"
                      bg="gray.100"
                      p="1"
                      rounded="sm"
                      fontSize="sm"
                      fontFamily="monospace"
                      display="inline"
                      wordBreak="break-all"
                      overflowWrap="break-word"
                    >
                      {children}
                    </Text>
                  ),
                }}
                className="markdown-content"
              >
                {message}
              </ReactMarkdown>
            )}
          </VStack>
        );

      default: // text
        return (
          <ReactMarkdown
            components={{
              ol: ({ children }) => (
                <Box as="ol" pl={4} my={2}>
                  {children}
                </Box>
              ),
              ul: ({ children }) => (
                <Box as="ul" pl={4} my={2}>
                  {children}
                </Box>
              ),
              li: ({ children }) => (
                <Box
                  as="li"
                  display="list-item"
                  listStylePosition={"initial"}
                  my={1}
                  alignItems="center"
                >
                  {children}
                </Box>
              ),
              p: ({ children }) => (
                <Text
                  display="inline-flex"
                  flexWrap="wrap"
                  wordBreak="break-word"
                  overflowWrap="break-word"
                >
                  {children}
                </Text>
              ),
              a: ({ href, children }) => (
                <Link
                  href={href}
                  color={primaryColor || "#FD2264"}
                  textDecoration="underline"
                  target="_blank"
                  maxWidth="100%"
                  wordBreak="break-word"
                  overflowWrap="break-word"
                >
                  {children}
                </Link>
              ),
              strong: ({ children }) => (
                <Text
                  as="strong"
                  fontWeight="bold"
                  display="inline"
                  wordBreak="break-word"
                  overflowWrap="break-word"
                >
                  {children}
                </Text>
              ),
              code: ({ children }) => (
                <Text
                  as="code"
                  bg="gray.100"
                  p="1"
                  rounded="sm"
                  fontSize="sm"
                  fontFamily="monospace"
                  display="inline"
                  wordBreak="break-all"
                  overflowWrap="break-word"
                >
                  {children}
                </Text>
              ),
            }}
            className="markdown-content"
          >
            {message}
          </ReactMarkdown>
        );
    }
  };

  const handleReply = () => {
    if (onReply) {
      onReply(secureId, message);
    }
  };

  const renderReplyMessage = () => {
    if (!replyTo || !replyMessage) return null;
    return (
      <Box
        p={1.5}
        bg="gray.100"
        borderRadius="md"
        borderLeftWidth={2}
        borderLeftColor="chatPrimary"
        mb={2}
        w={"100%"}
        maxHeight="60px"
        overflow="hidden"
        fontSize="xs"
      >
        <Text fontWeight="medium" color="chatPrimary" fontSize="xs" mb={0.5}>
          Mensagem respondida
        </Text>
        <Text color="gray.600" fontSize="xs">
          {replyMessage}
        </Text>
      </Box>
    );
  };

  return (
    <>
      {isMine ? (
        <HStack
          w={"100%"}
          justifyContent={"flex-end"}
          mb={!isSameSender ? 0 : 5}
        >
          <HStack
            flexDir={"row-reverse"}
            alignItems={"start"}
            gap={5}
            maxWidth={"80%"}
            mr={2}
          >
            <VStack position={"relative"}>
              {!isSameSender && (
                <Box
                  position="absolute"
                  bottom={0}
                  right="-15px"
                  top="30px"
                  width={0}
                  height={0}
                  zIndex={0}
                  borderLeft="20px solid transparent"
                  borderRight="20px solid transparent"
                  borderTop="20px solid"
                  borderTopColor={customerMessageBgColor || "white"}
                />
              )}
              <Box
                bgColor={customerMessageBgColor || "white"}
                p={2.5}
                zIndex={1}
                rounded={10}
                _hover={{
                  "& .reply-button": {
                    opacity: 1,
                  }
                }}
              >
                <VStack align={"start"} justify={"space-between"}>
                  {renderReplyMessage()}
                  <Box color={customerMessageColor || "black"} w="100%">
                    {renderMessageContent()}
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                  </HStack>
                </VStack>
                <IconButton
                  className="reply-button"
                  aria-label="Reply to message"
                  size="sm"
                  position="absolute"
                  left={-10}
                  bottom={0}
                  rounded="full"
                  bgColor={primaryColor || "chatPrimary"}
                  color="white"
                  opacity={0}
                  transition="opacity 0.2s"
                  onClick={handleReply}
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                  zIndex={2}
                >
                  <TbCornerDownRight />
                </IconButton>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      ) : (
        <HStack w={"100%"} justifyContent={"flex-start"}>
          <HStack
            alignItems={"start"}
            gap={5}
            maxWidth={"95%"}
            ml={isSameSender ? 16 : 0}
          >
            {!isSameSender && (
              <Avatar
                size={"lg"}
                name={attendantName}
                src={avatarUrl || undefined}
                bgColor={attendantAvatarColor || ""}
              />
            )}
            <VStack position={"relative"}>
              {!isSameSender && (
                <>
                  <Text
                    fontSize={"xs"}
                    color={"gray.500"}
                    position={"absolute"}
                    top={-5}
                    left={-1}
                    w={"40"}
                    fontWeight={600}
                  >
                    {attendantName}
                  </Text>

                  <Box
                    position="absolute"
                    bottom={0}
                    left="-15px"
                    top="30px"
                    width={0}
                    height={0}
                    zIndex={0}
                    borderLeft="20px solid transparent"
                    borderRight="20px solid transparent"
                    borderTop="20px solid"
                    borderTopColor={attendantMessageBgColor || "white"}
                  />
                </>
              )}
              <Box
                bgColor={attendantMessageBgColor || "white"}
                p={2.5}
                rounded={10}
                zIndex={1}
                _hover={{
                  "& .reply-button": {
                    opacity: 1,
                  }
                }}
              >
                <VStack align={"start"} justify={"space-between"}>
                {renderReplyMessage()}
                  <Box color={attendantMessageColor || "black"} w="100%">
                    {renderMessageContent()}
                  </Box>
                  <HStack justifyContent={"flex-end"} gap={0.5} w={"100%"}>
                    <Text color="chatTextColor" fontSize={"xs"}>
                      {messageTime}
                    </Text>
                  </HStack>
                </VStack>
                <IconButton
                  className="reply-button"
                  aria-label="Reply to message"
                  size="xs"
                  position="absolute"
                  bottom={0}
                  right={-10}
                  rounded="full"
                  bgColor={primaryColor || "chatPrimary"}
                  color="white"
                  opacity={0}
                  transition="opacity 0.2s"
                  onClick={handleReply}
                  _hover={{
                    transform: "scale(1.1)",
                  }}
                  zIndex={10}
                >
                  <TbCornerDownRight />
                </IconButton>
              </Box>
            </VStack>
          </HStack>
        </HStack>
      )}
    </>
  );
}
