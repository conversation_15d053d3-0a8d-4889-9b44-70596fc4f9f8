import { api } from "@/services/api";
import { ChatMessagesHistoryDto } from "@/utils/types/DTO/chat-messages-history.dto";
import { useQuery } from "@tanstack/react-query";

async function findHistory(secureId: string) {
  const { data } = await api.get<ChatMessagesHistoryDto[]>(
    `public/chat-sessions/messages/${secureId}`
  );

  return data;
}

export function useChatHistory(secureId: string) {
  return useQuery({
    queryKey: ["chat-history", secureId],
    queryFn: async () => await findHistory(secureId),
  });
}
