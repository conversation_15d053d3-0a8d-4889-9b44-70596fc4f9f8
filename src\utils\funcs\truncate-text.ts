/**
 * Trunca o texto para o comprimento especificado e adiciona reticências se necessário
 * @param text A string a ser truncada
 * @param maxLength Comprimento máximo antes da truncagem
 * @returns String truncada com reticências ou string original
 */
export default function truncateText(text: string, maxLength: number): string {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};
