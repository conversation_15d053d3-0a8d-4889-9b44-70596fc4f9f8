import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import BasicModal from "@/components/global/modal/basic-modal";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Flex, HStack, Text } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { InputPassword } from "@/components/global/inputs/input-password";
import { Input } from "@/components/global/inputs/input";
import { Button } from "@/components/ui/button";
import { useAuthContext } from "@/providers/AuthProvider";

const updateProfileSchema = yup.object().shape({
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  cpf: yup.string().required("CPF é obrigatório"),
  cellPhone: yup.string().nullable(),
  password: yup
    .string()
    .test(
      "is-valid-password",
      "A senha deve ter pelo menos 8 caracteres",
      (value) => {
        if (!value) return true;
        return value.length >= 8;
      }
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem coincidir"),
});

type UpdateProfileFormData = yup.InferType<typeof updateProfileSchema>;

type ProfileUser = {
  user: {
    secureId: string;
    name: string;
    email: string;
    cpf: string;
    cellPhone: string | null;
  };
};

export default function AppProfileTab({ user }: ProfileUser) {
  const [phoneMask, setPhoneMask] = useState("+99 (99) 99999-9999[9]");
  const { signOut } = useAuthContext();

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<UpdateProfileFormData>({
    resolver: yupResolver(updateProfileSchema),
    defaultValues: {
      name: "",
      email: "",
      cpf: "",
      cellPhone: "",
    },
  });

  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (user) {
      setValue("name", user.name);
      setValue("email", user.email);
      setValue("cpf", user.cpf);
      setValue("cellPhone", user.cellPhone);
    }
  }, [user]);

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);

  const updateProfile = useMutation({
    mutationFn: async (data: UpdateProfileFormData) => {
      const { password, ...rest } = data;
      await api.put(`/profile`, {
        password: password ? password : null,
        ...rest,
      });
      if (user.email !== data.email) {
        signOut();
        return;
      }
      if (password) {
        signOut();
        return;
      }
      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Perfil atualizado com Sucesso.",
        title: "Perfil Atualizado!",
        type: "success",
      });
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: UpdateProfileFormData) => {
    try {
      updateProfile.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <Flex
      as={"form"}
      onSubmit={handleSubmit(onSubmit)}
      h={"100%"}
      bgColor={"chatCardBackground"}
      color={"chatTextColor"}
      p={"5"}
      rounded={"2xl"}
      border={"none"}
    >
      <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
        <Text
          alignSelf={"flex-start"}
          color="chatTextColor"
          fontSize={"xl"}
          fontWeight={"medium"}
          mb={4}
        >
          Minha conta
        </Text>
        <>
          <Input
            label="Nome"
            placeholder="Digite seu nome"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("name")}
            error={errors.name}
          />
          <Input
            label="Email"
            placeholder="Digite seu email"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("email")}
            error={errors.email}
          />
          <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
            <InputPassword
              label="Senha"
              height="80px"
              placeholder="Digite sua senha"
              size={"md"}
              borderRadius={20}
              {...register("password")}
              error={errors.password}
            />
            <InputPassword
              label="Confirmação da Senha"
              height="80px"
              placeholder="Digite sua senha novamente"
              size={"md"}
              borderRadius={20}
              {...register("confirmPassword")}
              error={errors.confirmPassword}
            />
          </HStack>
          <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
            <InputMaskIcon
              label="CPF"
              mask={"999.999.999-99"}
              fieldHeight="80px"
              borderRadius={20}
              placeholder="Digite seu CPF"
              size={"md"}
              {...register("cpf")}
              error={errors.cpf}
            />
            <InputMaskIcon
              label="Celular"
              mask={phoneMask}
              fieldHeight="80px"
              borderRadius={20}
              placeholder="Digite seu número"
              size={"md"}
              {...register("cellPhone")}
              error={errors.cellPhone}
            />
          </HStack>
        </>
        <Button
          type={"submit"}
          size={"md"}
          alignSelf={"flex-end"}
          fontWeight="700"
          bgColor="chatPrimary"
          color="white"
          transitionDuration={"0.2s"}
          _hover={{
            color: "chatPrimary",
            bgColor: "transparent",
            borderColor: "chatPrimary",
          }}
          loading={isSubmitting}
        >
          Atualizar
        </Button>
      </Flex>
    </Flex>
  );
}
