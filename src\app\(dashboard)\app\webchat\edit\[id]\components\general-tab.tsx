import { Input } from "@/components/global/inputs/input";
import { InputTextArea } from "@/components/global/inputs/input-text-area";
import {
  FileUploadDropzone,
  FileUploadList,
  FileUploadRoot,
} from "@/components/ui/file-upload";
import {
  Box,
  Button,
  FileUploadFileAcceptDetails,
  Flex,
  Grid,
  GridItem,
  HStack,
  Text,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import { GetOneChatDtoInput } from "@/utils/types/DTO/chats.dto";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect, useState } from "react";
import { toaster } from "@/components/ui/toaster";
import { useMutation } from "@tanstack/react-query";
import { InputUpload } from "@/components/global/inputs/input-file";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import useListChatBots from "@/hook/chatbots/useListChatbots";
import { InputSelect } from "@/components/global/inputs/input-select";
import { LuTrash2 } from "react-icons/lu";
import BasicModal from "@/components/global/modal/basic-modal";
import { useRouter } from "next/navigation";
import HeaderTab from "@/components/tabs/header";

const GeneralTabSchema = yup.object().shape({
  name: yup.string().required("Nome do WebChat é obrigatório"),
  description: yup.string().required("Descrição é obrigatória"),
  welcomeMessage: yup
    .string()
    .required("Mensagem de boas-vindas é obrigatória"),
  imageUpload: yup.mixed(),
  chatBotSecureId: yup.array(yup.string()).optional(),
});

type GeneralTabFormData = yup.InferType<typeof GeneralTabSchema>;

type GeneralTabProps = {
  chatData: GetOneChatDtoInput;
};

export default function GeneralTab({ chatData }: GeneralTabProps) {
  const { data, isLoading } = useListChatBots();
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const [itemList, setItemList] = useState<{ label: string; value: string }[]>(
    []
  );
  const route = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GeneralTabFormData>({
    resolver: yupResolver(GeneralTabSchema),
    defaultValues: {
      name: chatData.name,
      description: chatData.description || "",
      welcomeMessage: chatData.welcomeMessage || "",
      chatBotSecureId: chatData?.chatbot?.secureId
        ? [chatData.chatbot.secureId]
        : [],
    },
  });

  useEffect(() => {
    if (data?.data && data.data.length > 0) {
      const list = data.data.map((chatBot) => ({
        label: chatBot.name,
        value: chatBot.secureId,
      }));
      setItemList(list);
    }
  }, [data]);

  const handleSave = useMutation({
    mutationFn: async (data: GeneralTabFormData) => {
      const formData = new FormData();
      formData.append("name", data.name);
      formData.append("description", data.description);
      formData.append("welcomeMessage", data.welcomeMessage);
      if (data.imageUpload) {
        formData.append("file", data.imageUpload);
      }
      formData.append("chatBotSecureId", data.chatBotSecureId?.[0] ?? "");

      await api.put(`/chats/${chatData.secureId}`, formData);
    },
    onSuccess: () => {
      toaster.success({
        title: "Sucesso",
        description: "Alterações salvas com sucesso",
      });
      queryClient.invalidateQueries({
        queryKey: ["findChat", chatData.secureId],
      });
    },
    onError: () => { },
  });

  const handleUpdate = async (data: GeneralTabFormData) => {
    try {
      await handleSave.mutateAsync(data);
    } catch (error) { }
  };

  const handleDeleteWebchat = async () => {
    try {
      await api.delete(`/chats/${chatData.secureId}`);
      toaster.success({
        title: "Sucesso",
        description: "WebChat deletado com sucesso",
      });
      setOpenModalDelete(false);
      route.push("/app/config");
      queryClient.invalidateQueries({
        queryKey: ["chats"],
      });
    } catch (error) { }
  };

  return (
    <>
      <Flex
        height={"100%"}
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
      >
        <VStack
          flex={1}
          alignItems={"flex-start"}
          as={"form"}
          onSubmit={handleSubmit(handleUpdate)}
        >
          <HeaderTab
            buttonTitle="Salvar Alterações"
            title="Geral"
            hrefBack="/app/config#webchats"
            isSubmit={isSubmitting}
            newButton={
              <Button
                w={"100%"}
                borderRadius={20}
                size={"md"}
                fontWeight="700"
                bgColor="gray.300"
                color="black"
                // loading={isSubmit}
                onClick={() => setOpenModalDelete(true)}
                transitionDuration={"0.2s"}
                _hover={{
                  color: "chatPrimary",
                  bgColor: "transparent",
                  borderColor: "chatPrimary",
                }}
                _active={{
                  transform: "scale(0.95)",
                }}
              >
                <LuTrash2 />
              </Button>
            }
          />
          <HStack
            w={"100%"}
            h={"100%"}
            justifyContent={"space-between"}
            mt={4}
            alignItems={"flex-start"}
            flexWrap={"wrap"}
          >
            <VStack flex={1} gap={5} h={"100%"} alignItems={"flex-start"}>
              <Input
                {...register("name")}
                label="Nome do WebChat"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                color={"#84767A"}
                error={errors.name}
              />
              <Input
                {...register("description")}
                label="Descrição"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                color={"#84767A"}
                error={errors.description}
              />
              <InputTextArea
                {...register("welcomeMessage")}
                height={"150px"}
                label="Mensagem de boas-vindas"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.welcomeMessage}
              />
              <InputSelect
                {...register("chatBotSecureId")}
                control={control}
                label="Selecione o ChatBot"
                placeholder="Selecione o ChatBot"
                labelColor={"chatTextColor"}
                rounded={"2xl"}
                error={errors.chatBotSecureId as any}
                itensList={itemList}
              />
            </VStack>
            <VStack flex={1} gap={5} px={5}>
              <Text color={"chatTextColor"}>
                Faça upload da logo que será exibida no botão. A imagem deve ter
                exatamente 44x44 pixels com o fundo transparente.
              </Text>
              <InputUpload
                watch={watch}
                {...register("imageUpload")}
                control={control}
                defaultImage={chatData.upload.urlCdn}
              />
            </VStack>
          </HStack>
        </VStack>
      </Flex>
      <BasicModal
        open={openModalDelete}
        size="md"
        setOpen={setOpenModalDelete}
        cancelText="Cancelar"
        handleDelete={handleDeleteWebchat}
        deleteText="Apagar"
        placement="center"
        children={
          <VStack gap={5} m={5}>
            <Text fontSize={"xl"} fontWeight={"bold"} color="red.500">
              ATENÇÃO
            </Text>
            <Text fontSize={"md"} fontWeight={"bold"}>
              Tem certeza que deseja apagar este WebChat?
            </Text>
          </VStack>
        }
      />
    </>
  );
}
